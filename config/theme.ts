import { theme, type ThemeConfig } from 'antd';

// 共享的基础配置
const sharedConfig: Partial<ThemeConfig> = {
  cssVar: true,        // 启用CSS变量
  hashed: false,       // 关闭哈希
  token: {
    // 主色彩
    colorPrimary: '#2464F1',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    
    // 字体配置
    fontFamily: `-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif`,
    fontSize: 14,
    fontSizeHeading1: 38,
    fontSizeHeading2: 30,
    fontSizeHeading3: 24,
    fontSizeHeading4: 20,
    fontSizeHeading5: 16,
    
    // 圆角配置
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    borderRadiusXS: 2,
    
    // 间距配置
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    paddingXXS: 4,
    
    // 动画配置
    motionDurationFast: '0.1s',
    motionDurationMid: '0.2s',
    motionDurationSlow: '0.3s',
    
    // 控件高度
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,
  },
  
  components: {
    // Button 组件配置
    Button: {
      borderRadius: 6,
      controlHeight: 32,
      fontWeight: 500,
    },
    
    // Table 组件配置
    Table: {
      borderRadius: 6,
      headerBg: 'rgba(0, 0, 0, 0.02)',
      headerSortActiveBg: 'rgba(0, 0, 0, 0.04)',
      headerSortHoverBg: 'rgba(0, 0, 0, 0.03)',
    },
    
    // Card 组件配置
    Card: {
      borderRadius: 8,
      boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    },
    
    // Modal 组件配置
    Modal: {
      borderRadius: 8,
    },
    
    // Input 组件配置
    Input: {
      borderRadius: 6,
      controlHeight: 32,
    },
    
    // Select 组件配置
    Select: {
      borderRadius: 6,
      controlHeight: 32,
    },
    
    // Menu 组件配置
    Menu: {
      borderRadius: 6,
      itemBorderRadius: 6,
    },
    
    // Tabs 组件配置
    Tabs: {
      borderRadius: 6,
    },
    
    // Switch 组件配置
    Switch: {
      borderRadius: 100,
    },
    
    // Layout 组件配置
    Layout: {
      headerHeight: 64,
      siderWidth: 240,
    },
  },
};

// 亮色主题配置
export const lightConfig: ThemeConfig = {
  ...sharedConfig,
  algorithm: theme.defaultAlgorithm,
  token: {
    ...sharedConfig.token,
    // 亮色主题特定颜色
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f5f5f5',
    colorBgSpotlight: '#ffffff',
    colorBgMask: 'rgba(0, 0, 0, 0.45)',
    
    colorBorder: '#d9d9d9',
    colorBorderSecondary: '#f0f0f0',
    
    colorFill: 'rgba(0, 0, 0, 0.15)',
    colorFillSecondary: 'rgba(0, 0, 0, 0.06)',
    colorFillTertiary: 'rgba(0, 0, 0, 0.04)',
    colorFillQuaternary: 'rgba(0, 0, 0, 0.02)',
    
    colorText: 'rgba(0, 0, 0, 0.88)',
    colorTextSecondary: 'rgba(0, 0, 0, 0.65)',
    colorTextTertiary: 'rgba(0, 0, 0, 0.45)',
    colorTextQuaternary: 'rgba(0, 0, 0, 0.25)',
    
    colorSplit: 'rgba(5, 5, 5, 0.06)',
  },
  
  components: {
    ...sharedConfig.components,
    
    Table: {
      ...sharedConfig.components?.Table,
      headerBg: '#fafafa',
      headerSortActiveBg: '#f0f0f0',
      headerSortHoverBg: '#f5f5f5',
    },
    
    Layout: {
      ...sharedConfig.components?.Layout,
      bodyBg: '#f5f5f5',
      headerBg: '#ffffff',
      siderBg: '#ffffff',
      triggerBg: '#ffffff',
    },
  },
};

// 暗色主题配置
export const darkConfig: ThemeConfig = {
  ...sharedConfig,
  algorithm: theme.darkAlgorithm,
  token: {
    ...sharedConfig.token,
    // 暗色主题特定颜色
    colorBgContainer: '#141414',
    colorBgElevated: '#1f1f1f',
    colorBgLayout: '#000000',
    colorBgSpotlight: '#424242',
    colorBgMask: 'rgba(0, 0, 0, 0.45)',
    
    colorBorder: '#424242',
    colorBorderSecondary: '#303030',
    
    colorFill: 'rgba(255, 255, 255, 0.18)',
    colorFillSecondary: 'rgba(255, 255, 255, 0.12)',
    colorFillTertiary: 'rgba(255, 255, 255, 0.08)',
    colorFillQuaternary: 'rgba(255, 255, 255, 0.04)',
    
    colorText: 'rgba(255, 255, 255, 0.85)',
    colorTextSecondary: 'rgba(255, 255, 255, 0.65)',
    colorTextTertiary: 'rgba(255, 255, 255, 0.45)',
    colorTextQuaternary: 'rgba(255, 255, 255, 0.25)',
    
    colorSplit: 'rgba(253, 253, 253, 0.12)',
  },
  
  components: {
    ...sharedConfig.components,
    
    Table: {
      ...sharedConfig.components?.Table,
      headerBg: '#1f1f1f',
      headerSortActiveBg: '#262626',
      headerSortHoverBg: '#2f2f2f',
    },
    
    Layout: {
      ...sharedConfig.components?.Layout,
      bodyBg: '#000000',
      headerBg: '#141414',
      siderBg: '#141414',
      triggerBg: '#141414',
    },
  },
};

// 根据主题模式获取配置的辅助函数
export const getThemeConfig = (isDark: boolean): ThemeConfig => {
  return isDark ? darkConfig : lightConfig;
};

// CSS 变量映射（用于非 Ant Design 组件）
export const getCSSVariables = (isDark: boolean) => {
  const config = getThemeConfig(isDark);
  const { token } = config;
  
  return {
    // 主题基础变量
    '--theme-primary': token?.colorPrimary,
    '--theme-success': token?.colorSuccess,
    '--theme-warning': token?.colorWarning,
    '--theme-error': token?.colorError,
    '--theme-info': token?.colorInfo,
    
    // 背景色变量
    '--theme-bg-primary': token?.colorBgContainer,
    '--theme-bg-secondary': token?.colorBgElevated,
    '--theme-bg-tertiary': token?.colorBgLayout,
    '--theme-bg-spotlight': token?.colorBgSpotlight,
    '--theme-bg-mask': token?.colorBgMask,
    
    // 边框变量
    '--theme-border-color': token?.colorBorder,
    '--theme-border-color-split': token?.colorBorderSecondary,
    
    // 文字色变量
    '--theme-text-primary': token?.colorText,
    '--theme-text-secondary': token?.colorTextSecondary,
    '--theme-text-tertiary': token?.colorTextTertiary,
    '--theme-text-quaternary': token?.colorTextQuaternary,
    
    // 填充色变量
    '--theme-fill-primary': token?.colorFill,
    '--theme-fill-secondary': token?.colorFillSecondary,
    '--theme-fill-tertiary': token?.colorFillTertiary,
    '--theme-fill-quaternary': token?.colorFillQuaternary,
    
    // 阴影变量
    '--theme-shadow-1': isDark 
      ? '0 1px 2px 0 rgba(255, 255, 255, 0.03), 0 1px 6px -1px rgba(255, 255, 255, 0.02), 0 2px 4px 0 rgba(255, 255, 255, 0.02)'
      : '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
    '--theme-shadow-2': isDark
      ? '0 3px 6px -4px rgba(255, 255, 255, 0.12), 0 6px 16px 0 rgba(255, 255, 255, 0.08), 0 9px 28px 8px rgba(255, 255, 255, 0.05)'
      : '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
    
    // 圆角变量
    '--theme-border-radius': token?.borderRadius + 'px',
    '--theme-border-radius-lg': token?.borderRadiusLG + 'px',
    '--theme-border-radius-sm': token?.borderRadiusSM + 'px',
    
    // 间距变量
    '--theme-padding': token?.padding + 'px',
    '--theme-padding-lg': token?.paddingLG + 'px',
    '--theme-padding-sm': token?.paddingSM + 'px',
    '--theme-padding-xs': token?.paddingXS + 'px',
  };
};