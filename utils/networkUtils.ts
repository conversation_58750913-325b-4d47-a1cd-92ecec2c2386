import { getEnvConfig } from './envConfig';

/**
 * 网络连接检测和错误处理工具
 */

// 网络状态检查
export const checkNetworkConnection = async (): Promise<boolean> => {
  try {
    // 尝试访问一个快速响应的端点
    const response = await fetch('/api/health', { 
      method: 'GET',
      timeout: 5000 
    } as any);
    return response.ok;
  } catch (error) {
    console.warn('网络连接检查失败:', error);
    return false;
  }
};

// 后端服务连通性检查
export const checkBackendConnection = async (): Promise<{
  connected: boolean;
  error?: string;
  details?: any;
}> => {
  const envConfig = getEnvConfig();
  
  try {
    const response = await fetch(`${envConfig.api.baseUrl}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // 使用较短的超时时间快速检测
      signal: AbortSignal.timeout(5000),
    });

    if (response.ok) {
      return { connected: true };
    } else {
      return {
        connected: false,
        error: `后端响应错误: ${response.status} ${response.statusText}`,
        details: { status: response.status, statusText: response.statusText }
      };
    }
  } catch (error: any) {
    let errorMessage = '后端连接失败';
    
    if (error.name === 'AbortError') {
      errorMessage = '后端连接超时';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = '后端服务未启动或端口不正确';
    } else if (error.message?.includes('fetch')) {
      errorMessage = '网络请求失败，请检查网络连接';
    }

    return {
      connected: false,
      error: errorMessage,
      details: error
    };
  }
};

// 诊断网络问题
export const diagnoseNetworkIssues = async (): Promise<{
  networkOk: boolean;
  backendOk: boolean;
  suggestions: string[];
}> => {
  const suggestions: string[] = [];
  
  // 检查基础网络连接
  const networkOk = await checkNetworkConnection();
  if (!networkOk) {
    suggestions.push('检查网络连接是否正常');
    suggestions.push('尝试刷新页面');
  }

  // 检查后端连接
  const backendCheck = await checkBackendConnection();
  const backendOk = backendCheck.connected;
  
  if (!backendOk) {
    const envConfig = getEnvConfig();
    suggestions.push(`确保后端服务已启动且运行在 ${envConfig.api.baseUrl}`);
    
    if (backendCheck.error?.includes('超时')) {
      suggestions.push('后端响应超时，检查服务器性能或网络延迟');
    } else if (backendCheck.error?.includes('端口')) {
      suggestions.push(`检查后端是否运行在端口 8081 上`);
      suggestions.push('确认环境变量 VITE_API_BASE_URL 配置正确');
    } else if (backendCheck.error?.includes('CORS')) {
      suggestions.push('检查后端 CORS 配置是否允许前端域名访问');
    }
  }

  return {
    networkOk,
    backendOk,
    suggestions
  };
};

// 重试机制工具
export const retryWithExponentialBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (i === maxRetries) {
        throw error;
      }
      
      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, i);
      console.warn(`请求失败，${delay}ms 后重试... (${i + 1}/${maxRetries + 1})`, error);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

// 获取友好的错误消息
export const getFriendlyErrorMessage = (error: any): string => {
  if (!error) return '未知错误';
  
  // 网络相关错误
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return '网络连接失败，请检查网络连接或联系管理员';
  }
  
  // 超时错误
  if (error.code === 'TIMEOUT' || error.message?.includes('timeout')) {
    return '请求超时，请检查网络连接或稍后重试';
  }
  
  // 后端连接错误
  if (error.code === 'ECONNREFUSED') {
    return '无法连接到服务器，请确认服务器已启动';
  }
  
  // HTTP状态码错误
  if (error.response?.status) {
    const status = error.response.status;
    switch (status) {
      case 400:
        return '请求参数错误';
      case 401:
        return '登录已过期，请重新登录';
      case 403:
        return '权限不足，无法访问';
      case 404:
        return '请求的资源不存在';
      case 500:
        return '服务器内部错误，请稍后重试';
      case 502:
        return '网关错误，请检查服务器状态';
      case 503:
        return '服务暂时不可用，请稍后重试';
      default:
        return `服务器错误 (${status})，请稍后重试`;
    }
  }
  
  // 返回原始错误消息或默认消息
  return error.message || error.toString() || '操作失败，请稍后重试';
}; 