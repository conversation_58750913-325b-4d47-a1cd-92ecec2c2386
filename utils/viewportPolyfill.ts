/**
 * 动态视口单位 (dvh) 的轻量级 Polyfill
 * 仅在不支持 dvh 的浏览器中启用
 */

/**
 * 检测浏览器是否支持动态视口单位
 */
const supportsDynamicViewport = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  try {
    // 创建测试元素检测 dvh 支持
    const testEl = document.createElement('div');
    testEl.style.height = '100dvh';
    return testEl.style.height === '100dvh';
  } catch {
    return false;
  }
};

/**
 * 设置CSS自定义属性作为dvh的回退
 */
const setViewportFallback = (): void => {
  if (typeof window === 'undefined') return;

  const updateHeight = () => {
    // 获取真实的视口高度（排除浏览器UI）
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--dvh-fallback', `${vh}px`);
  };

  // 初始设置
  updateHeight();

  // 监听窗口变化
  let resizeTimer: number;
  const handleResize = () => {
    clearTimeout(resizeTimer);
    resizeTimer = window.setTimeout(updateHeight, 100);
  };

  window.addEventListener('resize', handleResize);
  window.addEventListener('orientationchange', () => {
    // 延迟执行，等待浏览器UI调整
    setTimeout(updateHeight, 200);
  });

  // 监听视觉视口变化（现代浏览器）
  if ('visualViewport' in window && window.visualViewport) {
    window.visualViewport.addEventListener('resize', updateHeight);
  }
};

/**
 * 初始化动态视口单位支持
 */
export const initViewportPolyfill = (): void => {
  // 只在不支持 dvh 的浏览器中启用回退方案
  if (!supportsDynamicViewport()) {
    setViewportFallback();

    // 添加CSS类标识需要使用回退方案
    document.documentElement.classList.add('no-dvh-support');
  }
};
