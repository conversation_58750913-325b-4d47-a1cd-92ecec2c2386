/**
 * V2 Admin JWT工具类
 * 用于处理JWT token的解析、验证和过期检查
 */

interface JWTPayload {
  user_id: number;
  username: string;
  role: string;
  iss: string;
  exp: number;
  nbf: number;
  iat: number;
}

class JWTUtils {
  /**
   * 解析JWT token
   * @param token JWT token字符串
   * @returns 解析后的payload或null
   */
  parseToken(token: string): JWTPayload | null {
    try {
      if (!token || !token.includes('.')) {
        return null;
      }

      // JWT格式：header.payload.signature
      const parts = token.split('.');
      if (parts.length !== 3) {
        return null;
      }

      // 解码payload部分（Base64URL）
      const payload = parts[1];
      const decodedPayload = this.base64UrlDecode(payload);
      
      return JSON.parse(decodedPayload) as JWTPayload;
    } catch (error) {
      console.error('解析JWT token失败:', error);
      return null;
    }
  }

  /**
   * 检查token是否过期
   * @param token JWT token字符串
   * @returns true表示已过期，false表示未过期
   */
  isTokenExpired(token: string): boolean {
    try {
      const payload = this.parseToken(token);
      if (!payload || !payload.exp) {
        return true;
      }

      // JWT的exp是秒级时间戳，JavaScript的Date.now()是毫秒级
      const currentTime = Math.floor(Date.now() / 1000);
      return payload.exp < currentTime;
    } catch (error) {
      console.error('检查token过期状态失败:', error);
      return true;
    }
  }

  /**
   * 获取token的剩余有效时间（秒）
   * @param token JWT token字符串
   * @returns 剩余秒数，如果已过期或无效则返回0
   */
  getTokenRemainingTime(token: string): number {
    try {
      const payload = this.parseToken(token);
      if (!payload || !payload.exp) {
        return 0;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const remainingTime = payload.exp - currentTime;
      return Math.max(0, remainingTime);
    } catch (error) {
      console.error('获取token剩余时间失败:', error);
      return 0;
    }
  }

  /**
   * Base64URL解码
   * @param str Base64URL编码的字符串
   * @returns 解码后的字符串
   */
  private base64UrlDecode(str: string): string {
    // Base64URL转Base64：替换字符并添加padding
    let base64 = str.replace(/-/g, '+').replace(/_/g, '/');
    
    // 添加必要的padding
    const padding = base64.length % 4;
    if (padding) {
      base64 += '='.repeat(4 - padding);
    }

    // 解码Base64
    return decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
  }

  /**
   * 保存token到localStorage
   * @param token JWT token字符串
   */
  saveToken(token: string): void {
    localStorage.setItem('token', token);
  }

  /**
   * 从localStorage获取token
   * @returns token字符串或null
   */
  getToken(): string | null {
    return localStorage.getItem('token');
  }

  /**
   * 清除localStorage中的token
   */
  clearToken(): void {
    localStorage.removeItem('token');
  }

  /**
   * 检查当前存储的token是否有效
   * @returns boolean
   */
  isCurrentTokenValid(): boolean {
    const token = this.getToken();
    if (!token) {
      return false;
    }
    return !this.isTokenExpired(token);
  }
}

// 创建单例实例
const jwtUtils = new JWTUtils();

export default jwtUtils;
