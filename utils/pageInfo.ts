/**
 * 页面信息工具函数
 * 根据路径获取页面的标题、图标等信息
 * 优先从路由配置中获取，fallback 到静态配置
 */

import React from 'react';
import { routeConfig } from '../config/routes';
import type { RouteConfig } from '../config/routes';
import {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  TeamOutlined,
  SafetyOutlined,
  BellOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  HomeOutlined,
  AppstoreOutlined,
  TableOutlined,
  FormOutlined,
  FileOutlined,
  PieChartOutlined,
  ShoppingCartOutlined,
  MessageOutlined,
  CalendarOutlined,
  CameraOutlined,
  CloudOutlined,
  GlobalOutlined,
  MailOutlined,
  PhoneOutlined,
  PrinterOutlined,
  ScanOutlined,
  WifiOutlined,
  ApiOutlined,
  BugOutlined,
  CodeOutlined,
  DeploymentUnitOutlined,
  ExperimentOutlined,
  HddOutlined,
  MonitorOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  BookOutlined,
} from '@ant-design/icons';

export interface PageInfo {
  title: string;
  icon?: string;
  description?: string;
  keywords?: string[];
  breadcrumb?: string[];
}

/**
 * 从路由配置中获取页面信息
 */
const getPageInfoFromRoutes = (path: string): PageInfo | null => {
  // 查找完全匹配的路由
  const route = routeConfig.find(route => route.path === path);

  if (route?.meta) {
    // 从 React 组件中提取图标名称
    let iconName = 'QuestionCircleOutlined';
    if (route.meta.icon && typeof route.meta.icon === 'object') {
      // 从 React 元素中提取组件名称
      iconName = route.meta.icon.type?.name || route.meta.icon.type?.displayName || 'QuestionCircleOutlined';
    }

    return {
      title: route.meta.title,
      icon: iconName, // 使用字符串名称
      description: route.meta.description,
      keywords: route.meta.keywords || [],
      breadcrumb: [route.meta.title]
    };
  }

  return null;
};

/**
 * 图标名称到组件的映射
 */
const ICON_MAP: Record<string, React.ComponentType> = {
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  BarChartOutlined,
  TeamOutlined,
  SafetyOutlined,
  BellOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  HomeOutlined,
  AppstoreOutlined,
  TableOutlined,
  FormOutlined,
  FileOutlined,
  PieChartOutlined,
  ShoppingCartOutlined,
  MessageOutlined,
  CalendarOutlined,
  CameraOutlined,
  CloudOutlined,
  GlobalOutlined,
  MailOutlined,
  PhoneOutlined,
  PrinterOutlined,
  ScanOutlined,
  WifiOutlined,
  ApiOutlined,
  BugOutlined,
  CodeOutlined,
  DeploymentUnitOutlined,
  ExperimentOutlined,
  HddOutlined,
  MonitorOutlined,
  RocketOutlined,
  ThunderboltOutlined,
  RobotOutlined,
  BookOutlined,
};

/**
 * 根据图标名称渲染图标组件
 */
export const renderIcon = (iconName?: string): React.ReactNode => {
  if (!iconName) return null;

  const IconComponent = ICON_MAP[iconName];
  if (IconComponent) {
    return React.createElement(IconComponent);
  }

  // 默认图标
  return React.createElement(QuestionCircleOutlined);
};

/**
 * 页面路径到信息的映射
 */
const PAGE_INFO_MAP: Record<string, PageInfo> = {
  // 首页/仪表板
  '/': {
    title: '首页',
    icon: 'HomeOutlined',
    description: '系统首页',
    keywords: ['首页', 'home', 'dashboard'],
    breadcrumb: ['首页']
  },
  '/dashboard': {
    title: '仪表板',
    icon: 'DashboardOutlined',
    description: '数据概览和统计信息',
    keywords: ['仪表板', 'dashboard', '概览', '统计'],
    breadcrumb: ['仪表板']
  },

  // 用户管理
  '/users': {
    title: '用户管理',
    icon: 'UserOutlined',
    description: '用户账户管理',
    keywords: ['用户', 'user', '账户', 'account'],
    breadcrumb: ['用户管理']
  },
  '/users/list': {
    title: '用户列表',
    icon: 'TeamOutlined',
    description: '查看和管理所有用户',
    keywords: ['用户列表', 'user list', '用户管理'],
    breadcrumb: ['用户管理', '用户列表']
  },
  '/users/create': {
    title: '创建用户',
    icon: 'UserOutlined',
    description: '创建新用户账户',
    keywords: ['创建用户', 'create user', '新建用户'],
    breadcrumb: ['用户管理', '创建用户']
  },
  '/users/online': {
    title: '在线用户',
    icon: 'UserOutlined',
    description: '在线用户监控',
    keywords: ['在线用户', 'online users', '用户监控'],
    breadcrumb: ['用户管理', '在线用户']
  },

  // 模型管理
  '/models': {
    title: '模型管理',
    icon: 'DatabaseOutlined',
    description: 'AI模型配置和管理',
    keywords: ['模型', 'model', 'AI', '人工智能'],
    breadcrumb: ['模型管理']
  },

  // 助手管理
  '/assistants': {
    title: '助手管理',
    icon: 'RobotOutlined',
    description: 'AI助手配置和管理',
    keywords: ['助手', 'assistant', 'AI', '机器人'],
    breadcrumb: ['助手管理']
  },

  // 知识库管理
  '/knowledge-base': {
    title: '知识库管理',
    icon: 'BookOutlined',
    description: '知识库内容管理',
    keywords: ['知识库', 'knowledge', '文档', '资料'],
    breadcrumb: ['知识库管理']
  },

  // 系统监控
  '/monitoring': {
    title: '系统监控',
    icon: 'MonitorOutlined',
    description: '系统状态监控和日志管理',
    keywords: ['监控', 'monitoring', '系统状态'],
    breadcrumb: ['系统监控']
  },
  '/monitoring/system-status': {
    title: '系统状态',
    icon: 'BarChartOutlined',
    description: '系统运行状态监控',
    keywords: ['系统状态', 'system status', '运行状态'],
    breadcrumb: ['系统监控', '系统状态']
  },
  '/monitoring/login-logs': {
    title: '登录日志',
    icon: 'UserOutlined',
    description: '用户登录日志查询',
    keywords: ['登录日志', 'login logs', '用户登录'],
    breadcrumb: ['系统监控', '登录日志']
  },
  '/monitoring/operation-logs': {
    title: '操作日志',
    icon: 'FileTextOutlined',
    description: '用户操作日志记录',
    keywords: ['操作日志', 'operation logs', '用户操作'],
    breadcrumb: ['系统监控', '操作日志']
  },
  '/monitoring/system-logs': {
    title: '系统日志',
    icon: 'FileTextOutlined',
    description: '系统运行日志',
    keywords: ['系统日志', 'system logs', '运行日志'],
    breadcrumb: ['系统监控', '系统日志']
  },

  // 系统设置
  '/settings': {
    title: '系统设置',
    icon: 'SettingOutlined',
    description: '系统配置和参数设置',
    keywords: ['设置', 'settings', '配置', 'config'],
    breadcrumb: ['系统设置']
  },
  '/settings/general': {
    title: '基础设置',
    icon: 'SettingOutlined',
    description: '基础系统配置',
    keywords: ['基础设置', 'general settings'],
    breadcrumb: ['系统设置', '基础设置']
  },
  '/settings/security': {
    title: '安全设置',
    icon: 'SafetyOutlined',
    description: '安全相关配置',
    keywords: ['安全设置', 'security settings'],
    breadcrumb: ['系统设置', '安全设置']
  },

  // 内容管理
  '/content': {
    title: '内容管理',
    icon: 'FileTextOutlined',
    description: '内容和文档管理',
    keywords: ['内容', 'content', '文档', 'document'],
    breadcrumb: ['内容管理']
  },
  '/content/articles': {
    title: '文章管理',
    icon: 'FileOutlined',
    description: '文章内容管理',
    keywords: ['文章', 'article', '内容管理'],
    breadcrumb: ['内容管理', '文章管理']
  },

  // 数据管理
  '/data': {
    title: '数据管理',
    icon: 'DatabaseOutlined',
    description: '数据库和数据管理',
    keywords: ['数据', 'data', '数据库', 'database'],
    breadcrumb: ['数据管理']
  },

  // 统计分析
  '/analytics': {
    title: '统计分析',
    icon: 'BarChartOutlined',
    description: '数据统计和分析',
    keywords: ['统计', 'analytics', '分析', 'analysis'],
    breadcrumb: ['统计分析']
  },
  '/analytics/reports': {
    title: '报表管理',
    icon: 'PieChartOutlined',
    description: '各类统计报表',
    keywords: ['报表', 'reports', '统计报表'],
    breadcrumb: ['统计分析', '报表管理']
  },

  // 通知管理
  '/notifications': {
    title: '通知管理',
    icon: 'BellOutlined',
    description: '系统通知和消息管理',
    keywords: ['通知', 'notifications', '消息', 'message'],
    breadcrumb: ['通知管理']
  },

  // 工具
  '/tools': {
    title: '系统工具',
    icon: 'ToolOutlined',
    description: '各种系统工具',
    keywords: ['工具', 'tools', '系统工具'],
    breadcrumb: ['系统工具']
  },

  // 帮助
  '/help': {
    title: '帮助中心',
    icon: 'QuestionCircleOutlined',
    description: '使用帮助和文档',
    keywords: ['帮助', 'help', '文档', 'documentation'],
    breadcrumb: ['帮助中心']
  },

  // 错误页面
  '/404': {
    title: '页面未找到',
    icon: 'QuestionCircleOutlined',
    description: '请求的页面不存在',
    keywords: ['404', '页面未找到', 'not found'],
    breadcrumb: ['错误页面', '404']
  },
  '/500': {
    title: '服务器错误',
    icon: 'BugOutlined',
    description: '服务器内部错误',
    keywords: ['500', '服务器错误', 'server error'],
    breadcrumb: ['错误页面', '500']
  },

  // 登录相关
  '/login': {
    title: '用户登录',
    icon: 'UserOutlined',
    description: '用户登录页面',
    keywords: ['登录', 'login', '用户登录'],
    breadcrumb: ['用户登录']
  },
  '/register': {
    title: '用户注册',
    icon: 'UserOutlined',
    description: '用户注册页面',
    keywords: ['注册', 'register', '用户注册'],
    breadcrumb: ['用户注册']
  },
};

/**
 * 根据路径获取页面信息
 * 优先从路由配置获取，然后从静态配置获取
 */
export const getPageInfo = (path: string): PageInfo => {
  // 1. 优先从路由配置中获取
  const routeInfo = getPageInfoFromRoutes(path);
  if (routeInfo) {
    return routeInfo;
  }

  // 2. 从静态配置中获取
  if (PAGE_INFO_MAP[path]) {
    return PAGE_INFO_MAP[path];
  }

  // 3. 动态路由匹配（如 /users/123/edit）
  const segments = path.split('/').filter(Boolean);

  // 尝试匹配父路径的路由配置
  for (let i = segments.length; i > 0; i--) {
    const parentPath = '/' + segments.slice(0, i).join('/');

    // 先尝试路由配置
    const parentRouteInfo = getPageInfoFromRoutes(parentPath);
    if (parentRouteInfo) {
      return {
        ...parentRouteInfo,
        title: `${parentRouteInfo.title} - 详情`,
        description: `${parentRouteInfo.description} - 详情页面`,
      };
    }

    // 再尝试静态配置
    if (PAGE_INFO_MAP[parentPath]) {
      const parentInfo = PAGE_INFO_MAP[parentPath];
      return {
        ...parentInfo,
        title: `${parentInfo.title} - 详情`,
        description: `${parentInfo.description} - 详情页面`,
      };
    }
  }

  // 4. 默认页面信息
  return {
    title: '未知页面',
    icon: 'QuestionCircleOutlined',
    description: '页面信息未配置',
    keywords: ['未知页面'],
    breadcrumb: ['未知页面']
  };
};

/**
 * 获取页面标题
 */
export const getPageTitle = (path: string): string => {
  return getPageInfo(path).title;
};

/**
 * 获取页面图标
 */
export const getPageIcon = (path: string): string | undefined => {
  return getPageInfo(path).icon;
};

/**
 * 获取页面描述
 */
export const getPageDescription = (path: string): string | undefined => {
  return getPageInfo(path).description;
};

/**
 * 获取页面面包屑
 */
export const getPageBreadcrumb = (path: string): string[] => {
  return getPageInfo(path).breadcrumb || [];
};

/**
 * 搜索页面
 */
export const searchPages = (query: string): PageInfo[] => {
  const lowerQuery = query.toLowerCase();
  
  return Object.entries(PAGE_INFO_MAP)
    .filter(([path, info]) => {
      return (
        info.title.toLowerCase().includes(lowerQuery) ||
        info.description?.toLowerCase().includes(lowerQuery) ||
        info.keywords?.some(keyword => keyword.toLowerCase().includes(lowerQuery)) ||
        path.toLowerCase().includes(lowerQuery)
      );
    })
    .map(([, info]) => info);
};

/**
 * 获取所有页面信息
 */
export const getAllPages = (): Record<string, PageInfo> => {
  return PAGE_INFO_MAP;
};
