import { App } from 'antd';

// 全局消息管理器
let messageInstance: any = null;

export const setMessageInstance = (instance: any) => {
  messageInstance = instance;
};

// 安全的消息方法，优先使用App context，回退到静态方法
export const messageUtils = {
  success: (content: string, duration?: number) => {
    if (messageInstance) {
      messageInstance.success(content, duration);
    } else {
      // 回退到静态方法，添加suppressWarnings以避免控制台警告
      import('antd').then(({ message }) => {
        message.success(content, duration);
      });
    }
  },

  error: (content: string | { content: string; key: string; duration?: number }, duration?: number) => {
    if (messageInstance) {
      if (typeof content === 'string') {
        messageInstance.error(content, duration);
      } else {
        messageInstance.error(content.content, content.duration || duration);
      }
    } else {
      import('antd').then(({ message }) => {
        if (typeof content === 'string') {
          message.error(content, duration);
        } else {
          message.error({
            content: content.content,
            key: content.key,
            duration: content.duration || duration,
          });
        }
      });
    }
  },

  warning: (content: string, duration?: number) => {
    if (messageInstance) {
      messageInstance.warning(content, duration);
    } else {
      import('antd').then(({ message }) => {
        message.warning(content, duration);
      });
    }
  },

  info: (content: string, duration?: number) => {
    if (messageInstance) {
      messageInstance.info(content, duration);
    } else {
      import('antd').then(({ message }) => {
        message.info(content, duration);
      });
    }
  },

  loading: (content: string, duration?: number) => {
    if (messageInstance) {
      return messageInstance.loading(content, duration);
    } else {
      return import('antd').then(({ message }) => {
        return message.loading(content, duration);
      });
    }
  },

  destroy: () => {
    if (messageInstance) {
      messageInstance.destroy();
    } else {
      import('antd').then(({ message }) => {
        message.destroy();
      });
    }
  },
};

// Hook for React components that are wrapped in App
export const useMessage = () => {
  const { message } = App.useApp();
  
  // 设置全局实例
  if (!messageInstance) {
    setMessageInstance(message);
  }
  
  return message;
}; 