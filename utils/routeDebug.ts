import routeConfig from '../config/routes';
import { flattenRoutes, generateBreadcrumbs } from './routeUtils';

/**
 * 路由调试工具
 * 用于检查路由配置是否正确
 */

export const debugRoutes = () => {
  console.group('🔍 路由配置调试');
  
  try {
    // 1. 检查路由扁平化
    const flattened = flattenRoutes(routeConfig);
    console.log('📋 扁平化路由:', Object.keys(flattened));
    
    // 2. 检查每个路由是否有有效的组件
    const routesWithoutElement = Object.entries(flattened)
      .filter(([path, route]) => !route.element && !route.redirect)
      .map(([path]) => path);
    
    if (routesWithoutElement.length > 0) {
      console.warn('⚠️ 没有element或redirect的路由:', routesWithoutElement);
    }
    
    // 3. 检查路由元数据
    const routesWithoutMeta = Object.entries(flattened)
      .filter(([path, route]) => !route.meta)
      .map(([path]) => path);
    
    if (routesWithoutMeta.length > 0) {
      console.warn('⚠️ 没有meta信息的路由:', routesWithoutMeta);
    }
    
    // 4. 测试面包屑生成
    const testPaths = [
      '/dashboard',
      '/models',
      '/monitoring/system-status',
      '/monitoring/login-logs',
      '/users/list',
      '/users/online',
      '/settings/general',
      '/settings/title-generation'
    ];
    
    console.group('🍞 面包屑测试');
    testPaths.forEach(path => {
      try {
        const breadcrumbs = generateBreadcrumbs(routeConfig, path);
        console.log(`${path}:`, breadcrumbs.map(b => b.title).join(' > '));
      } catch (error) {
        console.error(`${path}: 生成失败`, error);
      }
    });
    console.groupEnd();
    
    console.log('✅ 路由配置检查完成');
    
  } catch (error) {
    console.error('❌ 路由配置检查失败:', error);
  }
  
  console.groupEnd();
};

// 在开发环境自动运行调试
if (process.env.NODE_ENV === 'development') {
  // 延迟执行，避免在模块加载时运行
  setTimeout(debugRoutes, 1000);
}

export default debugRoutes;
