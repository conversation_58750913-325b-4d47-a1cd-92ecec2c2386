import React from 'react';

/**
 * 页面刷新工具类
 * 提供全局的页面刷新机制
 */

// 刷新事件类型
export interface RefreshEvent {
  tabId: string;
  path: string;
  timestamp: number;
}

// 刷新事件监听器类型
export type RefreshEventListener = (event: RefreshEvent) => void;

class RefreshManager {
  private listeners: Set<RefreshEventListener> = new Set();
  private refreshKeys: Map<string, number> = new Map();

  /**
   * 添加刷新事件监听器
   */
  addListener(listener: RefreshEventListener) {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  /**
   * 移除刷新事件监听器
   */
  removeListener(listener: RefreshEventListener) {
    this.listeners.delete(listener);
  }

  /**
   * 触发页面刷新
   */
  refresh(tabId: string, path: string) {
    const timestamp = Date.now();
    const event: RefreshEvent = { tabId, path, timestamp };
    
    // 更新刷新key
    this.refreshKeys.set(path, timestamp);
    
    // 通知所有监听器
    this.listeners.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Refresh listener error:', error);
      }
    });

    // 触发自定义DOM事件
    window.dispatchEvent(new CustomEvent('pageRefresh', { 
      detail: event 
    }));

    console.log('🔄 Page refresh triggered:', event);
  }

  /**
   * 获取页面的刷新key
   */
  getRefreshKey(path: string): number {
    return this.refreshKeys.get(path) || 0;
  }

  /**
   * 清除页面的刷新key
   */
  clearRefreshKey(path: string) {
    this.refreshKeys.delete(path);
  }

  /**
   * 清除所有刷新key
   */
  clearAllRefreshKeys() {
    this.refreshKeys.clear();
  }
}

// 全局刷新管理器实例
export const refreshManager = new RefreshManager();

/**
 * React Hook: 监听页面刷新事件
 */
export function usePageRefresh(callback: RefreshEventListener) {
  React.useEffect(() => {
    const unsubscribe = refreshManager.addListener(callback);
    return unsubscribe;
  }, [callback]);
}

/**
 * React Hook: 获取当前页面的刷新key
 */
export function useRefreshKey(path: string) {
  const [refreshKey, setRefreshKey] = React.useState(() => 
    refreshManager.getRefreshKey(path)
  );

  React.useEffect(() => {
    const handleRefresh = (event: RefreshEvent) => {
      if (event.path === path) {
        setRefreshKey(event.timestamp);
      }
    };

    const unsubscribe = refreshManager.addListener(handleRefresh);
    return unsubscribe;
  }, [path]);

  return refreshKey;
}
