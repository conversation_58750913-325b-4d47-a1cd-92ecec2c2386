/**
 * V2 Admin 认证相关工具函数
 * 基于主项目的认证工具，适配V2 Admin项目结构
 */
import { message } from 'antd';

/**
 * 清除所有认证信息并重定向到登录页面
 * @param showMessage 是否显示消息提示
 * @param messageText 消息内容
 * @param delay 延迟跳转时间(毫秒)
 */
export const clearAuthAndRedirect = (
  showMessage = true,
  messageText = '即将跳转到登录页面...',
  delay = 2000
) => {
  console.log('清除认证信息');

  // 清除所有认证相关的本地存储
  localStorage.removeItem('token');
  localStorage.removeItem('username');
  localStorage.removeItem('role');
  localStorage.removeItem('userId');
  localStorage.removeItem('superAdmin');
  localStorage.removeItem('avatar');

  // 显示消息提示
  if (showMessage && messageText) {
    message.info(messageText);
  }

  // 如果已经在登录页面，则不需要再重定向
  const currentPath = window.location.pathname;
  if (currentPath === '/login') {
    return;
  }

  // 延迟跳转到登录页面
  if (delay > 0) {
    setTimeout(() => {
      window.location.href = '/login';
    }, delay);
  } else {
    // 立即跳转
    window.location.href = '/login';
  }
};

/**
 * 检查用户是否已登录
 * @returns boolean
 */
export const isAuthenticated = (): boolean => {
  const token = localStorage.getItem('token');
  const username = localStorage.getItem('username');
  return !!(token && username);
};

/**
 * 获取当前用户信息
 * @returns 用户信息对象或null
 */
export const getCurrentUser = () => {
  if (!isAuthenticated()) {
    return null;
  }

  return {
    username: localStorage.getItem('username'),
    role: localStorage.getItem('role'),
    userId: localStorage.getItem('userId'),
    superAdmin: localStorage.getItem('superAdmin') === 'true',
    avatar: localStorage.getItem('avatar'),
  };
};

/**
 * 检查用户是否为管理员
 * @returns boolean
 */
export const isAdmin = (): boolean => {
  const role = localStorage.getItem('role');
  return role === 'admin';
};

/**
 * 检查用户是否为超级管理员
 * @returns boolean
 */
export const isSuperAdmin = (): boolean => {
  const superAdmin = localStorage.getItem('superAdmin');
  return superAdmin === 'true';
};

/**
 * 获取当前用户ID
 * @returns number | null
 */
export const getCurrentUserId = (): number | null => {
  const userId = localStorage.getItem('userId');
  return userId ? parseInt(userId, 10) : null;
};

/**
 * 获取认证token
 * @returns string | null
 */
export const getToken = (): string | null => {
  return localStorage.getItem('token');
};

/**
 * 保存用户认证信息
 * @param authData 认证数据
 */
export const saveAuthData = (authData: {
  token: string;
  username: string;
  role: string;
  id?: number | string;
  user_id?: number | string;
  userId?: number | string;
  superAdmin?: boolean;
  super_admin?: boolean;
  avatar?: string;
}) => {
  console.log('保存认证数据:', authData);

  if (!authData.token || !authData.username || !authData.role) {
    console.error('认证数据不完整:', authData);
    throw new Error('认证数据不完整，缺少必要字段');
  }

  localStorage.setItem('token', authData.token);
  localStorage.setItem('username', authData.username);
  localStorage.setItem('role', authData.role);

  // 处理用户ID - 支持多种字段名
  const userId = authData.id || authData.user_id || authData.userId;
  if (userId !== undefined && userId !== null) {
    localStorage.setItem('userId', userId.toString());
  } else {
    console.warn('用户ID未找到，使用默认值');
    localStorage.setItem('userId', '0');
  }

  // 处理超级管理员标识 - 支持多种字段名
  const superAdmin = authData.superAdmin ?? authData.super_admin ?? false;
  localStorage.setItem('superAdmin', superAdmin.toString());

  if (authData.avatar) {
    localStorage.setItem('avatar', authData.avatar);
  }

  console.log('认证数据保存成功');
};

const authUtils = {
  clearAuthAndRedirect,
  isAuthenticated,
  getCurrentUser,
  getCurrentUserId,
  getToken,
  isAdmin,
  isSuperAdmin,
  saveAuthData,
};

export default authUtils;
