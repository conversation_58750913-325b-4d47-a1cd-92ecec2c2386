import { RouteConfig, BreadcrumbItem, RouteMeta } from '../types';

/**
 * 路由工具函数集合
 */

/**
 * 扁平化路由配置，获取所有路由的映射
 */
export const flattenRoutes = (routes: RouteConfig[], parentPath = ''): Record<string, RouteConfig> => {
  const flattened: Record<string, RouteConfig> = {};
  
  routes.forEach(route => {
    const fullPath = route.path.startsWith('/') ? route.path : `${parentPath}${route.path}`;

    // 包含所有路由，包括重定向路由（用于查找元数据）
    flattened[fullPath] = {
      ...route,
      path: fullPath,
    };

    if (route.children) {
      Object.assign(flattened, flattenRoutes(route.children, fullPath));
    }
  });
  
  return flattened;
};

/**
 * 根据路径查找路由配置
 */
export const findRouteByPath = (routes: RouteConfig[], targetPath: string): RouteConfig | null => {
  const flattened = flattenRoutes(routes);
  return flattened[targetPath] || null;
};

/**
 * 获取路由的父级路径
 */
export const getParentPath = (path: string): string => {
  const segments = path.split('/').filter(Boolean);
  if (segments.length <= 1) return '/';
  return '/' + segments.slice(0, -1).join('/');
};

/**
 * 获取路由的所有父级路径
 */
export const getAllParentPaths = (path: string): string[] => {
  const segments = path.split('/').filter(Boolean);
  const paths: string[] = [];
  
  for (let i = 1; i <= segments.length; i++) {
    paths.push('/' + segments.slice(0, i).join('/'));
  }
  
  return paths;
};

/**
 * 根据当前路径生成面包屑导航
 */
export const generateBreadcrumbs = (
  routes: RouteConfig[],
  currentPath: string,
  options: {
    showHome?: boolean;
    homeTitle?: string;
    homePath?: string;
  } = {}
): BreadcrumbItem[] => {
  const { showHome = true, homeTitle = '首页', homePath = '/dashboard' } = options;
  const breadcrumbs: BreadcrumbItem[] = [];

  // 添加首页 - 可以点击跳转
  if (showHome && currentPath !== homePath) {
    breadcrumbs.push({
      title: homeTitle,
      path: homePath, // 首页可以点击跳转
      key: 'home',
    });
  }

  // 获取扁平化的路由映射
  const flattened = flattenRoutes(routes);

  // 根据当前路径构建面包屑
  const pathSegments = currentPath.split('/').filter(Boolean);
  let currentSegmentPath = '';

  pathSegments.forEach((segment, index) => {
    currentSegmentPath += '/' + segment;

    // 跳过首页（如果已经添加）
    if (showHome && currentSegmentPath === homePath) return;

    // 查找当前路径段对应的路由
    const route = flattened[currentSegmentPath];

    if (route && route.meta) {
      // 所有面包屑项都不可点击，只作为位置指示器
      breadcrumbs.push({
        title: route.meta.title,
        path: undefined, // 移除所有跳转功能
        icon: route.meta.icon,
        key: currentSegmentPath,
      });
    } else {
      // 如果没有找到当前路径的路由，检查是否需要添加父级
      const parentPath = getParentPath(currentSegmentPath);
      if (parentPath && parentPath !== '/' && parentPath !== currentSegmentPath) {
        const parentRoute = flattened[parentPath];
        if (parentRoute && parentRoute.meta) {
          // 添加父级面包屑（如果还没有添加）
          const existingParent = breadcrumbs.find(b => b.key === parentPath);
          if (!existingParent) {
            breadcrumbs.push({
              title: parentRoute.meta.title,
              path: undefined, // 父级不可点击
              icon: parentRoute.meta.icon,
              key: parentPath,
            });
          }
        }
      }
    }
  });

  return breadcrumbs;
};

/**
 * 检查路径是否匹配
 */
export const isPathMatch = (routePath: string, currentPath: string): boolean => {
  // 精确匹配
  if (routePath === currentPath) return true;
  
  // 动态路由匹配（简单实现）
  const routeSegments = routePath.split('/');
  const currentSegments = currentPath.split('/');
  
  if (routeSegments.length !== currentSegments.length) return false;
  
  return routeSegments.every((segment, index) => {
    return segment.startsWith(':') || segment === currentSegments[index];
  });
};

/**
 * 获取路由的活跃状态
 */
export const getActiveRoutes = (routes: RouteConfig[], currentPath: string): string[] => {
  const activeRoutes: string[] = [];
  const parentPaths = getAllParentPaths(currentPath);
  
  parentPaths.forEach(path => {
    const route = findRouteByPath(routes, path);
    if (route) {
      activeRoutes.push(path);
    }
  });
  
  return activeRoutes;
};

/**
 * 根据路由配置生成菜单数据
 */
export const generateMenuFromRoutes = (routes: RouteConfig[]): any[] => {
  return routes
    .filter(route => !route.meta?.hideInMenu && !route.redirect)
    .map(route => {
      const menuItem: any = {
        key: route.path,
        label: route.meta?.title || route.path,
        icon: route.meta?.icon,
        path: route.path,
      };
      
      if (route.children) {
        const childMenus = generateMenuFromRoutes(route.children);
        if (childMenus.length > 0) {
          menuItem.children = childMenus;
        }
      }
      
      return menuItem;
    });
};

/**
 * 搜索路由
 */
export const searchRoutes = (routes: RouteConfig[], keyword: string): RouteConfig[] => {
  const results: RouteConfig[] = [];
  const flattened = flattenRoutes(routes);
  
  Object.values(flattened).forEach(route => {
    if (route.meta) {
      const { title, description, keywords } = route.meta;
      const searchText = [title, description, ...(keywords || [])].join(' ').toLowerCase();
      
      if (searchText.includes(keyword.toLowerCase())) {
        results.push(route);
      }
    }
  });
  
  return results;
};

/**
 * 获取路由的重定向目标
 */
export const resolveRedirect = (routes: RouteConfig[], path: string): string => {
  const route = findRouteByPath(routes, path);
  if (route?.redirect) {
    // 递归解析重定向
    return resolveRedirect(routes, route.redirect);
  }
  return path;
};

/**
 * 验证路由路径是否有效
 */
export const isValidRoute = (routes: RouteConfig[], path: string): boolean => {
  const resolvedPath = resolveRedirect(routes, path);
  const route = findRouteByPath(routes, resolvedPath);
  return !!route && !!route.element;
};

/**
 * 获取路由的元数据
 */
export const getRouteMeta = (routes: RouteConfig[], path: string): RouteMeta | null => {
  const route = findRouteByPath(routes, path);
  return route?.meta || null;
};

/**
 * 格式化路径（确保以/开头，移除重复的/）
 */
export const normalizePath = (path: string): string => {
  return ('/' + path).replace(/\/+/g, '/').replace(/\/$/, '') || '/';
};

/**
 * 检查用户是否有访问路由的权限
 */
export const hasRoutePermission = (route: RouteConfig, userRoles: string[]): boolean => {
  if (!route.meta?.roles || route.meta.roles.length === 0) {
    return true; // 没有角色限制，允许访问
  }
  
  return route.meta.roles.some(role => userRoles.includes(role));
};

export default {
  flattenRoutes,
  findRouteByPath,
  getParentPath,
  getAllParentPaths,
  generateBreadcrumbs,
  isPathMatch,
  getActiveRoutes,
  generateMenuFromRoutes,
  searchRoutes,
  resolveRedirect,
  isValidRoute,
  getRouteMeta,
  normalizePath,
  hasRoutePermission,
};
