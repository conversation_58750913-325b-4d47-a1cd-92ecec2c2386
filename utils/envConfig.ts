/**
 * V2 Admin 环境变量配置工具
 * 统一管理和验证环境变量配置
 */

// 环境变量配置接口
export interface EnvConfig {
  // 应用配置
  app: {
    title: string;
    version: string;
    description: string;
  };
  
  // 服务器配置
  server: {
    port: number;
    host: string;
  };
  
  // API配置
  api: {
    baseUrl: string;
    timeout: number;
    useProxy: boolean;
    proxyTarget: string;
  };
  
  // 主题配置
  theme: {
    primaryColor: string;
    borderRadius: number;
  };
  
  // 开发配置
  dev: {
    devTools: boolean;
    sourceMap: boolean;
  };
}

/**
 * 智能获取API基础URL
 * 根据跨域配置自动选择合适的API地址
 */
const getApiBaseUrl = (): string => {
  const useProxy = import.meta.env.VITE_USE_PROXY === 'true';
  const configuredUrl = import.meta.env.VITE_API_BASE_URL;

  // 如果启用代理模式
  if (useProxy) {
    // 开发环境使用相对路径，通过 Vite 代理转发
    return '/api';
  }

  // 直接请求模式：使用配置的完整URL
  if (configuredUrl) {
    return configuredUrl;
  }

  // 默认配置
  return 'http://localhost:8081/api';
};

/**
 * 获取环境变量配置
 */
export const getEnvConfig = (): EnvConfig => {
  return {
    app: {
      title: import.meta.env.VITE_APP_TITLE || 'V2 Admin 管理界面',
      version: import.meta.env.VITE_APP_VERSION || '1.0.0',
      description: import.meta.env.VITE_APP_DESCRIPTION || 'V2 管理界面独立项目',
    },
    
    server: {
      port: parseInt(import.meta.env.VITE_PORT || '9982'),
      host: import.meta.env.VITE_HOST || '0.0.0.0',
    },
    
    api: {
      baseUrl: getApiBaseUrl(),
      timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '30000'),
      useProxy: import.meta.env.VITE_USE_PROXY === 'true',
      proxyTarget: import.meta.env.VITE_PROXY_TARGET || 'http://localhost:8081',
    },
    
    theme: {
      primaryColor: import.meta.env.VITE_THEME_PRIMARY_COLOR || '#1890ff',
      borderRadius: parseInt(import.meta.env.VITE_THEME_BORDER_RADIUS || '6'),
    },
    
    dev: {
      devTools: import.meta.env.VITE_DEV_TOOLS === 'true',
      sourceMap: import.meta.env.VITE_SOURCE_MAP === 'true',
    },
  };
};

/**
 * 验证环境变量配置
 */
export const validateEnvConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const config = getEnvConfig();

  // 验证端口号
  if (isNaN(config.server.port) || config.server.port < 1 || config.server.port > 65535) {
    errors.push('VITE_PORT 必须是1-65535之间的有效端口号');
  }

  // 验证API超时时间
  if (isNaN(config.api.timeout) || config.api.timeout < 1000) {
    errors.push('VITE_API_TIMEOUT 必须是大于等于1000的数字（毫秒）');
  }

  // 验证API URL格式 - 支持相对路径（代理模式）和绝对URL
  const apiUrl = config.api.baseUrl;
  const isRelativePath = apiUrl.startsWith('/');
  const isValidUrl = (() => {
    try {
      new URL(apiUrl);
      return true;
    } catch {
      return false;
    }
  })();

  if (!isRelativePath && !isValidUrl) {
    errors.push('VITE_API_BASE_URL 必须是有效的URL格式或相对路径（如 /api）');
  }

  // 验证主题颜色格式
  if (!/^#[0-9A-Fa-f]{6}$/.test(config.theme.primaryColor)) {
    errors.push('VITE_THEME_PRIMARY_COLOR 必须是有效的十六进制颜色值（如 #1890ff）');
  }

  // 验证边框圆角
  if (isNaN(config.theme.borderRadius) || config.theme.borderRadius < 0) {
    errors.push('VITE_THEME_BORDER_RADIUS 必须是大于等于0的数字');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * 打印环境变量配置信息（开发环境）
 */
export const logEnvConfig = (): void => {
  if (import.meta.env.DEV) {
    const config = getEnvConfig();
    const validation = validateEnvConfig();

    console.group('🔧 V2 Admin 环境变量配置');
    console.log('📱 应用配置:', config.app);
    console.log('🌐 服务器配置:', config.server);
    console.log('🔗 API配置:', config.api);
    console.log('🔗 原始环境变量 VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
    console.log('🎨 主题配置:', config.theme);
    console.log('🛠️ 开发配置:', config.dev);

    if (validation.isValid) {
      console.log('✅ 配置验证通过');
    } else {
      console.warn('❌ 配置验证失败:');
      validation.errors.forEach(error => console.warn(`  - ${error}`));
    }
    console.groupEnd();
  }
};

/**
 * 获取当前运行环境信息
 */
export const getEnvironmentInfo = () => {
  return {
    mode: import.meta.env.MODE,
    dev: import.meta.env.DEV,
    prod: import.meta.env.PROD,
    ssr: import.meta.env.SSR,
  };
};

// 导出配置实例
export const envConfig = getEnvConfig();

// 默认导出
export default {
  getEnvConfig,
  validateEnvConfig,
  logEnvConfig,
  getEnvironmentInfo,
  envConfig,
};
