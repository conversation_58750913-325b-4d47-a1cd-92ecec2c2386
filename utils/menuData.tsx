// import React from 'react'; // 移除未使用的导入
import {
  DashboardOutlined,
  DatabaseOutlined,
  UserOutlined,
  SettingOutlined,
  MonitorOutlined,
  BookOutlined,
  RobotOutlined,
  BarChartOutlined,
  TeamOutlined,
  SafetyOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  EditOutlined,
  SafetyCertificateOutlined,
} from '@ant-design/icons';
import { MenuItem } from '../types';

/**
 * 菜单数据配置 - 从现有项目提取并适配demo风格
 * 保持现有的菜单结构和功能，采用demo的设计模式
 */

export const menuData: MenuItem[] = [
  {
    key: 'dashboard',
    label: '仪表板',
    icon: <DashboardOutlined />,
    path: '/dashboard',
  },
  {
    key: 'model-management',
    label: '模型管理',
    icon: <DatabaseOutlined />,
    path: '/models',
  },
  {
    key: 'monitoring',
    label: '系统监控',
    icon: <MonitorOutlined />,
    // 父级菜单不设置path，使其不可点击
    children: [
      {
        key: 'system-status',
        label: '系统状态',
        path: '/monitoring/system-status',
        icon: <BarChartOutlined />,
      },
      {
        key: 'login-logs',
        label: '登录日志',
        path: '/monitoring/login-logs',
        icon: <UserOutlined />,
      },
      {
        key: 'operation-logs',
        label: '操作日志',
        path: '/monitoring/operation-logs',
        icon: <FileTextOutlined />,
      },
      {
        key: 'system-logs',
        label: '系统日志',
        path: '/monitoring/system-logs',
        icon: <FileTextOutlined />,
      },
    ],
  },
  {
    key: 'user-management',
    label: '用户管理',
    icon: <UserOutlined />,
    // 父级菜单不设置path，使其不可点击
    children: [
      {
        key: 'user-list',
        label: '用户列表',
        path: '/users/list',
        icon: <TeamOutlined />,
      },
      {
        key: 'online-users',
        label: '在线用户',
        path: '/users/online',
        icon: <UserOutlined />,
      },
    ],
  },
  {
    key: 'assistant-management',
    label: '助手管理',
    icon: <RobotOutlined />,
    path: '/assistants',
  },
  {
    key: 'knowledge-base',
    label: '知识库管理',
    icon: <BookOutlined />,
    path: '/knowledge-base',
  },
  {
    key: 'system-settings',
    label: '系统设置',
    icon: <SettingOutlined />,
    // 父级菜单不设置path，使其不可点击
    children: [
      {
        key: 'general-settings',
        label: '基础设置',
        path: '/settings/general',
        icon: <SettingOutlined />,
      },
      {
        key: 'title-generation-settings',
        label: '标题生成',
        path: '/settings/title-generation',
        icon: <EditOutlined />,
      },
      {
        key: 'captcha-settings',
        label: '验证码设置',
        path: '/settings/captcha',
        icon: <SafetyCertificateOutlined />,
      },
      {
        key: 'security-settings',
        label: '安全设置',
        path: '/settings/security',
        icon: <SafetyOutlined />,
      },
      {
        key: 'faq-management',
        label: 'FAQ管理',
        path: '/settings/faq',
        icon: <QuestionCircleOutlined />,
      },
      {
        key: 'file-settings',
        label: '文件设置',
        path: '/settings/file',
        icon: <FileTextOutlined />,
      },
    ],
  },
];

/**
 * 获取菜单项的路径映射
 */
export const getMenuPathMap = (): Record<string, string> => {
  const pathMap: Record<string, string> = {};
  
  const extractPaths = (items: MenuItem[]) => {
    items.forEach(item => {
      if (item.path) {
        pathMap[item.path] = item.key;
      }
      if (item.children) {
        extractPaths(item.children);
      }
    });
  };
  
  extractPaths(menuData);
  return pathMap;
};

/**
 * 根据路径获取菜单key
 */
export const getMenuKeyByPath = (path: string): string | undefined => {
  const pathMap = getMenuPathMap();
  return pathMap[path];
};

/**
 * 获取菜单项的父级keys
 */
export const getParentKeys = (targetKey: string): string[] => {
  const parentKeys: string[] = [];

  const findParent = (items: MenuItem[], parents: string[] = []): boolean => {
    for (const item of items) {
      if (item.key === targetKey) {
        parentKeys.push(...parents);
        return true;
      }

      if (item.children) {
        if (findParent(item.children, [...parents, item.key])) {
          return true;
        }
      }
    }
    return false;
  };

  findParent(menuData);
  return parentKeys;
};

/**
 * 获取菜单项的直接父级key
 */
export const getDirectParentKey = (targetKey: string): string | null => {
  const findParent = (items: MenuItem[], parentKey?: string): string | null => {
    for (const item of items) {
      if (item.key === targetKey) {
        return parentKey || null;
      }

      if (item.children) {
        const found = findParent(item.children, item.key);
        if (found !== null) return found;
      }
    }
    return null;
  };

  return findParent(menuData);
};

/**
 * 获取默认展开的菜单keys
 */
export const getDefaultOpenKeys = (): string[] => {
  return menuData
    .filter(item => item.children && item.children.length > 0)
    .map(item => item.key);
};

/**
 * 检查菜单项是否为叶子节点（没有子菜单）
 */
export const isLeafMenuItem = (key: string): boolean => {
  const findItem = (items: MenuItem[]): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findItem(item.children);
        if (found) return found;
      }
    }
    return null;
  };

  const item = findItem(menuData);
  return item ? !item.children || item.children.length === 0 : false;
};

/**
 * 根据当前路径获取完整的菜单状态
 * 🔧 重新设计：支持展开和折叠状态下的正确选中逻辑
 */
export const getMenuStateByPath = (currentPath: string, collapsed: boolean = false) => {
  const selectedKey = getMenuKeyByPath(currentPath);
  const openKeys = selectedKey ? getParentKeys(selectedKey) : [];
  const directParentKey = selectedKey ? getDirectParentKey(selectedKey) : null;

  if (!selectedKey) {
    return {
      selectedKeys: [],
      openKeys: [],
      activeParentKeys: [],
    };
  }

  if (collapsed) {
    // 🔧 折叠模式：显示父级菜单为选中状态
    if (isLeafMenuItem(selectedKey) && directParentKey) {
      // 子菜单项被选中 -> 父级菜单显示选中状态
      return {
        selectedKeys: [directParentKey],
        openKeys: [],
        activeParentKeys: [directParentKey],
      };
    } else {
      // 父级菜单被选中 -> 直接显示选中状态
      return {
        selectedKeys: [selectedKey],
        openKeys: [],
        activeParentKeys: [],
      };
    }
  } else {
    // 🔧 展开模式：子菜单项选中，父级菜单显示活跃状态
    if (isLeafMenuItem(selectedKey)) {
      return {
        selectedKeys: [selectedKey], // 子菜单项选中
        openKeys,
        activeParentKeys: directParentKey ? [directParentKey] : [], // 父级菜单活跃
      };
    } else {
      // 父级菜单被直接选中（如果有路径的话）
      return {
        selectedKeys: [selectedKey],
        openKeys,
        activeParentKeys: [],
      };
    }
  }
};

/**
 * 扁平化菜单数据（用于搜索等功能）
 */
export const flattenMenuData = (): MenuItem[] => {
  const flattened: MenuItem[] = [];
  
  const flatten = (items: MenuItem[]) => {
    items.forEach(item => {
      flattened.push(item);
      if (item.children) {
        flatten(item.children);
      }
    });
  };
  
  flatten(menuData);
  return flattened;
};

/**
 * 菜单搜索功能
 */
export const searchMenuItems = (keyword: string): MenuItem[] => {
  if (!keyword.trim()) return [];
  
  const flattened = flattenMenuData();
  const lowerKeyword = keyword.toLowerCase();
  
  return flattened.filter(item => 
    item.label.toLowerCase().includes(lowerKeyword) ||
    item.key.toLowerCase().includes(lowerKeyword)
  );
};

export default menuData;
