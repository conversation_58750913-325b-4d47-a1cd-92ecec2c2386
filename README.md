# V2 Admin - 独立管理界面

一个基于 React + TypeScript + Antd 的现代化管理界面，完全独立运行，无样式冲突。

## ✨ 特性

- 🚀 **现代化技术栈**: React 18 + TypeScript + Vite
- 🎨 **优雅设计**: 基于 Antd 5.x 设计系统
- 📱 **响应式布局**: 完美适配桌面端和移动端
- 🌓 **主题支持**: 支持亮色/暗色主题切换
- 🔧 **完全独立**: 无任何外部样式依赖和冲突
- ⚡ **高性能**: Vite 构建，热更新快速

## 🛠️ 技术栈

- **框架**: React 18.2.0
- **语言**: TypeScript 5.0+
- **构建工具**: Vite 4.4+
- **UI库**: Antd 5.8+
- **路由**: React Router 6.15+
- **样式**: Sass/SCSS
- **图标**: Ant Design Icons

## 📦 安装依赖

```bash
npm install
```

## 🚀 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:9982 启动

## 🏗️ 构建生产版本

```bash
npm run build
```

## 🧪 类型检查

```bash
npm run type-check
```

## 📁 项目结构

```
v2-admin/
├── src/                    # 源代码目录
│   ├── main.tsx           # 应用入口
│   ├── App.tsx            # 主应用组件
│   └── index.scss         # 全局样式
├── components/            # 组件目录
│   ├── Sidebar/          # 侧边栏组件
│   └── Header/           # 顶部栏组件
├── layouts/              # 布局组件
│   └── BasicLayout/      # 基础布局
├── pages/                # 页面组件
│   └── DashboardV2.tsx   # 仪表板页面
├── styles/               # 样式文件
│   └── variables.scss    # 样式变量
├── hooks/                # 自定义Hooks
├── utils/                # 工具函数
├── types/                # 类型定义
├── package.json          # 项目配置
├── vite.config.ts        # Vite配置
└── tsconfig.json         # TypeScript配置
```

## 🎯 核心功能

### 布局系统
- ✅ 响应式侧边栏（支持展开/收起）
- ✅ 固定顶部导航栏
- ✅ 面包屑导航
- ✅ 移动端抽屉式侧边栏

### 主题系统
- ✅ 亮色/暗色主题切换
- ✅ 自定义主色调
- ✅ 响应式设计

### 路由系统
- ✅ 基于 React Router 的路由管理
- ✅ 嵌套路由支持
- ✅ 404 页面处理

## 🔧 配置说明

### 端口配置
项目默认运行在端口 9982，可通过以下方式修改：

1. 修改 `package.json` 中的 scripts
2. 修改 `vite.config.ts` 中的 server.port
3. 修改 `.env` 文件中的 VITE_PORT

### 样式配置
- 主要样式变量在 `styles/variables.scss`
- 全局样式在 `src/index.scss`
- 组件样式采用模块化管理

## 🐛 问题解决

### 样式冲突
本项目已完全独立，不会与其他项目产生样式冲突。

### 端口冲突
如果端口 9982 被占用，Vite 会自动选择下一个可用端口。

## 📝 开发指南

### 添加新页面
1. 在 `pages/` 目录创建新组件
2. 在 `src/App.tsx` 中添加路由配置
3. 在侧边栏菜单中添加导航项

### 自定义主题
修改 `src/main.tsx` 中的 theme 配置对象。

### 添加新组件
在 `components/` 目录下创建新组件，遵循现有的目录结构。

## 📄 许可证

MIT License
