#!/usr/bin/env node

/**
 * 批量修复页面动画类型问题的脚本
 */

const fs = require('fs');
const path = require('path');

// 需要修复的文件列表
const filesToFix = [
  'pages/KnowledgeBaseManagement/index.tsx',
  'pages/ModelManagementV2/index.tsx',
  'pages/OnlineUsersManagement/index.tsx',
  'pages/SystemLogsPage/index.tsx',
  'pages/LoginLogsPage/index.tsx',
  'pages/OperationLogsPage/index.tsx',
  'pages/SystemStatusPage/index.tsx',
  'pages/SystemSettings/FAQManagement.tsx',
  'pages/SystemSettings/FileMiddlewareSettings.tsx',
  'pages/SystemSettings/GeneralSettings.tsx',
];

// 修复函数
function fixFile(filePath) {
  try {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;

    // 检查是否已经导入了 AnimationType
    if (!content.includes('import { AnimationType }') && content.includes('usePageTransition')) {
      // 查找 usePageTransition 的导入行
      const importMatch = content.match(/import { usePageTransition } from ['"]([^'"]+)['"];/);
      if (importMatch) {
        const newImport = `import { usePageTransition } from '${importMatch[1]}';\nimport { AnimationType } from '../../stores/global/pageAnimationSlice';`;
        content = content.replace(importMatch[0], newImport);
        modified = true;
      }
    }

    // 替换 'fadeInUp' as any 为 AnimationType.FADE_IN_UP
    if (content.includes("type: 'fadeInUp' as any")) {
      content = content.replace(/type: 'fadeInUp' as any/g, 'type: AnimationType.FADE_IN_UP');
      modified = true;
    }

    // 保存文件
    if (modified) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ 修复完成: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  无需修复: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ 修复失败: ${filePath}`, error.message);
    return false;
  }
}

// 主函数
function main() {
  console.log('🔧 开始批量修复页面动画类型问题...\n');
  
  let fixedCount = 0;
  let totalCount = filesToFix.length;

  for (const file of filesToFix) {
    if (fixFile(file)) {
      fixedCount++;
    }
  }

  console.log(`\n📊 修复完成: ${fixedCount}/${totalCount} 个文件`);
  
  if (fixedCount > 0) {
    console.log('\n🎉 所有类型问题已修复！');
  } else {
    console.log('\n✨ 所有文件都已是最新状态！');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { fixFile, filesToFix };
