# 跨域配置说明

本项目支持通过环境变量统一控制跨域处理方式，可以根据不同的部署环境和后端配置灵活切换。

## 🔧 配置变量

### 主要配置项

| 变量名 | 说明 | 可选值 | 默认值 |
|--------|------|--------|--------|
| `VITE_USE_PROXY` | 是否启用代理模式 | `true` / `false` | `false` |
| `VITE_API_BASE_URL` | API基础地址 | URL字符串 | `http://localhost:8081/api` |
| `VITE_PROXY_TARGET` | 代理目标地址 | URL字符串 | `http://localhost:8081` |

## 📋 使用场景

### 场景1：开发环境 + 代理模式
**适用于：** 开发阶段，后端未配置CORS

```env
# .env.development
VITE_USE_PROXY=true
VITE_API_BASE_URL=/api
VITE_PROXY_TARGET=http://**************:8081
```

**工作原理：**
- 前端请求 `/api/xxx`
- Vite 代理转发到 `http://**************:8081/api/xxx`
- 避免浏览器跨域限制

### 场景2：生产环境 + 直接请求
**适用于：** 生产部署，后端已配置CORS

```env
# .env.production
VITE_USE_PROXY=false
VITE_API_BASE_URL=http://**************:8081/api
```

**工作原理：**
- 前端直接请求 `http://**************:8081/api/xxx`
- 依赖后端CORS配置允许跨域

### 场景3：同域部署
**适用于：** 前后端部署在同一域名下

```env
VITE_USE_PROXY=false
VITE_API_BASE_URL=/api
```

## 🚀 快速切换

### 启用代理模式（解决跨域）
```bash
# 修改 .env 文件
VITE_USE_PROXY=true
VITE_PROXY_TARGET=http://**************:8081
```

### 禁用代理模式（依赖后端CORS）
```bash
# 修改 .env 文件
VITE_USE_PROXY=false
VITE_API_BASE_URL=http://**************:8081/api
```

## 🔍 调试信息

启动开发服务器后，在浏览器控制台查看配置信息：

```
🔗 API 配置信息:
  - Base URL: /api
  - 使用代理: true
  - 代理目标: http://**************:8081
```

## ⚠️ 注意事项

1. **代理模式仅在开发环境有效**，生产环境需要其他解决方案
2. **修改配置后需要重启开发服务器**
3. **确保后端服务器地址正确且可访问**
4. **生产环境建议使用 Nginx 反向代理或后端CORS配置**

## 🛠️ 故障排除

### 问题1：代理不生效
- 检查 `VITE_USE_PROXY=true`
- 确认 `VITE_PROXY_TARGET` 地址正确
- 重启开发服务器

### 问题2：直接请求跨域错误
- 检查后端CORS配置
- 确认 `VITE_USE_PROXY=false`
- 验证 `VITE_API_BASE_URL` 地址

### 问题3：配置不生效
- 检查环境变量文件优先级
- 清除浏览器缓存
- 重启开发服务器
