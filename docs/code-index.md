# 代码索引

## 组件列表

| 组件名称 | 描述 | 文件路径 |
|----------|------|----------|
| ModelCreateForm | 模型创建表单 | /components/ModelCreateForm/index.tsx |
| Header | 页面头部组件 | /components/Header/index.tsx |
| UserTable | 用户表格组件 | /components/UserTable/index.tsx |
| UserCreateForm | 用户创建表单 | /components/UserCreateForm/index.tsx |
| CommonTable | 通用表格组件 | /components/CommonTable/index.tsx |
| ApiSourceForm | API 源表单 | /components/ApiSourceForm/index.tsx |
| ChunkDetailDrawer | 分块详情抽屉 | /components/ChunkDetailDrawer/index.tsx |
| CustomTable | 自定义表格 | /components/CustomTable/index.tsx |
| DocumentEditModal | 文档编辑模态框 | /components/DocumentEditModal/index.tsx |
| DocumentManagementModal | 文档管理模态框 | /components/DocumentManagementModal/index.tsx |
| FramerPageAnimation | 页面动画组件 | /components/FramerPageAnimation/index.tsx |
| KnowledgeBaseDetailModal | 知识库详情模态框 | /components/KnowledgeBaseDetailModal/index.tsx |
| KnowledgeBaseFormModal | 知识库表单模态框 | /components/KnowledgeBaseFormModal/index.tsx |
| LogsProTable | 日志高级表格 | /components/LogsProTable/index.tsx |
| PageLoadingAnimation | 页面加载动画 | /components/PageLoadingAnimation/index.tsx |
| Sidebar | 侧边栏组件 | /components/Sidebar/index.tsx |
| TabsHistory | 标签页历史组件 | /components/TabsHistory/index.tsx |

## 页面列表

| 页面名称 | 描述 | 文件路径 |
|----------|------|----------|
| ModelListManagement | 模型列表管理页 | /pages/ModelListManagement/index.tsx |
| AssistantManagement | 助手管理页 | /pages/AssistantManagement/index.tsx |
| DashboardV2 | 仪表盘页面 | /pages/DashboardV2/index.tsx |
| AnimationTestPage | 动画测试页面 | /pages/AnimationTestPage/index.tsx |
| DiagnosticsPage | 诊断页面 | /pages/DiagnosticsPage/index.tsx |
| EnvConfigPage | 环境配置页面 | /pages/EnvConfigPage/index.tsx |
| KnowledgeBaseManagement | 知识库管理页 | /pages/KnowledgeBaseManagement/index.tsx |
| KnowledgeBaseTest | 知识库测试页 | /pages/KnowledgeBaseTest/index.tsx |
| LoginLogsPage | 登录日志页面 | /pages/LoginLogsPage/index.tsx |
| LogsTestPage | 日志测试页面 | /pages/LogsTestPage/index.tsx |

## 类型定义

| 类型名称 | 描述 | 文件路径 |
|----------|------|----------|
| Model | 模型相关类型定义 | /types/model.ts |
| ApiSource | API 源类型定义 | /types/model.ts |