# 主题系统使用指南

## 概述

v2-admin 项目集成了完整的暗色/亮色主题切换功能，基于 Ant Design 的 ConfigProvider 和 theme 配置实现，使用 Zustand 进行状态管理。

## 功能特性

- ✅ 支持亮色主题（light）和暗色主题（dark）
- ✅ 支持跟随系统主题（auto）
- ✅ 主题状态持久化（页面刷新后保持用户选择）
- ✅ 平滑的主题切换动画
- ✅ 完整的 TypeScript 类型支持
- ✅ 响应式设计适配

## 主题模式

### 1. 亮色主题 (light)
- 白色背景，深色文字
- 适合明亮环境使用

### 2. 暗色主题 (dark)
- 深色背景，浅色文字
- 适合暗光环境使用，减少眼部疲劳

### 3. 跟随系统 (auto)
- 自动检测系统主题偏好
- 当系统主题变化时自动切换

## 使用方法

### 1. 主题切换组件

```tsx
import ThemeToggle from '../components/ThemeToggle';

// 下拉菜单模式（推荐）
<ThemeToggle mode="dropdown" />

// 简单按钮模式
<ThemeToggle mode="button" />

// 带文本显示
<ThemeToggle mode="dropdown" showText />
```

### 2. 主题状态管理

```tsx
import { useThemeStore } from '../stores';

const MyComponent = () => {
  const {
    mode,           // 当前主题模式: 'light' | 'dark' | 'auto'
    actualTheme,    // 实际应用的主题: 'light' | 'dark'
    isTransitioning, // 是否正在切换主题
    setThemeMode,   // 设置主题模式
    toggleTheme,    // 切换主题
  } = useThemeStore();

  return (
    <div>
      <p>当前主题: {actualTheme}</p>
      <button onClick={() => setThemeMode('dark')}>
        切换到暗色主题
      </button>
    </div>
  );
};
```

### 3. 自定义样式适配

```scss
// 使用 CSS 变量
.my-component {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

// 使用主题选择器
[data-theme='light'] {
  .my-component {
    background-color: #ffffff;
    color: #000000;
  }
}

[data-theme='dark'] {
  .my-component {
    background-color: #1f1f1f;
    color: #ffffff;
  }
}
```

## 技术实现

### 1. 状态管理架构

```
ThemeSlice (Zustand)
├── mode: ThemeMode              // 用户选择的主题模式
├── actualTheme: 'light' | 'dark' // 实际应用的主题
├── isTransitioning: boolean     // 切换动画状态
├── systemTheme: 'light' | 'dark' // 系统主题
└── actions: ThemeActions        // 主题操作方法
```

### 2. 主题提供者

```tsx
// ThemeProvider 组件负责：
// 1. 初始化主题状态
// 2. 监听系统主题变化
// 3. 配置 Ant Design 主题
// 4. 设置 HTML 根元素属性

<ThemeProvider>
  <App />
</ThemeProvider>
```

### 3. 样式系统

- **CSS 变量**: 定义主题相关的颜色、间距等
- **数据属性**: 使用 `[data-theme]` 选择器
- **过渡动画**: 平滑的主题切换效果

## 配置选项

### 1. Ant Design 主题配置

主题系统会自动配置以下 Ant Design 组件：

- Layout（布局）
- Menu（菜单）
- Button（按钮）
- Input（输入框）
- Table（表格）
- Card（卡片）
- Modal（模态框）
- Dropdown（下拉菜单）
- Tooltip（工具提示）

### 2. 自定义主题令牌

```tsx
// 在 ThemeProvider 中可以自定义主题令牌
const customTheme = {
  token: {
    colorPrimary: '#1677ff',    // 主色
    borderRadius: 6,            // 圆角
    fontSize: 14,               // 字体大小
    // ... 更多配置
  }
};
```

## 最佳实践

### 1. 组件开发

- 使用 CSS 变量而不是硬编码颜色
- 测试组件在两种主题下的显示效果
- 确保文字对比度符合无障碍标准

### 2. 样式编写

```scss
// ✅ 推荐：使用 CSS 变量
.component {
  background: var(--bg-primary);
  color: var(--text-primary);
}

// ❌ 不推荐：硬编码颜色
.component {
  background: #ffffff;
  color: #000000;
}
```

### 3. 性能优化

- 主题切换使用 CSS 过渡而不是 JavaScript 动画
- 避免在主题切换时重新渲染大量组件
- 使用 `isTransitioning` 状态控制动画时机

## 故障排除

### 1. 主题不生效

检查是否正确包装了 `ThemeProvider`：

```tsx
// App.tsx
<ThemeProvider>
  <YourApp />
</ThemeProvider>
```

### 2. 样式不更新

确保导入了主题样式文件：

```tsx
import '../styles/theme.scss';
```

### 3. 系统主题检测失败

检查浏览器是否支持 `prefers-color-scheme` 媒体查询。

## 扩展功能

### 1. 添加新主题模式

可以扩展 `ThemeMode` 类型添加更多主题：

```tsx
export type ThemeMode = 'light' | 'dark' | 'auto' | 'blue' | 'green';
```

### 2. 自定义主题切换动画

修改 `theme.scss` 中的动画配置：

```scss
.theme-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
```

### 3. 主题预设

可以创建多个主题预设供用户选择：

```tsx
const themePresets = {
  default: { colorPrimary: '#1677ff' },
  green: { colorPrimary: '#52c41a' },
  purple: { colorPrimary: '#722ed1' },
};
```

## 更新日志

- **v1.0.0**: 初始版本，支持基础的亮色/暗色主题切换
- **v1.1.0**: 添加跟随系统主题功能
- **v1.2.0**: 优化切换动画，提升用户体验
