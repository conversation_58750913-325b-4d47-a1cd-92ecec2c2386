/**
 * 模型管理相关类型定义
 */

// 模型类型选项
export const MODEL_TYPE_OPTIONS = [
  { label: "语言模型", value: "LLM" },
  { label: "图像模型", value: "IMAGE" },
  { label: "语音模型", value: "AUDIO" },
  { label: "视频模型", value: "VIDEO" },
  { label: "嵌入模型", value: "EMBEDDING" },
  { label: "多模态模型", value: "MULTIMODAL" },
  { label: "其他", value: "OTHER" },
];

// 模型状态枚举
export enum ModelStatus {
  ENABLED = 'enabled',
  DISABLED = 'disabled'
}

// 模型参数类型
export interface ModelParameter {
  name: string;
  value: string;
  type?: string;
  description?: string;
}

// 模型能力配置
export interface ModelCapabilities {
  enableVision?: boolean;
  enableFunctionCalling?: boolean;
  enableInference?: boolean;
  enableOnline?: boolean;
}

// 模型信息
export interface Model {
  id: string;
  name: string;
  type: string;
  vendor: string;
  vendorId: string;
  apiSourceId: string;
  description?: string;
  status: boolean;
  enableVision: boolean;
  enableFunctionCalling: boolean;
  enableInference: boolean;
  enableOnline: boolean;
  parameters: ModelParameter[];
  createdAt: string;
  updatedAt: string;
}

// 创建模型请求
export interface ModelCreateRequest {
  id: string;
  name: string;
  type: string;
  vendorId: string;
  apiSourceId: string;
  description?: string;
  status: boolean;
  capabilities: string[];
  parameters: ModelParameter[];
}

// 更新模型请求
export interface ModelUpdateRequest extends Partial<ModelCreateRequest> {}

// API 源接口
export interface ApiSource {
  id: string;
  name: string;
  type: string;
  apiUrl: string;
  status: boolean;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 模型统计信息
export interface ModelStats {
  total: number;
  enabled: number;
  disabled: number;
  types: number;
}

/**
 * 获取模型类型显示名称
 */
export function getModelTypeDisplayName(type: string): string {
  const option = MODEL_TYPE_OPTIONS.find(opt => opt.value === type);
  return option ? option.label : type;
} 