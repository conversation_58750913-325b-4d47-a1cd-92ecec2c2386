/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_PORT: string
  readonly VITE_HOST: string
  readonly VITE_API_BASE_URL: string
  readonly VITE_API_TIMEOUT: string
  readonly VITE_THEME_PRIMARY_COLOR: string
  readonly VITE_THEME_BORDER_RADIUS: string
  readonly VITE_DEV_TOOLS: string
  readonly VITE_SOURCE_MAP: string
  readonly VITE_ENABLE_CORS: string
  // 其他可能的环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv
} 