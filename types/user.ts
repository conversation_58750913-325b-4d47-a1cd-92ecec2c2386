/**
 * V2 Admin 用户管理相关类型定义
 */

// 用户基础信息接口
export interface User {
  id: number;
  username: string;
  nickname?: string;
  email: string;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  role: 'admin' | 'user';
  status: boolean;
  superAdmin?: boolean;
  avatar?: string;
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
  loginCount?: number;
}

// 用户表单数据接口
export interface UserFormData {
  username: string;
  nickname?: string;
  email: string;
  phone?: string;
  gender?: 'male' | 'female' | 'other';
  role: 'admin' | 'user';
  status: boolean;
  password?: string;
  avatar?: string;
}

// 用户列表查询参数
export interface UserQueryParams {
  page?: number;
  limit?: number;
  keyword?: string;
  role?: string;
  status?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 用户列表响应数据
export interface UserListResponse {
  items: User[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 性别选项
export const GENDER_OPTIONS = [
  { value: 'male', label: '男' },
  { value: 'female', label: '女' },
  { value: 'other', label: '其他' },
] as const;

// 角色选项
export const ROLE_OPTIONS = [
  { value: 'user', label: '普通用户' },
  { value: 'admin', label: '管理员' },
] as const;

// 用户状态选项
export const STATUS_OPTIONS = [
  { value: true, label: '启用' },
  { value: false, label: '禁用' },
] as const;
