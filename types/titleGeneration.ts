/**
 * 标题生成相关类型定义
 */

// 从 model.ts 重新导出 Model 类型
export { Model } from './model';

// 标题生成表单数据类型
export interface TitleGenerationFormData {
  enabled: boolean;
  titleGenerationModelId: string;
  titlePromptTemplate: string;
  temperature: number;
  maxTokens: number;
}

// 标题生成设置数据类型（用于 Zustand store）
export interface TitleGenerationSettingsData {
  enabled: boolean;
  model_id: string;
  model_name: string;
  prompt_template: string;
  max_length: number;
  temperature: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  timeout: number;
  retry_count: number;
  fallback_enabled: boolean;
  fallback_template: string;
  cache_enabled: boolean;
  cache_duration: number;
}

// 模型信息类型
export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  type: string;
  available: boolean;
}

// 标题生成测试请求
export interface TitleGenerationTestRequest {
  content: string;
  model_id: string;
  prompt_template: string;
  max_length: number;
  temperature: number;
  top_p: number;
}

// 标题生成测试响应
export interface TitleGenerationTestResponse {
  title: string;
  success: boolean;
  message?: string;
}

// 标题生成配置
export interface TitleGenerationConfig {
  enabled: boolean;
  modelId: string;
  promptTemplate: string;
  temperature: number;
  maxTokens: number;
  timeout: number;
  retryCount: number;
  fallbackEnabled: boolean;
  fallbackTemplate: string;
}

// 标题生成结果
export interface TitleGenerationResult {
  title: string;
  success: boolean;
  error?: string;
  duration?: number;
  modelUsed?: string;
}

// 标题生成统计
export interface TitleGenerationStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  mostUsedModel: string;
}

// 标题生成历史记录
export interface TitleGenerationHistory {
  id: string;
  content: string;
  generatedTitle: string;
  modelId: string;
  temperature: number;
  success: boolean;
  error?: string;
  duration: number;
  createdAt: string;
}

// 标题生成验证规则
export interface TitleGenerationValidation {
  minLength: number;
  maxLength: number;
  allowedCharacters: string[];
  forbiddenWords: string[];
  requireChinese: boolean;
}

// 标题生成性能指标
export interface TitleGenerationMetrics {
  requestsPerMinute: number;
  averageLatency: number;
  errorRate: number;
  modelPerformance: Record<string, {
    requests: number;
    successRate: number;
    averageLatency: number;
  }>;
}
