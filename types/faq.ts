/**
 * FAQ 相关类型定义
 */

// FAQ 建议状态
export type FAQStatus = 'active' | 'inactive' | 'draft';

// FAQ 分类
export type FAQCategory = 'general' | 'technical' | 'billing' | 'support' | 'product' | 'other';

// FAQ 建议数据类型
export interface FAQSuggestion {
  id: string;
  title: string;
  content: string;
  category: FAQCategory;
  status: FAQStatus;
  icon?: string;
  order: number;
  tags?: string[];
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
}

// 创建 FAQ 建议请求
export interface CreateFAQSuggestionRequest {
  title: string;
  content: string;
  category: FAQCategory;
  status?: FAQStatus;
  icon?: string;
  order?: number;
  tags?: string[];
}

// 更新 FAQ 建议请求
export interface UpdateFAQSuggestionRequest extends Partial<CreateFAQSuggestionRequest> {
  id: string;
}

// FAQ 查询参数
export interface FAQQueryParams {
  page?: number;
  pageSize?: number;
  category?: FAQCategory;
  status?: FAQStatus;
  keyword?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'order' | 'title';
  sortOrder?: 'asc' | 'desc';
}

// FAQ 分页响应
export interface FAQPaginatedResponse {
  data: FAQSuggestion[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// FAQ 统计信息
export interface FAQStats {
  total: number;
  active: number;
  inactive: number;
  draft: number;
  byCategory: Record<FAQCategory, number>;
}

// 图标选项
export const ICON_OPTIONS = [
  { label: '问号', value: 'QuestionCircleOutlined' },
  { label: '信息', value: 'InfoCircleOutlined' },
  { label: '警告', value: 'ExclamationCircleOutlined' },
  { label: '设置', value: 'SettingOutlined' },
  { label: '用户', value: 'UserOutlined' },
  { label: '文档', value: 'FileTextOutlined' },
  { label: '工具', value: 'ToolOutlined' },
  { label: '安全', value: 'SafetyOutlined' },
  { label: '钱包', value: 'WalletOutlined' },
  { label: '客服', value: 'CustomerServiceOutlined' },
  { label: '产品', value: 'AppstoreOutlined' },
  { label: '其他', value: 'EllipsisOutlined' },
] as const;

// 分类选项
export const CATEGORY_OPTIONS = [
  { label: '常规问题', value: 'general' },
  { label: '技术问题', value: 'technical' },
  { label: '计费问题', value: 'billing' },
  { label: '支持服务', value: 'support' },
  { label: '产品功能', value: 'product' },
  { label: '其他问题', value: 'other' },
] as const;

// 状态选项
export const STATUS_OPTIONS = [
  { label: '启用', value: 'active' },
  { label: '禁用', value: 'inactive' },
  { label: '草稿', value: 'draft' },
] as const;

// 状态颜色映射
export const STATUS_COLORS: Record<FAQStatus, string> = {
  active: 'green',
  inactive: 'red',
  draft: 'orange',
};

// 分类颜色映射
export const CATEGORY_COLORS: Record<FAQCategory, string> = {
  general: 'blue',
  technical: 'purple',
  billing: 'gold',
  support: 'cyan',
  product: 'green',
  other: 'default',
};
