// 知识库管理相关类型定义

// 知识库基础信息
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  type: 'document' | 'qa' | 'structured' | 'web';
  status: 'ready' | 'processing' | 'error' | 'inactive';
  document_count: number;
  vector_count: number;
  total_size: number;                  // 总大小（字节）
  embedding_model: string;             // 嵌入模型
  tags: string[];                      // 标签
  created_at: string;
  updated_at: string;
  created_by: string;
  owner_id: string;
}

// 文档信息
export interface Document {
  id: string;
  knowledge_base_id: string;
  name: string;
  type: 'pdf' | 'docx' | 'txt' | 'md' | 'html' | 'csv' | 'xlsx';
  size: number;                        // 文件大小（字节）
  status: 'processing' | 'completed' | 'failed';
  chunk_count: number;                 // 分块数量
  error_message?: string;              // 错误信息
  upload_progress?: number;            // 上传进度 (0-100)
  processing_progress?: number;        // 处理进度 (0-100)
  created_at: string;
  updated_at: string;
}

// 文档分块信息
export interface DocumentChunk {
  id: string;
  document_id: string;
  knowledge_base_id: string;
  content: string;                     // 分块内容
  metadata: Record<string, any>;       // 元数据
  embedding?: number[];                // 向量嵌入
  status: 'processing' | 'completed' | 'failed';
  error_message?: string;              // 错误信息
  chunk_index: number;                 // 分块索引
  token_count: number;                 // Token数量
  similarity_score?: number;           // 相似度分数（搜索时使用）
  created_at: string;
  updated_at: string;
}

// 知识库统计信息
export interface KnowledgeBaseStats {
  total_count: number;                 // 总知识库数
  active_count: number;                // 活跃知识库数
  total_documents: number;             // 总文档数
  total_size: number;                  // 总存储大小
  processing_count: number;            // 处理中的知识库数
  error_count: number;                 // 错误状态的知识库数
}

// 创建知识库请求
export interface CreateKnowledgeBaseRequest {
  name: string;
  description: string;
  type: 'document' | 'qa' | 'structured' | 'web';
  embedding_model: string;
  tags?: string[];
}

// 更新知识库请求
export interface UpdateKnowledgeBaseRequest extends Partial<CreateKnowledgeBaseRequest> {}

// 文档上传请求
export interface UploadDocumentRequest {
  knowledge_base_id: string;
  files: File[];
}

// 查询参数
export interface KnowledgeBaseQueryParams {
  search?: string;                     // 搜索关键词
  type?: string;                       // 知识库类型
  status?: string;                     // 状态筛选
  embedding_model?: string;            // 嵌入模型筛选
  tag?: string;                        // 标签筛选
  owner_id?: string;                   // 所有者筛选
  sort_by?: 'name' | 'created_at' | 'updated_at' | 'document_count' | 'total_size';
  sort_order?: 'asc' | 'desc';
  page?: number;
  page_size?: number;
}

// 文档查询参数
export interface DocumentQueryParams {
  knowledge_base_id: string;
  search?: string;
  type?: string;
  status?: string;
  sort_by?: 'name' | 'created_at' | 'size' | 'chunk_count';
  sort_order?: 'asc' | 'desc';
  page?: number;
  page_size?: number;
}

// 文档分块查询参数
export interface DocumentChunkQueryParams {
  document_id?: string;
  knowledge_base_id?: string;
  search?: string;                     // 搜索分块内容
  status?: string;
  sort_by?: 'chunk_index' | 'created_at' | 'token_count';
  sort_order?: 'asc' | 'desc';
  page?: number;
  page_size?: number;
}

// API响应类型
export interface KnowledgeBaseListResponse {
  success: boolean;
  message: string;
  data: {
    items: KnowledgeBase[];
    total: number;
    page: number;
    page_size: number;
  };
}

export interface KnowledgeBaseResponse {
  success: boolean;
  message: string;
  data: KnowledgeBase;
}

export interface DocumentListResponse {
  success: boolean;
  message: string;
  data: {
    items: Document[];
    total: number;
    page: number;
    page_size: number;
  };
}

export interface DocumentChunkListResponse {
  success: boolean;
  message: string;
  data: {
    items: DocumentChunk[];
    total: number;
    page: number;
    page_size: number;
  };
}

export interface KnowledgeBaseStatsResponse {
  success: boolean;
  message: string;
  data: KnowledgeBaseStats;
}

// RAG测试相关类型
export interface RAGTestRequest {
  query: string;                       // 查询文本
  knowledge_base_id: string;           // 知识库ID
  top_k?: number;                      // 返回结果数量
  similarity_threshold?: number;       // 相似度阈值
}

export interface RAGTestResult {
  id: string;                          // 结果ID
  document_id: string;                 // 文档ID
  document_name: string;               // 文档名称
  chunk_id: string;                    // 分块ID
  content: string;                     // 匹配的内容片段
  similarity_score: number;            // 相似度评分 (0-1)
  metadata: {
    page?: number;                     // 页码
    section?: string;                  // 章节
    title?: string;                    // 标题
    source?: string;                   // 来源
    [key: string]: any;                // 其他元数据
  };
  highlighted_content?: string;        // 高亮显示的内容
}

export interface RAGTestResponse {
  success: boolean;
  message: string;
  data: {
    query: string;                     // 原始查询
    knowledge_base_id: string;         // 知识库ID
    knowledge_base_name: string;       // 知识库名称
    results: RAGTestResult[];          // 检索结果
    total_results: number;             // 总结果数
    response_time: number;             // 响应时间（毫秒）
    embedding_time: number;            // 嵌入计算时间（毫秒）
    search_time: number;               // 搜索时间（毫秒）
  };
}

// 知识库类型选项
export const KNOWLEDGE_BASE_TYPES = [
  { key: 'document', label: '文档库', description: '支持PDF、Word、文本等文档格式' },
  { key: 'qa', label: '问答库', description: '结构化的问答对数据' },
  { key: 'structured', label: '结构化库', description: '表格、数据库等结构化数据' },
  { key: 'web', label: '网页库', description: '网页内容和在线资源' }
] as const;

// 文档类型选项
export const DOCUMENT_TYPES = [
  { key: 'pdf', label: 'PDF文档', icon: '📄' },
  { key: 'docx', label: 'Word文档', icon: '📝' },
  { key: 'txt', label: '文本文件', icon: '📄' },
  { key: 'md', label: 'Markdown', icon: '📋' },
  { key: 'html', label: 'HTML文件', icon: '🌐' },
  { key: 'csv', label: 'CSV文件', icon: '📊' },
  { key: 'xlsx', label: 'Excel文件', icon: '📈' }
] as const;

// 嵌入模型选项
export const EMBEDDING_MODELS = [
  { key: 'text-embedding-ada-002', label: 'OpenAI Ada-002', provider: 'OpenAI' },
  { key: 'text-embedding-3-small', label: 'OpenAI Embedding-3-Small', provider: 'OpenAI' },
  { key: 'text-embedding-3-large', label: 'OpenAI Embedding-3-Large', provider: 'OpenAI' },
  { key: 'bge-large-zh-v1.5', label: 'BGE Large Chinese', provider: 'BAAI' },
  { key: 'bge-base-zh-v1.5', label: 'BGE Base Chinese', provider: 'BAAI' },
  { key: 'm3e-base', label: 'M3E Base', provider: 'Moka' },
  { key: 'm3e-large', label: 'M3E Large', provider: 'Moka' }
] as const;
