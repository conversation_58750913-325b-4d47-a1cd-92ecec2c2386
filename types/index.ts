// V2 Admin 类型定义
// 基于demo项目的类型系统设计

export interface User {
  id: string;
  username: string;
  email?: string;
  role: string;
  avatar?: string;
  isSuperAdmin: boolean;
}

export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  disabled?: boolean;
  badge?: string | number;
}

export interface SidebarProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  menuItems: MenuItem[];
  selectedKey?: string;
  onMenuSelect?: (key: string, path?: string) => void;
  theme?: 'light' | 'dark';
  width?: number;
  collapsedWidth?: number;
}

export interface HeaderProps {
  collapsed: boolean;
  onCollapse: (collapsed: boolean) => void;
  user?: User;
  title?: string;
  showBreadcrumb?: boolean;
  breadcrumbItems?: BreadcrumbItem[];
  breadcrumbComponent?: React.ReactNode;
  actions?: React.ReactNode;
}

export interface BreadcrumbItem {
  title: string;
  path?: string;
  icon?: React.ReactNode;
  key?: string;
}

// 路由配置相关类型
export interface RouteConfig {
  path: string;
  element?: React.ComponentType<any>;
  children?: RouteConfig[];
  meta?: RouteMeta;
  index?: boolean;
  redirect?: string;
}

export interface RouteMeta {
  title: string;
  icon?: React.ReactNode;
  breadcrumb?: boolean;
  hideInMenu?: boolean;
  requireAuth?: boolean;
  roles?: string[];
  description?: string;
  keywords?: string[];
}

// 面包屑导航相关类型
export interface BreadcrumbConfig {
  showHome?: boolean;
  homeTitle?: string;
  homePath?: string;
  separator?: React.ReactNode;
  maxItems?: number;
  showIcon?: boolean;
}

// 路由历史记录类型
export interface RouteHistoryItem {
  path: string;
  title: string;
  timestamp: number;
  meta?: RouteMeta;
}

// 路由导航相关类型
export interface RouteNavigation {
  goBack: () => void;
  goForward: () => void;
  canGoBack: boolean;
  canGoForward: boolean;
  navigateTo: (path: string) => void;
  replace: (path: string) => void;
}

// 路由状态类型
export interface RouteState {
  currentPath: string;
  currentMeta: RouteMeta | null;
  isValidPath: boolean;
  previousPath: string | null;
  isLoading: boolean;
  error?: string;
}

// 路由守卫类型
export interface RouteGuard {
  canActivate?: (route: RouteConfig) => boolean | Promise<boolean>;
  canDeactivate?: (route: RouteConfig) => boolean | Promise<boolean>;
  beforeEnter?: (route: RouteConfig) => void | Promise<void>;
  afterEnter?: (route: RouteConfig) => void | Promise<void>;
}

// 路由中间件类型
export interface RouteMiddleware {
  name: string;
  execute: (context: RouteMiddlewareContext) => void | Promise<void>;
  priority?: number;
}

export interface RouteMiddlewareContext {
  route: RouteConfig;
  from?: RouteConfig;
  next: (error?: Error) => void;
  abort: (reason?: string) => void;
}

export interface LayoutProps {
  children?: React.ReactNode;
  sidebarProps?: Partial<SidebarProps>;
  headerProps?: Partial<HeaderProps>;
  className?: string;
  style?: React.CSSProperties;
}

export interface ThemeConfig {
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
  sidebarBg: string;
  headerBg: string;
  menuItemHoverBg: string;
  menuItemActiveBg: string;
}

export interface BreakpointConfig {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  xxl: number;
}

export interface ResponsiveConfig {
  breakpoints: BreakpointConfig;
  sidebarCollapsedBreakpoint: number;
  mobileBreakpoint: number;
}

// 布局状态管理
export interface LayoutState {
  sidebarCollapsed: boolean;
  mobileDrawerOpen: boolean;
  currentBreakpoint: keyof BreakpointConfig;
  isMobile: boolean;
  sidebarWidth: number;
  theme: 'light' | 'dark';
}

export interface LayoutActions {
  setSidebarCollapsed: (collapsed: boolean) => void;
  setMobileDrawerOpen: (open: boolean) => void;
  setCurrentBreakpoint: (breakpoint: keyof BreakpointConfig) => void;
  setIsMobile: (isMobile: boolean) => void;
  setSidebarWidth: (width: number) => void;
  setTheme: (theme: 'light' | 'dark') => void;
  toggleSidebar: () => void;
  toggleMobileDrawer: () => void;
  toggleTheme: () => void;
}

// 组件通用属性
export interface BaseComponentProps {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
}

// 菜单数据结构（从现有项目提取）
export interface MenuData {
  dashboard: MenuItem;
  modelManagement: MenuItem;
  userManagement: MenuItem;
  systemSettings: MenuItem;
  monitoring: MenuItem;
  knowledgeBase: MenuItem;
  assistantManagement: MenuItem;
}

// 响应式Hook返回类型
export interface UseResponsiveReturn {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLarge: boolean;
  currentBreakpoint: keyof BreakpointConfig;
  screenWidth: number;
  screenHeight: number;
}

// 主题Hook返回类型
export interface UseThemeReturn {
  theme: 'light' | 'dark';
  themeConfig: ThemeConfig;
  toggleTheme: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
}

// 侧边栏Hook返回类型
export interface UseSidebarReturn {
  collapsed: boolean;
  width: number;
  collapsedWidth: number;
  toggle: () => void;
  collapse: () => void;
  expand: () => void;
  setCollapsed: (collapsed: boolean) => void;
  setWidth: (width: number) => void;
}
