// 助手管理相关类型定义 - v2-admin版本

// RAG配置
export interface RAGConfig {
  retrieval_strategy: 'similarity' | 'keyword' | 'hybrid';  // 检索策略
  similarity_threshold: number;       // 相似度阈值 (0-1)
  top_k: number;                      // 检索Top-K结果数量
  max_context_length: number;         // 最大上下文长度
  rerank_enabled: boolean;            // 是否启用重排序
  citation_enabled: boolean;          // 是否显示引用来源
}

// 助手配置（扩展RAG支持）
export interface AssistantConfig {
  system_prompt: string;
  temperature: number;
  max_tokens: number;
  top_p?: number;

  // RAG相关配置
  rag_config?: RAGConfig;
}

// 知识库信息
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string;
  document_count: number;
  vector_count: number;
  status: 'ready' | 'processing' | 'error';
  created_at: string;
  updated_at: string;
}

// 纯助手模型（扩展知识库RAG支持）
export interface Assistant {
  id: string;
  name: string;
  description: string;
  category: string;
  tags: string[];
  config: AssistantConfig;
  model_id: string;

  // 权限和状态
  is_public: boolean;    // 是否公开（其他用户可见和复制）
  is_active: boolean;    // 是否启用
  is_system: boolean;    // 是否为系统预置助手（仅用于标识，不影响功能）

  // 所有权
  owner_id: string;      // 创建者ID
  created_by: string;    // 创建者名称

  // 知识库集成（RAG支持）
  knowledge_base_ids: string[];        // 关联的知识库ID列表
  rag_enabled: boolean;                // 是否启用RAG功能
  knowledge_bases?: KnowledgeBase[];   // 关联的知识库详情（查询时填充）

  // 使用统计
  usage_count: number;
  last_used_at: string | null;
  message_count: number;
  total_tokens: number;
  rag_query_count: number;             // RAG查询次数
  average_rating: number;
  rating_count: number;

  // 时间戳
  created_at: string;
  updated_at: string;
}

// 助手分类
export interface AssistantCategory {
  key: string;
  label: string;
  description: string;
  count?: number;
}

// 创建助手请求（扩展知识库支持）
export interface CreateAssistantRequest {
  name: string;
  description: string;
  category: string;
  tags?: string[];
  config: AssistantConfig;
  model_id: string;
  is_public?: boolean;
  is_active?: boolean;

  // 知识库集成
  knowledge_base_ids?: string[];
  rag_enabled?: boolean;
}

// 更新助手请求
export interface UpdateAssistantRequest extends Partial<CreateAssistantRequest> {}

// 查询参数
export interface AssistantQueryParams {
  page?: number;
  page_size?: number;
  keyword?: string;
  category?: string;
  is_public?: boolean;
  rag_enabled?: boolean;
  owner_id?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data: T;
}

export interface AssistantListResponse {
  success: boolean;
  message: string;
  data: {
    items: Assistant[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
  };
}

export interface CategoriesResponse {
  success: boolean;
  message: string;
  data: AssistantCategory[];
}

export interface AssistantResponse {
  success: boolean;
  message: string;
  data: Assistant;
}
