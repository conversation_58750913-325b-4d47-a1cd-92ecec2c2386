import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');

  // 从环境变量获取配置，提供默认值
  const port = parseInt(env.VITE_PORT || '9982');
  const host = env.VITE_HOST || '0.0.0.0';
  const enableCors = env.VITE_ENABLE_CORS !== 'false';
  const useProxy = env.VITE_USE_PROXY === 'true';
  const proxyTarget = env.VITE_PROXY_TARGET || 'http://localhost:8081';

  return {
    plugins: [react()],
    server: {
      port,
      host: host === '0.0.0.0' ? true : host,
      open: true,
      cors: enableCors,
      // SPA路由支持 - 所有路由都返回index.html
      historyApiFallback: true,
      // 动态代理配置
      ...(useProxy && {
        proxy: {
          '/api': {
            target: proxyTarget,
            changeOrigin: true,
            secure: false,
            configure: (proxy, options) => {
              console.log('🔄 代理配置已启用');
              console.log('🎯 代理目标:', options.target);
            }
          }
        }
      })
    },
    preview: {
      port,
      host: host === '0.0.0.0' ? true : host
    },
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        '@components': resolve(__dirname, 'components'),
        '@layouts': resolve(__dirname, 'layouts'),
        '@pages': resolve(__dirname, 'pages'),
        '@hooks': resolve(__dirname, 'hooks'),
        '@styles': resolve(__dirname, 'styles'),
        '@utils': resolve(__dirname, 'utils')
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./styles/variables" as *;`,
          charset: false
        }
      }
    },
    build: {
      outDir: 'dist',
      sourcemap: env.VITE_SOURCE_MAP === 'true',
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom'],
            antd: ['antd', '@ant-design/icons'],
            router: ['react-router-dom']
          }
        }
      }
    }
  };
})
