{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,

    /* Path mapping */
    "baseUrl": ".",
    "types": ["vite/client", "node"],
    "paths": {
      "@/*": ["src/*"],
      "@components/*": ["components/*"],
      "@layouts/*": ["layouts/*"],
      "@pages/*": ["pages/*"],
      "@hooks/*": ["hooks/*"],
      "@styles/*": ["styles/*"],
      "@utils/*": ["utils/*"]
    }
  },
  "include": [
    "src",
    "components",
    "layouts",
    "pages",
    "hooks",
    "styles",
    "utils",
    "*.tsx"
  ],
  "exclude": [
    "vite.config.ts"
  ],
  "references": [{ "path": "./tsconfig.node.json" }]
}