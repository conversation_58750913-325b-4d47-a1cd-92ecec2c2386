/**
 * 验证码设置状态管理 Slice
 * 管理验证码配置、测试验证码生成等功能
 */

import { StateCreator } from 'zustand';

// 验证码设置数据类型
export interface CaptchaSettingsData {
  enabled: boolean;
  type: 'image' | 'audio' | 'math' | 'text';
  difficulty: 'easy' | 'medium' | 'hard';
  length: number;
  width: number;
  height: number;
  noise: number;
  background: string;
  font_size: number;
  font_color: string;
  expire_time: number;
  max_attempts: number;
  case_sensitive: boolean;
  refresh_enabled: boolean;
  audio_enabled: boolean;
  math_enabled: boolean;
}

// 验证码设置状态接口
export interface CaptchaSettingsState {
  // 验证码设置数据
  captchaSettings: CaptchaSettingsData | null;
  
  // UI 状态
  captchaLoading: boolean;
  captchaSaving: boolean;
  captchaError: string | null;
  
  // 缓存和持久化
  captchaLastUpdated: number | null;
  captchaIsDirty: boolean;
  
  // 验证码特定状态
  testCaptcha: string;
  testCaptchaLoading: boolean;
  
  // 验证码操作方法
  loadCaptchaSettings: () => Promise<void>;
  updateCaptchaSettings: (settings: Partial<CaptchaSettingsData>) => Promise<void>;
  setCaptchaSettings: (settings: CaptchaSettingsData) => void;
  setCaptchaLoading: (loading: boolean) => void;
  setCaptchaSaving: (saving: boolean) => void;
  setCaptchaError: (error: string | null) => void;
  markCaptchaDirty: (dirty: boolean) => void;
  resetCaptchaSettings: () => void;
  getCaptchaDefaultSettings: () => CaptchaSettingsData;
  
  // 验证码特定操作
  generateTestCaptcha: () => Promise<void>;
  validateCaptcha: (code: string) => Promise<boolean>;
  refreshCaptcha: () => Promise<void>;
}

// 默认验证码设置
const DEFAULT_CAPTCHA_SETTINGS: CaptchaSettingsData = {
  enabled: true,
  type: 'image',
  difficulty: 'medium',
  length: 4,
  width: 120,
  height: 40,
  noise: 3,
  background: '#f0f0f0',
  font_size: 18,
  font_color: '#333333',
  expire_time: 300, // 5分钟
  max_attempts: 3,
  case_sensitive: false,
  refresh_enabled: true,
  audio_enabled: false,
  math_enabled: false,
};

// 存储键
const STORAGE_KEY = 'v2-admin-captcha-settings';

/**
 * 验证设置数据
 */
const validateCaptchaSettings = (settings: Partial<CaptchaSettingsData>): string[] => {
  const errors: string[] = [];
  
  if (settings.length !== undefined) {
    if (settings.length < 3 || settings.length > 8) {
      errors.push('验证码长度必须在3-8之间');
    }
  }
  
  if (settings.width !== undefined) {
    if (settings.width < 80 || settings.width > 300) {
      errors.push('验证码宽度必须在80-300之间');
    }
  }
  
  if (settings.height !== undefined) {
    if (settings.height < 30 || settings.height > 100) {
      errors.push('验证码高度必须在30-100之间');
    }
  }
  
  if (settings.expire_time !== undefined) {
    if (settings.expire_time < 60 || settings.expire_time > 1800) {
      errors.push('过期时间必须在60-1800秒之间');
    }
  }
  
  if (settings.max_attempts !== undefined) {
    if (settings.max_attempts < 1 || settings.max_attempts > 10) {
      errors.push('最大尝试次数必须在1-10之间');
    }
  }
  
  return errors;
};

/**
 * 从 localStorage 加载设置缓存
 */
const loadFromCache = (): { settings: CaptchaSettingsData; lastUpdated: number } | null => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY);
    if (cached) {
      const { settings, lastUpdated } = JSON.parse(cached);
      // 检查缓存是否过期（24小时）
      if (Date.now() - lastUpdated < 24 * 60 * 60 * 1000) {
        return { settings, lastUpdated };
      }
    }
  } catch (error) {
    console.warn('Failed to load captcha settings from cache:', error);
  }
  return null;
};

/**
 * 保存设置到 localStorage
 */
const saveToCache = (settings: CaptchaSettingsData) => {
  try {
    const cacheData = {
      settings,
      lastUpdated: Date.now(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Failed to save captcha settings to cache:', error);
  }
};

/**
 * 验证码设置 Slice 创建器
 */
export const createCaptchaSettingsSlice: StateCreator<
  CaptchaSettingsState,
  [],
  [],
  CaptchaSettingsState
> = (set, get) => ({
  // 初始状态
  captchaSettings: null,
  captchaLoading: false,
  captchaSaving: false,
  captchaError: null,
  captchaLastUpdated: null,
  captchaIsDirty: false,
  testCaptcha: '',
  testCaptchaLoading: false,

  /**
   * 获取默认设置
   */
  getCaptchaDefaultSettings: () => ({ ...DEFAULT_CAPTCHA_SETTINGS }),

  /**
   * 设置验证码设置数据
   */
  setCaptchaSettings: (settings: CaptchaSettingsData) => {
    set({ 
      captchaSettings: settings,
      captchaIsDirty: true,
      captchaError: null 
    });
  },

  /**
   * 设置加载状态
   */
  setCaptchaLoading: (loading: boolean) => {
    set({ captchaLoading: loading });
  },

  /**
   * 设置保存状态
   */
  setCaptchaSaving: (saving: boolean) => {
    set({ captchaSaving: saving });
  },

  /**
   * 设置错误信息
   */
  setCaptchaError: (error: string | null) => {
    set({ captchaError: error });
  },

  /**
   * 标记是否有未保存的更改
   */
  markCaptchaDirty: (dirty: boolean) => {
    set({ captchaIsDirty: dirty });
  },

  /**
   * 加载验证码设置
   */
  loadCaptchaSettings: async () => {
    // 先尝试从缓存加载
    const cached = loadFromCache();
    if (cached) {
      set({ 
        captchaSettings: cached.settings,
        captchaLastUpdated: cached.lastUpdated,
        captchaIsDirty: false,
        captchaError: null 
      });
      console.log('✅ Captcha settings loaded from cache');
      return;
    }
    
    try {
      set({ captchaLoading: true, captchaError: null });
      
      // 动态导入服务
      const { captchaService } = await import('../../services/captchaService');
      const response = await captchaService.getSettings();
      
      const settings = { ...DEFAULT_CAPTCHA_SETTINGS, ...response.data };
      
      set({ 
        captchaSettings: settings,
        captchaLastUpdated: Date.now(),
        captchaIsDirty: false,
        captchaError: null 
      });
      
      // 保存到缓存
      saveToCache(settings);
    } catch (error: any) {
      console.error('Failed to load captcha settings:', error);
      set({ 
        captchaError: error.message || '加载验证码设置失败',
        captchaSettings: { ...DEFAULT_CAPTCHA_SETTINGS }
      });
    } finally {
      set({ captchaLoading: false });
    }
  },

  /**
   * 更新验证码设置
   */
  updateCaptchaSettings: async (newSettings: Partial<CaptchaSettingsData>) => {
    const { captchaSettings } = get();
    
    if (!captchaSettings) {
      set({ captchaError: '验证码设置数据未加载' });
      return;
    }
    
    // 合并新设置
    const updatedSettings = { ...captchaSettings, ...newSettings };
    
    // 验证设置
    const validationErrors = validateCaptchaSettings(updatedSettings);
    if (validationErrors.length > 0) {
      set({ captchaError: validationErrors.join('; ') });
      return;
    }
    
    try {
      set({ captchaSaving: true, captchaError: null });
      
      // 动态导入服务
      const { captchaService } = await import('../../services/captchaService');
      await captchaService.updateSettings(updatedSettings);
      
      set({ 
        captchaSettings: updatedSettings,
        captchaLastUpdated: Date.now(),
        captchaIsDirty: false,
        captchaError: null 
      });
      
      // 保存到缓存
      saveToCache(updatedSettings);

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('验证码设置保存成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to update captcha settings:', error);
      set({ captchaError: error.message || '保存验证码设置失败' });
    } finally {
      set({ captchaSaving: false });
    }
  },

  /**
   * 重置验证码设置
   */
  resetCaptchaSettings: () => {
    set({
      captchaSettings: { ...DEFAULT_CAPTCHA_SETTINGS },
      captchaLoading: false,
      captchaSaving: false,
      captchaError: null,
      captchaLastUpdated: null,
      captchaIsDirty: false,
      testCaptcha: '',
      testCaptchaLoading: false,
    });
    
    // 清除缓存
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear captcha settings cache:', error);
    }
  },

  /**
   * 生成测试验证码
   */
  generateTestCaptcha: async () => {
    try {
      set({ testCaptchaLoading: true, captchaError: null });
      
      // 动态导入服务
      const { captchaService } = await import('../../services/captchaService');
      const response = await captchaService.generateCaptcha();
      
      set({ 
        testCaptcha: response.data?.image || '',
        captchaError: null 
      });
    } catch (error: any) {
      console.error('Failed to generate test captcha:', error);
      set({ captchaError: error.message || '生成测试验证码失败' });
    } finally {
      set({ testCaptchaLoading: false });
    }
  },

  /**
   * 验证验证码
   */
  validateCaptcha: async (code: string): Promise<boolean> => {
    try {
      // 动态导入服务
      const { captchaService } = await import('../../services/captchaService');
      const response = await captchaService.validateCaptcha(code);
      
      return response.data?.valid || false;
    } catch (error: any) {
      console.error('Failed to validate captcha:', error);
      set({ captchaError: error.message || '验证码验证失败' });
      return false;
    }
  },

  /**
   * 刷新验证码
   */
  refreshCaptcha: async () => {
    const { generateTestCaptcha } = get();
    await generateTestCaptcha();
  },
});
