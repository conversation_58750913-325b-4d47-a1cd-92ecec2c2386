import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// 主题模式类型
export type ThemeMode = 'light' | 'dark' | 'auto';

// 主题状态接口
export interface ThemeState {
  mode: ThemeMode;
  isSystemDark: boolean;
  actualTheme: 'light' | 'dark'; // 计算后的实际主题
}

// 主题操作接口
export interface ThemeActions {
  setMode: (mode: ThemeMode) => void;
  setSystemDark: (isSystemDark: boolean) => void;
  toggleTheme: () => void;
  getActualTheme: () => 'light' | 'dark';
}

// 计算实际主题的辅助函数
const calculateActualTheme = (mode: ThemeMode, isSystemDark: boolean): 'light' | 'dark' => {
  if (mode === 'auto') {
    return isSystemDark ? 'dark' : 'light';
  }
  return mode;
};

// 从localStorage获取初始主题模式
const getInitialMode = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  
  const stored = localStorage.getItem('theme-mode');
  if (stored && ['light', 'dark', 'auto'].includes(stored)) {
    return stored as ThemeMode;
  }
  return 'auto'; // 默认使用自动模式
};

// 检测系统是否为暗色模式
const getSystemDarkMode = (): boolean => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-color-scheme: dark)').matches;
};

// 初始状态
const initialIsSystemDark = getSystemDarkMode();
const initialMode = getInitialMode();

// 创建主题store
export const useThemeStore = create<ThemeState & ThemeActions>()(
  devtools(
    (set, get) => ({
      // 初始状态
      mode: initialMode,
      isSystemDark: initialIsSystemDark,
      actualTheme: calculateActualTheme(initialMode, initialIsSystemDark),

      // 设置主题模式
      setMode: (mode: ThemeMode) => {
        // 保存到localStorage
        localStorage.setItem('theme-mode', mode);
        
        const { isSystemDark } = get();
        const actualTheme = calculateActualTheme(mode, isSystemDark);
        
        // 更新HTML元素的class
        const html = document.documentElement;
        html.classList.remove('light', 'dark');
        html.classList.add(actualTheme);
        
        set({
          mode,
          actualTheme,
        });
      },

      // 设置系统暗色模式状态
      setSystemDark: (isSystemDark: boolean) => {
        const { mode } = get();
        const actualTheme = calculateActualTheme(mode, isSystemDark);
        
        // 如果是auto模式，需要更新HTML元素的class
        if (mode === 'auto') {
          const html = document.documentElement;
          html.classList.remove('light', 'dark');
          html.classList.add(actualTheme);
        }
        
        set({
          isSystemDark,
          actualTheme,
        });
      },

      // 切换主题（在light和dark之间切换）
      toggleTheme: () => {
        const { mode } = get();
        const newMode = mode === 'dark' ? 'light' : 'dark';
        get().setMode(newMode);
      },

      // 获取当前实际主题
      getActualTheme: () => {
        const { actualTheme } = get();
        return actualTheme;
      },
    }),
    {
      name: 'theme-store',
    }
  )
);

// 初始化主题
export const initializeTheme = () => {
  const { mode, isSystemDark, setSystemDark, setMode } = useThemeStore.getState();
  
  // 设置初始HTML class
  const actualTheme = calculateActualTheme(mode, isSystemDark);
  const html = document.documentElement;
  html.classList.remove('light', 'dark');
  html.classList.add(actualTheme);
  
  // 监听系统主题变化
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      setSystemDark(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    // 返回清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }
};

// Hook for theme values
export const useTheme = () => {
  const mode = useThemeStore(state => state.mode);
  const actualTheme = useThemeStore(state => state.actualTheme);
  const isSystemDark = useThemeStore(state => state.isSystemDark);
  
  return {
    mode,
    actualTheme,
    isSystemDark,
    isDark: actualTheme === 'dark',
    isLight: actualTheme === 'light',
  };
};

// Hook for theme actions
export const useThemeActions = () => {
  const setMode = useThemeStore(state => state.setMode);
  const toggleTheme = useThemeStore(state => state.toggleTheme);
  const getActualTheme = useThemeStore(state => state.getActualTheme);
  
  return {
    setMode,
    toggleTheme,
    getActualTheme,
  };
};