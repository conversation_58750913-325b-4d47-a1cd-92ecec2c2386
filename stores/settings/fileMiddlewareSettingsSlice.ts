/**
 * 文件中间件设置状态管理 Slice
 * 管理文件中间件配置、存储设置、安全功能等
 */

import { StateCreator } from 'zustand';

// 文件中间件设置数据类型
export interface FileMiddlewareSettingsData {
  enabled: boolean;
  maxFileSize: number; // MB
  allowedExtensions: string[];
  storageType: "local" | "s3" | "oss" | "cos";
  storagePath: string;
  tempPath: string;
  compressionEnabled: boolean;
  compressionQuality?: number;
  virusScanEnabled: boolean;
  encryptFilenames: boolean;
  forbiddenPatterns?: string[];
}

// 文件中间件设置状态接口
export interface FileMiddlewareSettingsState {
  // 文件中间件设置数据
  fileMiddlewareSettings: FileMiddlewareSettingsData | null;
  
  // UI 状态
  fileMiddlewareLoading: boolean;
  fileMiddlewareSaving: boolean;
  fileMiddlewareError: string | null;
  
  // 缓存和持久化
  fileMiddlewareLastUpdated: number | null;
  fileMiddlewareIsDirty: boolean;
  
  // 文件中间件特定状态
  testResult: string;
  testing: boolean;
  
  // 文件中间件操作方法
  loadFileMiddlewareSettings: () => Promise<void>;
  updateFileMiddlewareSettings: (settings: Partial<FileMiddlewareSettingsData>) => Promise<void>;
  setFileMiddlewareSettings: (settings: FileMiddlewareSettingsData) => void;
  setFileMiddlewareLoading: (loading: boolean) => void;
  setFileMiddlewareSaving: (saving: boolean) => void;
  setFileMiddlewareError: (error: string | null) => void;
  markFileMiddlewareDirty: (dirty: boolean) => void;
  resetFileMiddlewareSettings: () => void;
  getFileMiddlewareDefaultSettings: () => FileMiddlewareSettingsData;
  
  // 文件中间件特定操作
  testFileMiddleware: (testType: 'upload' | 'scan' | 'compression') => Promise<void>;
}

// 默认文件中间件设置
const DEFAULT_FILE_MIDDLEWARE_SETTINGS: FileMiddlewareSettingsData = {
  enabled: true,
  maxFileSize: 10,
  allowedExtensions: [".pdf", ".doc", ".docx", ".txt", ".md"],
  storageType: "local",
  storagePath: "/uploads",
  tempPath: "/tmp",
  compressionEnabled: true,
  compressionQuality: 80,
  virusScanEnabled: false,
  encryptFilenames: false,
  forbiddenPatterns: [],
};

// 存储键
const STORAGE_KEY = 'v2-admin-file-middleware-settings';

/**
 * 验证设置数据
 */
const validateFileMiddlewareSettings = (settings: Partial<FileMiddlewareSettingsData>): string[] => {
  const errors: string[] = [];
  
  if (settings.maxFileSize !== undefined) {
    if (settings.maxFileSize < 1 || settings.maxFileSize > 1024) {
      errors.push('文件大小限制必须在1-1024MB之间');
    }
  }
  
  if (settings.compressionQuality !== undefined) {
    if (settings.compressionQuality < 10 || settings.compressionQuality > 100) {
      errors.push('压缩质量必须在10-100之间');
    }
  }
  
  if (settings.allowedExtensions !== undefined) {
    if (!Array.isArray(settings.allowedExtensions) || settings.allowedExtensions.length === 0) {
      errors.push('至少需要允许一种文件扩展名');
    }
  }
  
  if (settings.storagePath !== undefined) {
    if (!settings.storagePath || settings.storagePath.trim() === '') {
      errors.push('存储路径不能为空');
    }
  }
  
  if (settings.tempPath !== undefined) {
    if (!settings.tempPath || settings.tempPath.trim() === '') {
      errors.push('临时路径不能为空');
    }
  }
  
  return errors;
};

/**
 * 从 localStorage 加载设置缓存
 */
const loadFromCache = (): { settings: FileMiddlewareSettingsData; lastUpdated: number } | null => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY);
    if (cached) {
      const { settings, lastUpdated } = JSON.parse(cached);
      // 检查缓存是否过期（24小时）
      if (Date.now() - lastUpdated < 24 * 60 * 60 * 1000) {
        return { settings, lastUpdated };
      }
    }
  } catch (error) {
    console.warn('Failed to load file middleware settings from cache:', error);
  }
  return null;
};

/**
 * 保存设置到 localStorage
 */
const saveToCache = (settings: FileMiddlewareSettingsData) => {
  try {
    const cacheData = {
      settings,
      lastUpdated: Date.now(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Failed to save file middleware settings to cache:', error);
  }
};

/**
 * 文件中间件设置 Slice 创建器
 */
export const createFileMiddlewareSettingsSlice: StateCreator<
  FileMiddlewareSettingsState,
  [],
  [],
  FileMiddlewareSettingsState
> = (set, get) => ({
  // 初始状态
  fileMiddlewareSettings: null,
  fileMiddlewareLoading: false,
  fileMiddlewareSaving: false,
  fileMiddlewareError: null,
  fileMiddlewareLastUpdated: null,
  fileMiddlewareIsDirty: false,
  testResult: '',
  testing: false,

  /**
   * 获取默认设置
   */
  getFileMiddlewareDefaultSettings: () => ({ ...DEFAULT_FILE_MIDDLEWARE_SETTINGS }),

  /**
   * 设置文件中间件设置数据
   */
  setFileMiddlewareSettings: (settings: FileMiddlewareSettingsData) => {
    set({ 
      fileMiddlewareSettings: settings,
      fileMiddlewareIsDirty: true,
      fileMiddlewareError: null 
    });
  },

  /**
   * 设置加载状态
   */
  setFileMiddlewareLoading: (loading: boolean) => {
    set({ fileMiddlewareLoading: loading });
  },

  /**
   * 设置保存状态
   */
  setFileMiddlewareSaving: (saving: boolean) => {
    set({ fileMiddlewareSaving: saving });
  },

  /**
   * 设置错误信息
   */
  setFileMiddlewareError: (error: string | null) => {
    set({ fileMiddlewareError: error });
  },

  /**
   * 标记是否有未保存的更改
   */
  markFileMiddlewareDirty: (dirty: boolean) => {
    set({ fileMiddlewareIsDirty: dirty });
  },

  /**
   * 加载文件中间件设置
   */
  loadFileMiddlewareSettings: async () => {
    // 先尝试从缓存加载
    const cached = loadFromCache();
    if (cached) {
      set({ 
        fileMiddlewareSettings: cached.settings,
        fileMiddlewareLastUpdated: cached.lastUpdated,
        fileMiddlewareIsDirty: false,
        fileMiddlewareError: null 
      });
      console.log('✅ File middleware settings loaded from cache');
      return;
    }
    
    try {
      set({ fileMiddlewareLoading: true, fileMiddlewareError: null });
      
      // 动态导入服务
      const fileMiddlewareService = await import('/services/fileMiddlewareService');
      const settings = await fileMiddlewareService.fileMiddlewareService.getSettings();
      
      set({ 
        fileMiddlewareSettings: settings,
        fileMiddlewareLastUpdated: Date.now(),
        fileMiddlewareIsDirty: false,
        fileMiddlewareError: null 
      });
      
      // 保存到缓存
      saveToCache(settings);
    } catch (error: any) {
      console.error('Failed to load file middleware settings:', error);
      set({ 
        fileMiddlewareError: error.message || '加载文件中间件设置失败',
        fileMiddlewareSettings: { ...DEFAULT_FILE_MIDDLEWARE_SETTINGS }
      });
    } finally {
      set({ fileMiddlewareLoading: false });
    }
  },

  /**
   * 更新文件中间件设置
   */
  updateFileMiddlewareSettings: async (newSettings: Partial<FileMiddlewareSettingsData>) => {
    const { fileMiddlewareSettings } = get();
    
    if (!fileMiddlewareSettings) {
      set({ fileMiddlewareError: '文件中间件设置数据未加载' });
      return;
    }
    
    // 合并新设置
    const updatedSettings = { ...fileMiddlewareSettings, ...newSettings };
    
    // 验证设置
    const validationErrors = validateFileMiddlewareSettings(updatedSettings);
    if (validationErrors.length > 0) {
      set({ fileMiddlewareError: validationErrors.join('; ') });
      return;
    }
    
    try {
      set({ fileMiddlewareSaving: true, fileMiddlewareError: null });
      
      // 动态导入服务
      const fileMiddlewareService = await import('/services/fileMiddlewareService');
      await fileMiddlewareService.fileMiddlewareService.updateSettings(updatedSettings);
      
      set({ 
        fileMiddlewareSettings: updatedSettings,
        fileMiddlewareLastUpdated: Date.now(),
        fileMiddlewareIsDirty: false,
        fileMiddlewareError: null 
      });
      
      // 保存到缓存
      saveToCache(updatedSettings);

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('文件中间件设置保存成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to update file middleware settings:', error);
      set({ fileMiddlewareError: error.message || '保存文件中间件设置失败' });
    } finally {
      set({ fileMiddlewareSaving: false });
    }
  },

  /**
   * 重置文件中间件设置
   */
  resetFileMiddlewareSettings: () => {
    set({
      fileMiddlewareSettings: { ...DEFAULT_FILE_MIDDLEWARE_SETTINGS },
      fileMiddlewareLoading: false,
      fileMiddlewareSaving: false,
      fileMiddlewareError: null,
      fileMiddlewareLastUpdated: null,
      fileMiddlewareIsDirty: false,
      testResult: '',
      testing: false,
    });
    
    // 清除缓存
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear file middleware settings cache:', error);
    }
  },

  /**
   * 测试文件中间件功能
   */
  testFileMiddleware: async (testType: 'upload' | 'scan' | 'compression') => {
    const { fileMiddlewareSettings } = get();
    
    if (!fileMiddlewareSettings || !fileMiddlewareSettings.enabled) {
      set({ fileMiddlewareError: '请先启用文件中间件' });
      return;
    }
    
    try {
      set({ testing: true, fileMiddlewareError: null });
      
      // 动态导入服务
      const fileMiddlewareService = await import('/services/fileMiddlewareService');
      const response = await fileMiddlewareService.fileMiddlewareService.testFileMiddleware({
        testType,
        fileSize: testType === 'upload' ? fileMiddlewareSettings.maxFileSize : undefined,
        fileExtension: testType === 'upload' ? fileMiddlewareSettings.allowedExtensions[0] : undefined,
      });
      
      if (response.success) {
        set({ 
          testResult: response.message,
          fileMiddlewareError: null 
        });
      } else {
        set({ 
          fileMiddlewareError: response.message,
          testResult: ''
        });
      }
    } catch (error: any) {
      console.error('Failed to test file middleware:', error);
      set({ 
        fileMiddlewareError: error.message || '文件中间件测试失败',
        testResult: ''
      });
    } finally {
      set({ testing: false });
    }
  },
});
