/**
 * 标题生成设置状态管理 Slice
 * 管理标题生成配置、模型选择、测试功能等
 */

import { StateCreator } from 'zustand';

// 标题生成设置数据类型
export interface TitleGenerationSettingsData {
  enabled: boolean;
  model_id: string;
  model_name: string;
  prompt_template: string;
  max_length: number;
  temperature: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  timeout: number;
  retry_count: number;
  fallback_enabled: boolean;
  fallback_template: string;
  cache_enabled: boolean;
  cache_duration: number;
}

// 模型信息类型
export interface ModelInfo {
  id: string;
  name: string;
  provider: string;
  type: string;
  available: boolean;
}

// 标题生成设置状态接口
export interface TitleGenerationSettingsState {
  // 标题生成设置数据
  titleGenerationSettings: TitleGenerationSettingsData | null;
  
  // UI 状态
  titleGenerationLoading: boolean;
  titleGenerationSaving: boolean;
  titleGenerationError: string | null;
  
  // 缓存和持久化
  titleGenerationLastUpdated: number | null;
  titleGenerationIsDirty: boolean;
  
  // 标题生成特定状态
  models: ModelInfo[];
  loadingModels: boolean;
  testResult: string;
  testing: boolean;
  
  // 标题生成操作方法
  loadTitleGenerationSettings: () => Promise<void>;
  updateTitleGenerationSettings: (settings: Partial<TitleGenerationSettingsData>) => Promise<void>;
  setTitleGenerationSettings: (settings: TitleGenerationSettingsData) => void;
  setTitleGenerationLoading: (loading: boolean) => void;
  setTitleGenerationSaving: (saving: boolean) => void;
  setTitleGenerationError: (error: string | null) => void;
  markTitleGenerationDirty: (dirty: boolean) => void;
  resetTitleGenerationSettings: () => void;
  getTitleGenerationDefaultSettings: () => TitleGenerationSettingsData;
  
  // 标题生成特定操作
  loadModels: () => Promise<void>;
  testTitleGeneration: (content: string) => Promise<void>;
}

// 默认标题生成设置
const DEFAULT_TITLE_GENERATION_SETTINGS: TitleGenerationSettingsData = {
  enabled: true,
  model_id: '',
  model_name: '',
  prompt_template: '你是一个专业的对话标题生成器。请根据以下对话内容生成一个简洁的标题。\n\n要求：\n1. 标题长度：3-5个中文词汇\n2. 准确概括：抓住对话的核心主题\n3. 简洁明了：避免冗长和复杂表述\n4. 中文输出：使用简体中文\n5. 无标点：不包含任何标点符号\n\n对话内容：\n{content}\n\n请直接输出标题，不要包含任何解释或额外内容：',
  max_length: 50,
  temperature: 0.3,
  top_p: 0.9,
  frequency_penalty: 0.0,
  presence_penalty: 0.0,
  timeout: 30,
  retry_count: 3,
  fallback_enabled: true,
  fallback_template: '对话 {timestamp}',
  cache_enabled: true,
  cache_duration: 3600, // 1小时
};

// 存储键
const STORAGE_KEY = 'v2-admin-title-generation-settings';

/**
 * 验证设置数据
 */
const validateTitleGenerationSettings = (settings: Partial<TitleGenerationSettingsData>): string[] => {
  const errors: string[] = [];
  
  if (settings.max_length !== undefined) {
    if (settings.max_length < 10 || settings.max_length > 200) {
      errors.push('标题最大长度必须在10-200之间');
    }
  }
  
  if (settings.temperature !== undefined) {
    if (settings.temperature < 0 || settings.temperature > 2) {
      errors.push('温度参数必须在0-2之间');
    }
  }
  
  if (settings.top_p !== undefined) {
    if (settings.top_p < 0 || settings.top_p > 1) {
      errors.push('Top P参数必须在0-1之间');
    }
  }
  
  if (settings.timeout !== undefined) {
    if (settings.timeout < 5 || settings.timeout > 300) {
      errors.push('超时时间必须在5-300秒之间');
    }
  }
  
  if (settings.retry_count !== undefined) {
    if (settings.retry_count < 0 || settings.retry_count > 10) {
      errors.push('重试次数必须在0-10之间');
    }
  }
  
  if (settings.cache_duration !== undefined) {
    if (settings.cache_duration < 300 || settings.cache_duration > 86400) {
      errors.push('缓存时长必须在300-86400秒之间');
    }
  }
  
  return errors;
};

/**
 * 从 localStorage 加载设置缓存
 */
const loadFromCache = (): { settings: TitleGenerationSettingsData; lastUpdated: number } | null => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY);
    if (cached) {
      const { settings, lastUpdated } = JSON.parse(cached);
      // 检查缓存是否过期（24小时）
      if (Date.now() - lastUpdated < 24 * 60 * 60 * 1000) {
        return { settings, lastUpdated };
      }
    }
  } catch (error) {
    console.warn('Failed to load title generation settings from cache:', error);
  }
  return null;
};

/**
 * 保存设置到 localStorage
 */
const saveToCache = (settings: TitleGenerationSettingsData) => {
  try {
    const cacheData = {
      settings,
      lastUpdated: Date.now(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Failed to save title generation settings to cache:', error);
  }
};

/**
 * 标题生成设置 Slice 创建器
 */
export const createTitleGenerationSettingsSlice: StateCreator<
  TitleGenerationSettingsState,
  [],
  [],
  TitleGenerationSettingsState
> = (set, get) => ({
  // 初始状态
  titleGenerationSettings: null,
  titleGenerationLoading: false,
  titleGenerationSaving: false,
  titleGenerationError: null,
  titleGenerationLastUpdated: null,
  titleGenerationIsDirty: false,
  models: [],
  loadingModels: false,
  testResult: '',
  testing: false,

  /**
   * 获取默认设置
   */
  getTitleGenerationDefaultSettings: () => ({ ...DEFAULT_TITLE_GENERATION_SETTINGS }),

  /**
   * 设置标题生成设置数据
   */
  setTitleGenerationSettings: (settings: TitleGenerationSettingsData) => {
    set({ 
      titleGenerationSettings: settings,
      titleGenerationIsDirty: true,
      titleGenerationError: null 
    });
  },

  /**
   * 设置加载状态
   */
  setTitleGenerationLoading: (loading: boolean) => {
    set({ titleGenerationLoading: loading });
  },

  /**
   * 设置保存状态
   */
  setTitleGenerationSaving: (saving: boolean) => {
    set({ titleGenerationSaving: saving });
  },

  /**
   * 设置错误信息
   */
  setTitleGenerationError: (error: string | null) => {
    set({ titleGenerationError: error });
  },

  /**
   * 标记是否有未保存的更改
   */
  markTitleGenerationDirty: (dirty: boolean) => {
    set({ titleGenerationIsDirty: dirty });
  },

  /**
   * 加载标题生成设置
   */
  loadTitleGenerationSettings: async () => {
    // 先尝试从缓存加载
    const cached = loadFromCache();
    if (cached) {
      set({ 
        titleGenerationSettings: cached.settings,
        titleGenerationLastUpdated: cached.lastUpdated,
        titleGenerationIsDirty: false,
        titleGenerationError: null 
      });
      console.log('✅ Title generation settings loaded from cache');
      return;
    }
    
    try {
      set({ titleGenerationLoading: true, titleGenerationError: null });
      
      // 动态导入服务
      const titleGenerationService = await import('../../services/titleGenerationService');
      const response = await titleGenerationService.default.getTitleGenerationSettings();
      
      // 转换数据格式
      const settings: TitleGenerationSettingsData = {
        enabled: response.enabled,
        model_id: response.titleGenerationModelId,
        model_name: '',
        prompt_template: response.titlePromptTemplate,
        max_length: response.maxTokens,
        temperature: response.temperature,
        top_p: 0.9,
        frequency_penalty: 0.0,
        presence_penalty: 0.0,
        timeout: 30,
        retry_count: 3,
        fallback_enabled: true,
        fallback_template: '对话 {timestamp}',
        cache_enabled: true,
        cache_duration: 3600,
      };
      
      set({ 
        titleGenerationSettings: settings,
        titleGenerationLastUpdated: Date.now(),
        titleGenerationIsDirty: false,
        titleGenerationError: null 
      });
      
      // 保存到缓存
      saveToCache(settings);
    } catch (error: any) {
      console.error('Failed to load title generation settings:', error);
      set({ 
        titleGenerationError: error.message || '加载标题生成设置失败',
        titleGenerationSettings: { ...DEFAULT_TITLE_GENERATION_SETTINGS }
      });
    } finally {
      set({ titleGenerationLoading: false });
    }
  },

  /**
   * 更新标题生成设置
   */
  updateTitleGenerationSettings: async (newSettings: Partial<TitleGenerationSettingsData>) => {
    const { titleGenerationSettings } = get();
    
    if (!titleGenerationSettings) {
      set({ titleGenerationError: '标题生成设置数据未加载' });
      return;
    }
    
    // 合并新设置
    const updatedSettings = { ...titleGenerationSettings, ...newSettings };
    
    // 验证设置
    const validationErrors = validateTitleGenerationSettings(updatedSettings);
    if (validationErrors.length > 0) {
      set({ titleGenerationError: validationErrors.join('; ') });
      return;
    }
    
    try {
      set({ titleGenerationSaving: true, titleGenerationError: null });
      
      // 转换数据格式
      const formData = {
        enabled: updatedSettings.enabled,
        titleGenerationModelId: updatedSettings.model_id,
        titlePromptTemplate: updatedSettings.prompt_template,
        temperature: updatedSettings.temperature,
        maxTokens: updatedSettings.max_length,
      };
      
      // 动态导入服务
      const titleGenerationService = await import('../../services/titleGenerationService');
      await titleGenerationService.default.updateTitleGenerationSettings(formData);
      
      set({ 
        titleGenerationSettings: updatedSettings,
        titleGenerationLastUpdated: Date.now(),
        titleGenerationIsDirty: false,
        titleGenerationError: null 
      });
      
      // 保存到缓存
      saveToCache(updatedSettings);

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('标题生成设置保存成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to update title generation settings:', error);
      set({ titleGenerationError: error.message || '保存标题生成设置失败' });
    } finally {
      set({ titleGenerationSaving: false });
    }
  },

  /**
   * 重置标题生成设置
   */
  resetTitleGenerationSettings: () => {
    set({
      titleGenerationSettings: { ...DEFAULT_TITLE_GENERATION_SETTINGS },
      titleGenerationLoading: false,
      titleGenerationSaving: false,
      titleGenerationError: null,
      titleGenerationLastUpdated: null,
      titleGenerationIsDirty: false,
      models: [],
      loadingModels: false,
      testResult: '',
      testing: false,
    });
    
    // 清除缓存
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear title generation settings cache:', error);
    }
  },

  /**
   * 加载可用模型列表
   */
  loadModels: async () => {
    try {
      set({ loadingModels: true });
      
      // 模拟模型数据
      const mockModels: ModelInfo[] = [
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', provider: 'OpenAI', type: 'chat', available: true },
        { id: 'gpt-4', name: 'GPT-4', provider: 'OpenAI', type: 'chat', available: true },
        { id: 'claude-3-haiku', name: 'Claude 3 Haiku', provider: 'Anthropic', type: 'chat', available: true },
        { id: 'gemini-pro', name: 'Gemini Pro', provider: 'Google', type: 'chat', available: true },
      ];
      
      set({ 
        models: mockModels,
        titleGenerationError: null 
      });
    } catch (error: any) {
      console.error('Failed to load models:', error);
      set({ titleGenerationError: error.message || '加载模型列表失败' });
    } finally {
      set({ loadingModels: false });
    }
  },

  /**
   * 测试标题生成
   */
  testTitleGeneration: async (content: string) => {
    const { titleGenerationSettings } = get();
    
    if (!titleGenerationSettings || !titleGenerationSettings.model_id) {
      set({ titleGenerationError: '请先选择模型' });
      return;
    }
    
    if (!content.trim()) {
      set({ titleGenerationError: '请输入测试内容' });
      return;
    }
    
    try {
      set({ testing: true, titleGenerationError: null });
      
      // 动态导入服务
      const titleGenerationService = await import('../../services/titleGenerationService');
      const response = await titleGenerationService.default.testTitleGeneration({
        content,
        modelId: titleGenerationSettings.model_id,
        promptTemplate: titleGenerationSettings.prompt_template,
        temperature: titleGenerationSettings.temperature,
        maxTokens: titleGenerationSettings.max_length,
      });
      
      if (response.success) {
        set({ 
          testResult: response.title,
          titleGenerationError: null 
        });
      } else {
        set({ 
          titleGenerationError: response.message || '标题生成测试失败',
          testResult: ''
        });
      }
    } catch (error: any) {
      console.error('Failed to test title generation:', error);
      set({ 
        titleGenerationError: error.message || '标题生成测试失败',
        testResult: ''
      });
    } finally {
      set({ testing: false });
    }
  },
});
