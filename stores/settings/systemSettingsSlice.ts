/**
 * 系统设置状态管理 Slice
 * 管理系统基础设置、性能配置、超时配置等
 */

import { StateCreator } from 'zustand';

// 系统设置数据类型
export interface SystemSettingsData {
  // 系统基本信息
  system_name: string;
  system_version: string;
  load_balancing_enabled: boolean;
  
  // 性能配置
  default_page_size: number;
  max_page_size: number;
  model_refresh_interval: number;
  
  // 超时配置
  api_timeout: number;
  http_client_timeout: number;
  stream_context_timeout: number;
  model_list_timeout: number;
}

// 系统设置状态接口
export interface SystemSettingsState {
  // 数据状态
  settings: SystemSettingsData | null;
  
  // UI 状态
  loading: boolean;
  saving: boolean;
  error: string | null;
  
  // 缓存和持久化
  lastUpdated: number | null;
  isDirty: boolean; // 是否有未保存的更改
  
  // 操作方法
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Partial<SystemSettingsData>) => Promise<void>;
  setSettings: (settings: SystemSettingsData) => void;
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setError: (error: string | null) => void;
  markDirty: (dirty: boolean) => void;
  resetSettings: () => void;
  
  // 工具方法
  getDefaultSettings: () => SystemSettingsData;
  validateSettings: (settings: Partial<SystemSettingsData>) => string[];
  loadFromCache: () => boolean;
  saveToCache: (settings: SystemSettingsData) => void;
}

// 默认设置值
const DEFAULT_SETTINGS: SystemSettingsData = {
  system_name: '',
  system_version: '',
  load_balancing_enabled: false,
  default_page_size: 20,
  max_page_size: 100,
  model_refresh_interval: 300,
  api_timeout: 30,
  http_client_timeout: 30,
  stream_context_timeout: 120,
  model_list_timeout: 10,
};

// 存储键
const STORAGE_KEY = 'v2-admin-system-settings';

/**
 * 系统设置 Slice 创建器
 */
export const createSystemSettingsSlice: StateCreator<
  SystemSettingsState,
  [],
  [],
  SystemSettingsState
> = (set, get) => ({
  // 初始状态
  settings: null,
  loading: false,
  saving: false,
  error: null,
  lastUpdated: null,
  isDirty: false,

  /**
   * 获取默认设置
   */
  getDefaultSettings: () => ({ ...DEFAULT_SETTINGS }),

  /**
   * 设置加载状态
   */
  setLoading: (loading: boolean) => {
    set({ loading });
  },

  /**
   * 设置保存状态
   */
  setSaving: (saving: boolean) => {
    set({ saving });
  },

  /**
   * 设置错误信息
   */
  setError: (error: string | null) => {
    set({ error });
  },

  /**
   * 标记是否有未保存的更改
   */
  markDirty: (dirty: boolean) => {
    set({ isDirty: dirty });
  },

  /**
   * 直接设置设置数据（用于表单更新）
   */
  setSettings: (settings: SystemSettingsData) => {
    set({ 
      settings,
      isDirty: true,
      error: null 
    });
  },

  /**
   * 验证设置数据
   */
  validateSettings: (settings: Partial<SystemSettingsData>): string[] => {
    const errors: string[] = [];
    
    if (settings.default_page_size !== undefined) {
      if (settings.default_page_size < 1 || settings.default_page_size > 100) {
        errors.push('默认页面大小必须在1-100之间');
      }
    }
    
    if (settings.max_page_size !== undefined) {
      if (settings.max_page_size < 1 || settings.max_page_size > 1000) {
        errors.push('最大页面大小必须在1-1000之间');
      }
    }
    
    if (settings.model_refresh_interval !== undefined) {
      if (settings.model_refresh_interval < 30 || settings.model_refresh_interval > 3600) {
        errors.push('模型刷新间隔必须在30-3600秒之间');
      }
    }
    
    if (settings.api_timeout !== undefined) {
      if (settings.api_timeout < 5 || settings.api_timeout > 300) {
        errors.push('API超时时间必须在5-300秒之间');
      }
    }
    
    if (settings.http_client_timeout !== undefined) {
      if (settings.http_client_timeout < 5 || settings.http_client_timeout > 300) {
        errors.push('HTTP客户端超时时间必须在5-300秒之间');
      }
    }
    
    if (settings.stream_context_timeout !== undefined) {
      if (settings.stream_context_timeout < 30 || settings.stream_context_timeout > 600) {
        errors.push('流式上下文超时时间必须在30-600秒之间');
      }
    }
    
    if (settings.model_list_timeout !== undefined) {
      if (settings.model_list_timeout < 5 || settings.model_list_timeout > 60) {
        errors.push('模型列表超时时间必须在5-60秒之间');
      }
    }
    
    return errors;
  },

  /**
   * 从 localStorage 加载设置缓存
   */
  loadFromCache: (): boolean => {
    try {
      const cached = localStorage.getItem(STORAGE_KEY);
      if (cached) {
        const { settings, lastUpdated } = JSON.parse(cached);
        // 检查缓存是否过期（24小时）
        if (Date.now() - lastUpdated < 24 * 60 * 60 * 1000) {
          set({ 
            settings,
            lastUpdated,
            isDirty: false 
          });
          return true;
        }
      }
    } catch (error) {
      console.warn('Failed to load settings from cache:', error);
    }
    return false;
  },

  /**
   * 保存设置到 localStorage
   */
  saveToCache: (settings: SystemSettingsData) => {
    try {
      const cacheData = {
        settings,
        lastUpdated: Date.now(),
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Failed to save settings to cache:', error);
    }
  },

  /**
   * 重置设置到默认值
   */
  resetSettings: () => {
    set({
      settings: { ...DEFAULT_SETTINGS },
      loading: false,
      saving: false,
      error: null,
      lastUpdated: null,
      isDirty: false,
    });
    
    // 清除缓存
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear settings cache:', error);
    }
  },

  /**
   * 加载系统设置
   */
  loadSettings: async () => {
    const { loadFromCache, saveToCache } = get();

    // 先尝试从缓存加载
    if (loadFromCache()) {
      console.log('✅ Settings loaded from cache');
      return;
    }

    try {
      set({ loading: true, error: null });

      // 动态导入 API 服务
      const { settingsAPI } = await import('../../../src/services/settingsService');
      const { handleApiResponse } = await import('../../../src/utils/apiResponseHandler');

      const response = await settingsAPI.getSettings();
      const result = handleApiResponse(response);

      if (result.success) {
        const settings = { ...DEFAULT_SETTINGS, ...(result.data as Partial<SystemSettingsData>) };
        set({
          settings,
          lastUpdated: Date.now(),
          isDirty: false,
          error: null
        });

        // 保存到缓存
        saveToCache(settings);

        console.log('✅ Settings loaded from API');
      } else {
        throw new Error((result as any).message || '加载设置失败');
      }
    } catch (error: any) {
      console.error('Failed to load settings:', error);
      set({
        error: error.message || '加载设置失败',
        settings: { ...DEFAULT_SETTINGS } // 使用默认设置作为后备
      });

      // 导入错误处理
      try {
        const { handleApiError } = await import('../../../src/utils/apiResponseHandler');
        handleApiError(error);
      } catch (importError) {
        console.error('Failed to import error handler:', importError);
      }
    } finally {
      set({ loading: false });
    }
  },

  /**
   * 更新系统设置
   */
  updateSettings: async (newSettings: Partial<SystemSettingsData>) => {
    const { settings, validateSettings, saveToCache } = get();

    if (!settings) {
      set({ error: '设置数据未加载' });
      return;
    }

    // 合并新设置
    const updatedSettings = { ...settings, ...newSettings };

    // 验证设置
    const validationErrors = validateSettings(updatedSettings);
    if (validationErrors.length > 0) {
      set({ error: validationErrors.join('; ') });
      return;
    }

    try {
      set({ saving: true, error: null });

      // 动态导入 API 服务
      const { settingsAPI } = await import('../../../src/services/settingsService');
      const { handleApiResponse } = await import('../../../src/utils/apiResponseHandler');

      const response = await settingsAPI.updateSettings(updatedSettings);
      const result = handleApiResponse(response);

      if (result.success) {
        set({
          settings: updatedSettings,
          lastUpdated: Date.now(),
          isDirty: false,
          error: null
        });

        // 保存到缓存
        saveToCache(updatedSettings);

        console.log('✅ Settings updated successfully');

        // 显示成功消息
        try {
          const { message } = await import('antd');
          message.success('设置保存成功');
        } catch (importError) {
          console.log('Settings saved successfully');
        }
      } else {
        throw new Error((result as any).message || '保存设置失败');
      }
    } catch (error: any) {
      console.error('Failed to update settings:', error);
      set({ error: error.message || '保存设置失败' });

      // 导入错误处理
      try {
        const { handleApiError } = await import('../../../src/utils/apiResponseHandler');
        handleApiError(error);
      } catch (importError) {
        console.error('Failed to import error handler:', importError);
      }
    } finally {
      set({ saving: false });
    }
  },
});
