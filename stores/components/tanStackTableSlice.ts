/**
 * TanStack Table 通用 Zustand Slice
 * 处理表格的状态管理，包括分页、排序、筛选、选择等
 */

import { StateCreator } from 'zustand';
import type { 
  SortingState, 
  PaginationState, 
  ColumnFiltersState 
} from '@tanstack/react-table';

// 表格状态接口
export interface TanStackTableState {
  // 基础状态
  tableLoading: boolean;
  tableError: string | null;

  // 表格状态
  tableSorting: SortingState;
  tablePagination: PaginationState;
  tableColumnFilters: ColumnFiltersState;
  tableGlobalFilter: string;

  // 选择状态
  tableSelectedRows: Record<string, boolean>;
  tableSelectedRowIds: string[];

  // 数据状态
  tableData: any[];
  tableFilteredData: any[];
  tableTotalCount: number;

  // 优化
  tableLastUpdated: number;
}

// 表格操作接口
export interface TanStackTableActions {
  // 基础操作
  setTableLoading: (loading: boolean) => void;
  setTableError: (error: string | null) => void;
  clearTableError: () => void;
  
  // 表格状态操作
  setTableSorting: (sorting: SortingState) => void;
  setTablePagination: (pagination: PaginationState) => void;
  setTableColumnFilters: (filters: ColumnFiltersState) => void;
  setTableGlobalFilter: (filter: string) => void;
  
  // 分页操作（由 TanStack Table 内部处理）
  goToTablePage: (pageIndex: number) => void;
  setTablePageSize: (pageSize: number) => void;
  nextTablePage: () => void;
  previousTablePage: () => void;
  
  // 选择操作
  setTableSelectedRows: (selectedRows: Record<string, boolean>) => void;
  selectTableRow: (rowId: string, selected: boolean) => void;
  selectAllTableRows: (selected: boolean) => void;
  clearTableSelection: () => void;
  
  // 数据操作
  setTableData: (data: any[]) => void;
  setTableFilteredData: (data: any[]) => void;
  setTableTotalCount: (count: number) => void;
  
  // 筛选和搜索
  resetTableFilters: () => void;
  applyTableFilters: () => void;

  // 重置操作
  resetTable: () => void;
}

// 组合接口
export interface TanStackTableSlice extends TanStackTableState, TanStackTableActions {}

// 默认状态
const DEFAULT_STATE: TanStackTableState = {
  tableLoading: false,
  tableError: null,
  tableSorting: [],
  tablePagination: { pageIndex: 0, pageSize: 10 },
  tableColumnFilters: [],
  tableGlobalFilter: '',
  tableSelectedRows: {},
  tableSelectedRowIds: [],
  tableData: [],
  tableFilteredData: [],
  tableTotalCount: 0,
  tableLastUpdated: 0,
};

// 移除缓存功能 - 表格状态不需要持久化

// 创建 Zustand slice
export const createTanStackTableSlice: StateCreator<
  TanStackTableSlice,
  [],
  [],
  TanStackTableSlice
> = (set, get) => ({
  // 初始状态
  ...DEFAULT_STATE,

  /**
   * 设置加载状态
   */
  setTableLoading: (loading: boolean) => {
    set({ tableLoading: loading });
  },

  /**
   * 设置错误
   */
  setTableError: (error: string | null) => {
    set({ tableError: error });
  },

  /**
   * 清除错误
   */
  clearTableError: () => {
    set({ tableError: null });
  },

  /**
   * 设置排序状态
   */
  setTableSorting: (sorting: SortingState) => {
    set({ tableSorting: sorting, tableLastUpdated: Date.now() });
  },

  /**
   * 设置分页状态
   */
  setTablePagination: (pagination: PaginationState) => {
    set({ tablePagination: pagination, tableLastUpdated: Date.now() });
  },

  /**
   * 设置列筛选
   */
  setTableColumnFilters: (columnFilters: ColumnFiltersState) => {
    set({
      tableColumnFilters: columnFilters,
      tablePagination: { ...get().tablePagination, pageIndex: 0 }, // 重置到第一页
      tableLastUpdated: Date.now()
    });
  },

  /**
   * 设置全局筛选
   */
  setTableGlobalFilter: (globalFilter: string) => {
    set({
      tableGlobalFilter: globalFilter,
      tablePagination: { ...get().tablePagination, pageIndex: 0 }, // 重置到第一页
      tableLastUpdated: Date.now()
    });
  },



  /**
   * 设置选中行
   */
  setTableSelectedRows: (selectedRows: Record<string, boolean>) => {
    const selectedRowIds = Object.keys(selectedRows).filter(id => selectedRows[id]);
    set({ tableSelectedRows: selectedRows, tableSelectedRowIds: selectedRowIds });
  },

  /**
   * 选择单行
   */
  selectTableRow: (rowId: string, selected: boolean) => {
    const { tableSelectedRows } = get();
    const newSelectedRows = { ...tableSelectedRows };
    
    if (selected) {
      newSelectedRows[rowId] = true;
    } else {
      delete newSelectedRows[rowId];
    }
    
    get().setTableSelectedRows(newSelectedRows);
  },

  /**
   * 全选/取消全选
   */
  selectAllTableRows: (selected: boolean) => {
    const { tableData } = get();
    const newSelectedRows: Record<string, boolean> = {};
    
    if (selected) {
      tableData.forEach((row, index) => {
        newSelectedRows[String(index)] = true;
      });
    }
    
    get().setTableSelectedRows(newSelectedRows);
  },

  /**
   * 清除选择
   */
  clearTableSelection: () => {
    set({ tableSelectedRows: {}, tableSelectedRowIds: [] });
  },

  /**
   * 设置数据
   */
  setTableData: (data: any[]) => {
    const { tablePagination } = get();

    // 确保 pageSize 有效，默认为 10
    const pageSize = tablePagination.pageSize || 10;

    // 设置数据时重置到第一页，确保状态一致
    set({
      tableData: data,
      tableTotalCount: data.length,
      tablePagination: { pageIndex: 0, pageSize },
      tableLastUpdated: Date.now()
    });
  },

  /**
   * 设置筛选后的数据
   */
  setTableFilteredData: (filteredData: any[]) => {
    set({ tableFilteredData: filteredData });
  },

  /**
   * 设置总数
   */
  setTableTotalCount: (totalCount: number) => {
    set({ tableTotalCount: totalCount });
  },

  /**
   * 重置筛选
   */
  resetTableFilters: () => {
    const { tablePagination } = get();
    set({
      tableColumnFilters: [],
      tableGlobalFilter: '',
      tablePagination: { ...tablePagination, pageIndex: 0 },
      tableSelectedRows: {},
      tableSelectedRowIds: [],
      tableLastUpdated: Date.now(),
    });
  },

  /**
   * 应用筛选
   */
  applyTableFilters: () => {
    // 这个方法可以在子类中重写以实现特定的筛选逻辑
    set({ tableLastUpdated: Date.now() });
  },

  /**
   * 跳转到指定页面
   */
  goToTablePage: (pageIndex: number) => {
    const { tablePagination } = get();
    set({
      tablePagination: { 
        ...tablePagination, 
        pageIndex 
      }
    });
  },

  /**
   * 设置页面大小
   */
  setTablePageSize: (pageSize: number) => {
    const { tablePagination } = get();
    set({
      tablePagination: { 
        pageIndex: 0, // 重置到第一页
        pageSize 
      }
    });
  },

  /**
   * 下一页
   */
  nextTablePage: () => {
    const { tablePagination } = get();
    set({
      tablePagination: { 
        ...tablePagination, 
        pageIndex: tablePagination.pageIndex + 1 
      }
    });
  },

  /**
   * 上一页
   */
  previousTablePage: () => {
    const { tablePagination } = get();
    set({
      tablePagination: { 
        ...tablePagination, 
        pageIndex: Math.max(0, tablePagination.pageIndex - 1) 
      }
    });
  },

  /**
   * 重置表格
   */
  resetTable: () => {
    set({
      ...DEFAULT_STATE,
    });
  },
});
