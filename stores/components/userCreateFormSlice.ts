/**
 * 用户创建表单 Zustand Slice
 * 处理用户创建和编辑表单的状态管理
 */

import { StateCreator } from 'zustand';
import type { User, UserFormData } from '../../types/user';

// 用户创建表单状态接口
export interface UserCreateFormState {
  // 基础状态
  visible: boolean;
  loading: boolean;
  saving: boolean;
  avatarLoading: boolean;
  error: string | null;

  // 表单数据
  editingUser: User | null;
  avatarUrl: string | undefined;
  avatarFile: File | null;

  // 缓存和优化
  lastUpdated: number;
  isDirty: boolean;
}

// 用户创建表单操作接口
export interface UserCreateFormActions {
  // 模态框控制
  setVisible: (visible: boolean) => void;
  setEditingUser: (user: User | null) => void;

  // 头像处理
  setAvatarUrl: (url: string | undefined) => void;
  setAvatarFile: (file: File | null) => void;
  uploadAvatar: (file: File) => Promise<string>;

  // 表单操作
  createUser: (userData: UserFormData) => Promise<void>;
  updateUser: (userId: string, userData: UserFormData) => Promise<void>;
  resetForm: () => void;

  // 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;

  // 状态管理
  markDirty: (dirty: boolean) => void;
}

// 组合接口
export interface UserCreateFormSlice extends UserCreateFormState, UserCreateFormActions {}

// 默认状态
const DEFAULT_STATE: UserCreateFormState = {
  visible: false,
  loading: false,
  saving: false,
  avatarLoading: false,
  error: null,
  editingUser: null,
  avatarUrl: undefined,
  avatarFile: null,
  lastUpdated: 0,
  isDirty: false,
};

// 移除缓存功能 - 表单数据不需要持久化

// 创建 Zustand slice
export const createUserCreateFormSlice: StateCreator<
  UserCreateFormSlice,
  [],
  [],
  UserCreateFormSlice
> = (set, get) => ({
  // 初始状态
  ...DEFAULT_STATE,

  /**
   * 设置模态框可见性
   */
  setVisible: (visible: boolean) => {
    set({ visible });

    if (!visible) {
      // 关闭模态框时重置状态
      get().resetForm();
    }
  },

  /**
   * 设置编辑用户
   */
  setEditingUser: (user: User | null) => {
    set({
      editingUser: user,
      avatarUrl: user?.avatar,
      isDirty: false,
    });
  },

  /**
   * 设置头像URL
   */
  setAvatarUrl: (url: string | undefined) => {
    set({ avatarUrl: url, isDirty: true });
  },

  /**
   * 设置头像文件
   */
  setAvatarFile: (file: File | null) => {
    set({ avatarFile: file, isDirty: true });
  },

  /**
   * 上传头像
   */
  uploadAvatar: async (file: File): Promise<string> => {
    try {
      set({ avatarLoading: true, error: null });

      // 动态导入服务
      const { userAPI } = await import('../../services/api');
      const response = await userAPI.uploadAvatar(file);

      if (response.data?.path) {
        const avatarPath = response.data.path;
        set({
          avatarUrl: avatarPath,
          avatarFile: null,
          isDirty: true,
          error: null,
        });



        return avatarPath;
      } else {
        throw new Error('上传响应格式错误');
      }
    } catch (error: any) {
      console.error('Failed to upload avatar:', error);
      const errorMessage = error.response?.data?.message || error.message || '头像上传失败';
      set({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      set({ avatarLoading: false });
    }
  },

  /**
   * 创建用户
   */
  createUser: async (userData: UserFormData) => {
    try {
      set({ saving: true, error: null });

      // 动态导入服务
      const { userAPI } = await import('../../services/api');
      const response = await userAPI.createUser(userData);

      if (response.data?.success || response.status === 200) {
        set({
          saving: false,
          visible: false,
          isDirty: false,
          lastUpdated: Date.now(),
          error: null,
        });



        // 重置表单
        get().resetForm();

        // 显示成功消息
        try {
          const { message } = await import('antd');
          message.success('用户创建成功');
        } catch (importError) {
          // 静默处理导入错误
        }
      } else {
        throw new Error(response.data?.message || '创建用户失败');
      }
    } catch (error: any) {
      console.error('Failed to create user:', error);
      const errorMessage = error.response?.data?.message || error.message || '创建用户失败';
      set({
        saving: false,
        error: errorMessage,
      });
      throw new Error(errorMessage);
    }
  },

  /**
   * 更新用户
   */
  updateUser: async (userId: string, userData: UserFormData) => {
    try {
      set({ saving: true, error: null });

      // 动态导入服务
      const { userAPI } = await import('../../services/api');
      const response = await userAPI.updateUser(userId, userData);

      if (response.data?.success || response.status === 200) {
        set({
          saving: false,
          visible: false,
          isDirty: false,
          lastUpdated: Date.now(),
          error: null,
        });



        // 重置表单
        get().resetForm();

        // 显示成功消息
        try {
          const { message } = await import('antd');
          message.success('用户更新成功');
        } catch (importError) {
          // 静默处理导入错误
        }
      } else {
        throw new Error(response.data?.message || '更新用户失败');
      }
    } catch (error: any) {
      console.error('Failed to update user:', error);
      const errorMessage = error.response?.data?.message || error.message || '更新用户失败';
      set({
        saving: false,
        error: errorMessage,
      });
      throw new Error(errorMessage);
    }
  },

  /**
   * 重置表单
   */
  resetForm: () => {
    set({
      editingUser: null,
      avatarUrl: undefined,
      avatarFile: null,
      error: null,
      isDirty: false,
    });
  },

  /**
   * 设置错误
   */
  setError: (error: string | null) => {
    set({ error });
  },

  /**
   * 清除错误
   */
  clearError: () => {
    set({ error: null });
  },

  /**
   * 标记为脏数据
   */
  markDirty: (dirty: boolean) => {
    set({ isDirty: dirty });
  },
});
