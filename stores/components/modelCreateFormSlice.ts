/**
 * 模型创建表单 Zustand Slice
 * 处理模型创建表单的状态管理
 */

import { StateCreator } from 'zustand';
import { v4 as uuidv4 } from 'uuid';
import type { ApiSource, ModelCreateRequest, ModelParameter } from '../../types/model';

// 模型创建表单状态接口
export interface ModelCreateFormState {
  // 基础状态
  visible: boolean;
  loading: boolean;
  saving: boolean;
  error: string | null;

  // 表单数据
  parameters: Array<{ name: string; value: string }>;
  apiSources: ApiSource[];
  loadingApiSources: boolean;
  generatedId: string | null;

  // 缓存和优化
  lastUpdated: number;
  isDirty: boolean;
}

// 模型创建表单操作接口
export interface ModelCreateFormActions {
  // 模态框控制
  setVisible: (visible: boolean) => void;

  // 参数管理
  setParameters: (parameters: Array<{ name: string; value: string }>) => void;
  addParameter: () => void;
  removeParameter: (index: number) => void;
  updateParameter: (index: number, field: 'name' | 'value', value: string) => void;

  // API 源管理
  fetchApiSources: () => Promise<void>;
  setApiSources: (sources: ApiSource[]) => void;
  setLoadingApiSources: (loading: boolean) => void;

  // ID 生成
  generateId: () => string;

  // 表单操作
  createModel: (modelData: ModelCreateRequest) => Promise<void>;
  resetForm: () => void;

  // 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;

  // 状态管理
  markDirty: (dirty: boolean) => void;
}

// 组合接口
export interface ModelCreateFormSlice extends ModelCreateFormState, ModelCreateFormActions {}

// 默认状态
const DEFAULT_STATE: ModelCreateFormState = {
  visible: false,
  loading: false,
  saving: false,
  error: null,
  parameters: [{ name: "", value: "" }],
  apiSources: [],
  loadingApiSources: false,
  generatedId: null,
  lastUpdated: 0,
  isDirty: false,
};

// 移除缓存功能 - 表单数据不需要持久化

// 创建 Zustand slice
export const createModelCreateFormSlice: StateCreator<
  ModelCreateFormSlice,
  [],
  [],
  ModelCreateFormSlice
> = (set, get) => ({
  // 初始状态
  ...DEFAULT_STATE,

  /**
   * 设置模态框可见性
   */
  setVisible: (visible: boolean) => {
    set({ visible });

    if (visible) {
      // 获取API源
      get().fetchApiSources();
    } else {
      // 关闭模态框时重置状态
      get().resetForm();
    }
  },

  /**
   * 设置参数列表
   */
  setParameters: (parameters: Array<{ name: string; value: string }>) => {
    set({ parameters, isDirty: true });
  },

  /**
   * 添加参数
   */
  addParameter: () => {
    const { parameters } = get();
    const newParameters = [...parameters, { name: "", value: "" }];
    set({ parameters: newParameters, isDirty: true });
  },

  /**
   * 删除参数
   */
  removeParameter: (index: number) => {
    const { parameters } = get();
    const newParameters = parameters.filter((_, i) => i !== index);
    set({ parameters: newParameters, isDirty: true });
  },

  /**
   * 更新参数
   */
  updateParameter: (index: number, field: 'name' | 'value', value: string) => {
    const { parameters } = get();
    const newParameters = [...parameters];
    newParameters[index][field] = value;
    set({ parameters: newParameters, isDirty: true });
  },

  /**
   * 设置API源列表
   */
  setApiSources: (sources: ApiSource[]) => {
    set({ apiSources: sources });
  },

  /**
   * 设置API源加载状态
   */
  setLoadingApiSources: (loading: boolean) => {
    set({ loadingApiSources: loading });
  },

  /**
   * 获取API源列表
   */
  fetchApiSources: async () => {
    try {
      set({ loadingApiSources: true, error: null });

      // 动态导入API服务
      const { apiSourceAPI } = await import('../../services/api');
      const { handleApiResponse } = await import('../../utils/apiResponseHandler');

      const response = await apiSourceAPI.getApiSources();
      const result = handleApiResponse(response);

      if (result.success && result.data) {
        const data = result.data as any;
        const apiSources = Array.isArray(data) ? data : data.items || [];
        set({ apiSources, error: null });
      } else {
        const errorMessage = result.error || "获取API源失败";
        set({ error: errorMessage, apiSources: [] });
      }
    } catch (error: any) {
      console.error("获取API源失败:", error);
      const errorMessage = error.response?.data?.message || error.message || "获取API源失败";
      set({ error: errorMessage, apiSources: [] });
    } finally {
      set({ loadingApiSources: false });
    }
  },

  /**
   * 生成新的模型ID
   */
  generateId: () => {
    const newId = uuidv4();
    set({ generatedId: newId, isDirty: true });

    // 显示成功消息
    try {
      import('antd').then(({ message }) => {
        message.success(`已生成新的ID: ${newId.slice(0, 8)}...`);
      });
    } catch (importError) {
      // 静默处理导入错误
    }

    return newId;
  },

  /**
   * 创建模型
   */
  createModel: async (modelData: ModelCreateRequest) => {
    try {
      set({ saving: true, error: null });

      // 动态导入服务
      const { modelAPI } = await import('../../services/api');
      const response = await modelAPI.createModel(modelData);

      if (response.data?.success || response.status === 200) {
        set({
          saving: false,
          visible: false,
          isDirty: false,
          lastUpdated: Date.now(),
          error: null,
        });



        // 重置表单
        get().resetForm();

        // 显示成功消息
        try {
          const { message } = await import('antd');
          message.success('模型创建成功');
        } catch (importError) {
          // 静默处理导入错误
        }
      } else {
        throw new Error(response.data?.message || '创建模型失败');
      }
    } catch (error: any) {
      console.error('Failed to create model:', error);
      const errorMessage = error.response?.data?.message || error.message || '创建模型失败';
      set({
        saving: false,
        error: errorMessage,
      });
      throw new Error(errorMessage);
    }
  },

  /**
   * 重置表单
   */
  resetForm: () => {
    set({
      parameters: [{ name: "", value: "" }],
      generatedId: null,
      error: null,
      isDirty: false,
    });
  },

  /**
   * 设置错误
   */
  setError: (error: string | null) => {
    set({ error });
  },

  /**
   * 清除错误
   */
  clearError: () => {
    set({ error: null });
  },

  /**
   * 标记为脏数据
   */
  markDirty: (dirty: boolean) => {
    set({ isDirty: dirty });
  },
});
