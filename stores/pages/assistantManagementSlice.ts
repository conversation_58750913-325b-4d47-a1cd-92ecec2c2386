/**
 * 助手管理状态管理 Slice
 * 管理助手的增删改查、状态管理等
 */

import { StateCreator } from 'zustand';
import type {
  Assistant,
  AssistantCategory,
  CreateAssistantRequest,
  UpdateAssistantRequest,
  AssistantQueryParams,
  AssistantListResponse,
} from '../../types/assistant';

// 助手管理状态接口
export interface AssistantManagementState {
  // 助手数据
  assistants: Assistant[];
  categories: AssistantCategory[];
  currentAssistant: Assistant | null;
  
  // 分页信息
  assistantPagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  
  // 查询参数和筛选
  assistantActiveTab: 'my' | 'public' | 'all';
  assistantSearchKeyword: string;
  assistantSelectedCategory: string;
  assistantRagFilter: boolean | undefined;
  
  // UI 状态
  assistantLoading: boolean;
  assistantSaving: boolean;
  assistantDeleting: boolean;
  assistantError: string | null;
  
  // 模态框状态
  formModalVisible: boolean;
  detailModalVisible: boolean;
  categoryModalVisible: boolean;
  editingAssistant: Assistant | null;
  viewingAssistant: Assistant | null;
  
  // 选择状态
  selectedRowKeys: string[];
  
  // 缓存和持久化
  assistantsLastUpdated: number | null;
  isDirty: boolean;
}

// 助手管理操作接口
export interface AssistantManagementActions {
  // 数据操作
  loadAssistants: (params?: AssistantQueryParams) => Promise<void>;
  loadAssistantById: (id: string) => Promise<void>;
  createAssistant: (data: CreateAssistantRequest) => Promise<void>;
  updateAssistant: (id: string, data: UpdateAssistantRequest) => Promise<void>;
  deleteAssistant: (id: string) => Promise<void>;
  duplicateAssistant: (assistant: Assistant) => Promise<void>;
  
  // 分类管理
  loadCategories: () => Promise<void>;
  createCategory: (category: Omit<AssistantCategory, 'key'>) => Promise<void>;
  updateCategory: (key: string, category: Partial<AssistantCategory>) => Promise<void>;
  deleteCategory: (key: string) => Promise<void>;
  
  // 筛选和搜索
  setAssistantActiveTab: (tab: 'my' | 'public' | 'all') => void;
  setAssistantSearchKeyword: (keyword: string) => void;
  setAssistantSelectedCategory: (category: string) => void;
  setAssistantRagFilter: (enabled: boolean | undefined) => void;
  
  // UI 状态管理
  setAssistantLoading: (loading: boolean) => void;
  setAssistantSaving: (saving: boolean) => void;
  setAssistantDeleting: (deleting: boolean) => void;
  setAssistantError: (error: string | null) => void;
  
  // 模态框管理
  setFormModalVisible: (visible: boolean) => void;
  setDetailModalVisible: (visible: boolean) => void;
  setCategoryModalVisible: (visible: boolean) => void;
  setEditingAssistant: (assistant: Assistant | null) => void;
  setViewingAssistant: (assistant: Assistant | null) => void;
  
  // 分页管理
  setAssistantPagination: (pagination: Partial<AssistantManagementState['assistantPagination']>) => void;
  
  // 选择管理
  setSelectedRowKeys: (keys: string[]) => void;
  
  // 重置和清理
  resetAssistantManagement: () => void;
  markDirty: (dirty: boolean) => void;
  refreshAssistantsData: () => Promise<void>;
}

// Mock数据 - 分类
const mockCategories: AssistantCategory[] = [
  {
    key: "general",
    label: "通用助手",
    description: "适用于各种日常对话和问答",
  },
  {
    key: "writing",
    label: "写作助手",
    description: "专门用于各类文档写作和内容创作",
  },
  {
    key: "coding",
    label: "编程助手",
    description: "专门用于编程相关问题解答和代码生成",
  },
  {
    key: "analysis",
    label: "分析助手",
    description: "专门用于数据分析和报告生成",
  },
  {
    key: "translation",
    label: "翻译助手",
    description: "专门用于多语言翻译服务",
  },
];

// Mock数据 - 助手
const generateMockAssistants = (): Assistant[] => [
  {
    id: "1",
    name: "通用AI助手",
    description: "一个功能全面的AI助手，可以帮助您处理各种日常任务",
    category: "general",
    tags: ["通用", "对话", "问答"],
    config: {
      system_prompt: "你是一个有用的AI助手，请友好地回答用户的问题。",
      temperature: 0.7,
      max_tokens: 2048,
      top_p: 0.9,
    },
    model_id: "gpt-4",
    is_public: true,
    is_active: true,
    is_system: false,
    owner_id: "current-user",
    created_by: "管理员",
    rag_enabled: false,
    knowledge_base_ids: [],
    usage_count: 1250,
    last_used_at: "2024-06-20T14:45:00Z",
    message_count: 2500,
    total_tokens: 125000,
    rag_query_count: 0,
    average_rating: 4.8,
    rating_count: 156,
    created_at: "2024-01-15T10:30:00Z",
    updated_at: "2024-06-20T14:45:00Z",
  },
  {
    id: "2",
    name: "代码助手",
    description: "专业的编程助手，支持多种编程语言的代码生成和调试",
    category: "coding",
    tags: ["编程", "代码生成", "调试"],
    config: {
      system_prompt: "你是一个专业的编程助手，请帮助用户解决编程问题。",
      temperature: 0.3,
      max_tokens: 4096,
      top_p: 0.8,
    },
    model_id: "gpt-4",
    is_public: true,
    is_active: true,
    is_system: true,
    owner_id: "system",
    created_by: "系统",
    rag_enabled: true,
    knowledge_base_ids: ["kb-coding-1", "kb-coding-2"],
    usage_count: 890,
    last_used_at: "2024-06-18T16:20:00Z",
    message_count: 1780,
    total_tokens: 89000,
    rag_query_count: 450,
    average_rating: 4.9,
    rating_count: 78,
    created_at: "2024-02-01T09:15:00Z",
    updated_at: "2024-06-18T16:20:00Z",
  },
  {
    id: "3",
    name: "写作助手",
    description: "专业的写作助手，帮助您创作高质量的文档和文章",
    category: "writing",
    tags: ["写作", "文档", "创作"],
    config: {
      system_prompt: "你是一个专业的写作助手，请帮助用户提升写作质量。",
      temperature: 0.8,
      max_tokens: 3072,
      top_p: 0.9,
    },
    model_id: "gpt-4",
    is_public: false,
    is_active: true,
    is_system: false,
    owner_id: "current-user",
    created_by: "用户A",
    rag_enabled: false,
    knowledge_base_ids: [],
    usage_count: 567,
    last_used_at: "2024-06-15T13:30:00Z",
    message_count: 1134,
    total_tokens: 56700,
    rag_query_count: 0,
    average_rating: 4.6,
    rating_count: 89,
    created_at: "2024-03-10T11:45:00Z",
    updated_at: "2024-06-15T13:30:00Z",
  },
];

// 默认状态
const DEFAULT_STATE: AssistantManagementState = {
  assistants: [],
  categories: mockCategories,
  currentAssistant: null,
  assistantPagination: {
    current: 1,
    pageSize: 12,
    total: 0,
  },
  assistantActiveTab: 'my',
  assistantSearchKeyword: '',
  assistantSelectedCategory: '',
  assistantRagFilter: undefined,
  assistantLoading: false,
  assistantSaving: false,
  assistantDeleting: false,
  assistantError: null,
  formModalVisible: false,
  detailModalVisible: false,
  categoryModalVisible: false,
  editingAssistant: null,
  viewingAssistant: null,
  selectedRowKeys: [],
  assistantsLastUpdated: null,
  isDirty: false,
};

// 存储键
const STORAGE_KEY = 'v2-admin-assistant-management';

// 创建助手管理 Slice
export const createAssistantManagementSlice: StateCreator<
  AssistantManagementState & AssistantManagementActions,
  [],
  [],
  AssistantManagementState & AssistantManagementActions
> = (set, get) => ({
  ...DEFAULT_STATE,

  // 数据操作
  loadAssistants: async (params?: AssistantQueryParams) => {
    set({ assistantLoading: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      const state = get();
      let filteredAssistants = generateMockAssistants();

      // 根据标签页筛选
      if (state.assistantActiveTab === 'my') {
        filteredAssistants = filteredAssistants.filter(
          (a) => a.owner_id === 'current-user',
        );
      } else if (state.assistantActiveTab === 'public') {
        filteredAssistants = filteredAssistants.filter((a) => a.is_public);
      }

      // 搜索筛选
      if (state.assistantSearchKeyword) {
        const keyword = state.assistantSearchKeyword.toLowerCase();
        filteredAssistants = filteredAssistants.filter(
          (a) =>
            a.name.toLowerCase().includes(keyword) ||
            a.description.toLowerCase().includes(keyword) ||
            a.tags.some((tag: string) => tag.toLowerCase().includes(keyword)),
        );
      }

      // 分类筛选
      if (state.assistantSelectedCategory) {
        filteredAssistants = filteredAssistants.filter(
          (a) => a.category === state.assistantSelectedCategory,
        );
      }

      // RAG筛选
      if (state.assistantRagFilter !== undefined) {
        filteredAssistants = filteredAssistants.filter(
          (a) => a.rag_enabled === state.assistantRagFilter,
        );
      }

      set({
        assistants: filteredAssistants,
        assistantPagination: {
          ...state.assistantPagination,
          total: filteredAssistants.length,
        },
        assistantLoading: false,
        assistantsLastUpdated: Date.now(),
      });
    } catch (error) {
      set({ 
        assistantLoading: false, 
        assistantError: error instanceof Error ? error.message : '加载助手列表失败' 
      });
    }
  },

  loadAssistantById: async (id: string) => {
    set({ assistantLoading: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 300));
      const assistant = generateMockAssistants().find(a => a.id === id);
      set({ 
        currentAssistant: assistant || null,
        assistantLoading: false 
      });
    } catch (error) {
      set({ 
        assistantLoading: false, 
        assistantError: error instanceof Error ? error.message : '加载助手详情失败' 
      });
    }
  },

  createAssistant: async (data: CreateAssistantRequest) => {
    set({ assistantSaving: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // 重新加载数据
      await get().loadAssistants();
      
      set({ 
        assistantSaving: false,
        formModalVisible: false,
        editingAssistant: null,
        assistantsLastUpdated: Date.now(),
      });
    } catch (error) {
      set({ 
        assistantSaving: false, 
        assistantError: error instanceof Error ? error.message : '创建助手失败' 
      });
    }
  },

  updateAssistant: async (id: string, data: UpdateAssistantRequest) => {
    set({ assistantSaving: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      // 重新加载数据
      await get().loadAssistants();
      
      set({ 
        assistantSaving: false,
        formModalVisible: false,
        editingAssistant: null,
        assistantsLastUpdated: Date.now(),
      });
    } catch (error) {
      set({ 
        assistantSaving: false, 
        assistantError: error instanceof Error ? error.message : '更新助手失败' 
      });
    }
  },

  deleteAssistant: async (id: string) => {
    set({ assistantDeleting: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 800));
      
      // 重新加载数据
      await get().loadAssistants();
      
      set({ 
        assistantDeleting: false,
        assistantsLastUpdated: Date.now(),
      });
    } catch (error) {
      set({ 
        assistantDeleting: false, 
        assistantError: error instanceof Error ? error.message : '删除助手失败' 
      });
    }
  },

  duplicateAssistant: async (assistant: Assistant) => {
    const duplicatedAssistant = {
      ...assistant,
      id: undefined,
      name: `${assistant.name} (副本)`,
      is_public: false,
      is_system: false,
    };
    set({ 
      editingAssistant: duplicatedAssistant as unknown as Assistant,
      formModalVisible: true 
    });
  },

  // 分类管理
  loadCategories: async () => {
    set({ assistantLoading: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 300));
      set({ 
        categories: mockCategories,
        assistantLoading: false 
      });
    } catch (error) {
      set({ 
        assistantLoading: false, 
        assistantError: error instanceof Error ? error.message : '加载分类失败' 
      });
    }
  },

  createCategory: async (category: Omit<AssistantCategory, 'key'>) => {
    set({ assistantSaving: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      const newCategory = {
        ...category,
        key: `category-${Date.now()}`,
      };
      
      set(state => ({ 
        categories: [...state.categories, newCategory],
        assistantSaving: false,
        categoryModalVisible: false,
      }));
    } catch (error) {
      set({ 
        assistantSaving: false, 
        assistantError: error instanceof Error ? error.message : '创建分类失败' 
      });
    }
  },

  updateCategory: async (key: string, category: Partial<AssistantCategory>) => {
    set({ assistantSaving: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      set(state => ({ 
        categories: state.categories.map(c => 
          c.key === key ? { ...c, ...category } : c
        ),
        assistantSaving: false,
      }));
    } catch (error) {
      set({ 
        assistantSaving: false, 
        assistantError: error instanceof Error ? error.message : '更新分类失败' 
      });
    }
  },

  deleteCategory: async (key: string) => {
    set({ assistantDeleting: true, assistantError: null });
    try {
      // 模拟API调用
      await new Promise((resolve) => setTimeout(resolve, 500));
      
      set(state => ({ 
        categories: state.categories.filter(c => c.key !== key),
        assistantDeleting: false,
      }));
    } catch (error) {
      set({ 
        assistantDeleting: false, 
        assistantError: error instanceof Error ? error.message : '删除分类失败' 
      });
    }
  },

  // 筛选和搜索
  setAssistantActiveTab: (tab) => {
    set({ assistantActiveTab: tab });
    // 自动重新加载数据
    setTimeout(() => get().loadAssistants(), 0);
  },

  setAssistantSearchKeyword: (keyword) => {
    set({ assistantSearchKeyword: keyword });
    // 自动重新加载数据
    setTimeout(() => get().loadAssistants(), 0);
  },

  setAssistantSelectedCategory: (category) => {
    set({ assistantSelectedCategory: category });
    // 自动重新加载数据
    setTimeout(() => get().loadAssistants(), 0);
  },

  setAssistantRagFilter: (enabled) => {
    set({ assistantRagFilter: enabled });
    // 自动重新加载数据
    setTimeout(() => get().loadAssistants(), 0);
  },

  // UI 状态管理
  setAssistantLoading: (loading) => set({ assistantLoading: loading }),
  setAssistantSaving: (saving) => set({ assistantSaving: saving }),
  setAssistantDeleting: (deleting) => set({ assistantDeleting: deleting }),
  setAssistantError: (error) => set({ assistantError: error }),

  // 模态框管理
  setFormModalVisible: (visible) => {
    set({ formModalVisible: visible });
    if (!visible) {
      set({ editingAssistant: null });
    }
  },
  
  setDetailModalVisible: (visible) => {
    set({ detailModalVisible: visible });
    if (!visible) {
      set({ viewingAssistant: null });
    }
  },
  
  setCategoryModalVisible: (visible) => set({ categoryModalVisible: visible }),
  setEditingAssistant: (assistant) => set({ editingAssistant: assistant }),
  setViewingAssistant: (assistant) => set({ viewingAssistant: assistant }),

  // 分页管理
  setAssistantPagination: (pagination) => {
    set(state => ({ 
      assistantPagination: { ...state.assistantPagination, ...pagination } 
    }));
  },

  // 选择管理
  setSelectedRowKeys: (keys) => set({ selectedRowKeys: keys }),

  // 重置和清理
  resetAssistantManagement: () => {
    set(DEFAULT_STATE);
  },

  markDirty: (dirty) => set({ isDirty: dirty }),

  refreshAssistantsData: async () => {
    await get().loadAssistants();
    set({ assistantsLastUpdated: Date.now() });
  },
});

// 导出类型
export type AssistantManagementSlice = AssistantManagementState & AssistantManagementActions;