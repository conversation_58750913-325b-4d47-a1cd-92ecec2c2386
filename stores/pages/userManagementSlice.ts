import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { useMemo } from 'react';
import dayjs from 'dayjs';
import type { UserData } from '../../pages/UserListManagement/types';
import type { SortingState, PaginationState, ColumnFiltersState } from '@tanstack/react-table';

// 用户管理状态接口
export interface UserManagementState {
  // 用户数据
  users: UserData[];
  loading: boolean;
  error: string | null;
  
  // 筛选状态
  roleFilter: string;
  statusFilter: string;
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  
  // 表单状态
  showCreateForm: boolean;
  createFormLoading: boolean;
  
  // 数据初始化状态
  dataInitialized: boolean;
  
  // 最后更新时间
  lastUpdated: number;
  
  // TanStackTable 状态
  userManagementSorting: SortingState;
  userManagementPagination: PaginationState;
  userManagementColumnFilters: ColumnFiltersState;
  userManagementGlobalFilter: string;
}

// 用户管理操作接口
export interface UserManagementActions {
  // 数据操作
  setUsers: (users: UserData[]) => void;
  addUser: (user: UserData) => void;
  updateUser: (userId: number, updates: Partial<UserData>) => void;
  deleteUser: (userId: number) => void;
  toggleUserStatus: (userId: number, status: boolean) => void;
  
  // 加载状态
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 筛选操作
  setRoleFilter: (role: string) => void;
  setStatusFilter: (status: string) => void;
  setDateRange: (range: [dayjs.Dayjs, dayjs.Dayjs] | null) => void;
  resetFilters: () => void;
  
  // 表单操作
  setShowCreateForm: (show: boolean) => void;
  setCreateFormLoading: (loading: boolean) => void;
  
  // 初始化
  initializeData: (users: UserData[]) => void;
  
  // 重置状态
  reset: () => void;
  
  // TanStackTable 操作
  setUserManagementSorting: (sorting: SortingState) => void;
  setUserManagementPagination: (pagination: PaginationState) => void;
  setUserManagementColumnFilters: (columnFilters: ColumnFiltersState) => void;
  setUserManagementGlobalFilter: (globalFilter: string) => void;
  resetUserManagementTableFilters: () => void;
}

// 默认状态
const DEFAULT_STATE: UserManagementState = {
  users: [],
  loading: false,
  error: null,
  roleFilter: 'all',
  statusFilter: 'all',
  dateRange: null,
  showCreateForm: false,
  createFormLoading: false,
  dataInitialized: false,
  lastUpdated: 0,
  userManagementSorting: [],
  userManagementPagination: { pageIndex: 0, pageSize: 10 },
  userManagementColumnFilters: [],
  userManagementGlobalFilter: '',
};

// 创建用户管理 store
export const useUserManagementStore = create<UserManagementState & UserManagementActions>()(
  devtools(
    (set, get) => ({
      ...DEFAULT_STATE,

      // 数据操作
      setUsers: (users: UserData[]) => {
        set({
          users,
          lastUpdated: Date.now(),
        });
      },

      addUser: (user: UserData) => {
        const { users } = get();
        set({
          users: [...users, user],
          lastUpdated: Date.now(),
        });
      },

      updateUser: (userId: number, updates: Partial<UserData>) => {
        const { users } = get();
        const updatedUsers = users.map(user =>
          user.id === userId ? { ...user, ...updates } : user
        );
        set({
          users: updatedUsers,
          lastUpdated: Date.now(),
        });
      },

      deleteUser: (userId: number) => {
        const { users } = get();
        const filteredUsers = users.filter(user => user.id !== userId);
        set({
          users: filteredUsers,
          lastUpdated: Date.now(),
        });
      },

      toggleUserStatus: (userId: number, status: boolean) => {
        const { users } = get();
        const updatedUsers = users.map(user =>
          user.id === userId
            ? { ...user, status: status ? 'active' as const : 'inactive' as const }
            : user
        );
        set({
          users: updatedUsers,
          lastUpdated: Date.now(),
        });
      },

      // 加载状态
      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      // 筛选操作
      setRoleFilter: (roleFilter: string) => {
        set({ roleFilter, lastUpdated: Date.now() });
      },

      setStatusFilter: (statusFilter: string) => {
        set({ statusFilter, lastUpdated: Date.now() });
      },

      setDateRange: (dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null) => {
        set({ dateRange, lastUpdated: Date.now() });
      },

      resetFilters: () => {
        set({
          roleFilter: 'all',
          statusFilter: 'all',
          dateRange: null,
          lastUpdated: Date.now(),
        });
      },

      // 表单操作
      setShowCreateForm: (showCreateForm: boolean) => {
        set({ showCreateForm });
      },

      setCreateFormLoading: (createFormLoading: boolean) => {
        set({ createFormLoading });
      },

      // 初始化
      initializeData: (users: UserData[]) => {
        set({
          users,
          dataInitialized: true,
          loading: false,
          error: null,
          lastUpdated: Date.now(),
        });
      },

      // 重置状态
      reset: () => {
        set({
          ...DEFAULT_STATE,
          lastUpdated: Date.now(),
        });
      },

      // TanStackTable 操作
      setUserManagementSorting: (userManagementSorting: SortingState) => {
        set({ userManagementSorting });
      },

      setUserManagementPagination: (userManagementPagination: PaginationState) => {
        set({ userManagementPagination });
      },

      setUserManagementColumnFilters: (userManagementColumnFilters: ColumnFiltersState) => {
        set({ userManagementColumnFilters });
      },

      setUserManagementGlobalFilter: (userManagementGlobalFilter: string) => {
        set({ userManagementGlobalFilter });
      },

      resetUserManagementTableFilters: () => {
        set({
          userManagementSorting: [],
          userManagementPagination: { pageIndex: 0, pageSize: 10 },
          userManagementColumnFilters: [],
          userManagementGlobalFilter: '',
        });
      },
    }),
    {
      name: 'user-management-store',
    }
  )
);

// 简化的选择器 - 避免无限重新渲染
export const useFilteredUsers = () => {
  const users = useUserManagementStore(state => state.users);
  const roleFilter = useUserManagementStore(state => state.roleFilter);
  const statusFilter = useUserManagementStore(state => state.statusFilter);
  const dateRange = useUserManagementStore(state => state.dateRange);

  return useMemo(() => {
    let filteredUsers = users;

    // 角色筛选
    if (roleFilter !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.role === roleFilter);
    }

    // 状态筛选
    if (statusFilter !== 'all') {
      filteredUsers = filteredUsers.filter(user => user.status === statusFilter);
    }

    // 日期范围筛选
    if (dateRange) {
      const [startDate, endDate] = dateRange;
      filteredUsers = filteredUsers.filter(user => {
        const userDate = dayjs(user.createdAt);
        return userDate.isAfter(startDate.startOf('day')) &&
               userDate.isBefore(endDate.endOf('day'));
      });
    }

    return filteredUsers;
  }, [users, roleFilter, statusFilter, dateRange]);
};

export const useUserStats = () => {
  const users = useUserManagementStore(state => state.users);

  return useMemo(() => ({
    total: users.length,
    active: users.filter(user => user.status === 'active').length,
    inactive: users.filter(user => user.status === 'inactive').length,
    roles: new Set(users.map(user => user.role)).size,
  }), [users]);
};
