import { StateCreator } from 'zustand';
import { AppState } from '../types';

// 定义系统日志项类型
export interface SystemLogItem {
  key: number;
  level: string;
  content: string;
  module: string;
  time: string;
  user: string;
  ip: string;
}

// 定义 SystemLogsState 类型
export interface SystemLogsState {
  systemLogsData: SystemLogItem[];
  systemLogsLoading: boolean;
  systemLogsSorting: { id: string; desc: boolean }[];
  systemLogsPagination: {
    pageIndex: number;
    pageSize: number;
  };
  systemLogsColumnFilters: { id: string; value: unknown }[];
  systemLogsGlobalFilter: string;
  systemLogsSearchParams: {
    level: string;
    content: string;
    module: string;
    user: string;
    startTime: string;
    endTime: string;
  };
}

// 定义 SystemLogsActions 类型
export interface SystemLogsActions {
  setSystemLogsData: (data: SystemLogItem[]) => void;
  setSystemLogsLoading: (loading: boolean) => void;
  setSystemLogsSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setSystemLogsPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setSystemLogsColumnFilters: (filters: { id: string; value: unknown }[]) => void;
  setSystemLogsGlobalFilter: (filter: string) => void;
  setSystemLogsSearchParams: (params: Partial<SystemLogsState['systemLogsSearchParams']>) => void;
  resetSystemLogsSearchParams: () => void;
  loadSystemLogsData: () => Promise<void>;
}

// 默认状态
const defaultState: SystemLogsState = {
  systemLogsData: [],
  systemLogsLoading: false,
  systemLogsSorting: [],
  systemLogsPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  systemLogsColumnFilters: [],
  systemLogsGlobalFilter: '',
  systemLogsSearchParams: {
    level: '',
    content: '',
    module: '',
    user: '',
    startTime: '',
    endTime: '',
  },
};

// 模拟数据
const mockData: SystemLogItem[] = Array.from({ length: 50 }).map((_, i) => ({
  key: i + 1,
  level: i % 3 === 0 ? "ERROR" : i % 3 === 1 ? "WARN" : "INFO",
  content: i % 3 === 0 ? "系统异常" : i % 3 === 1 ? "内存预警" : "服务启动成功",
  module: i % 2 === 0 ? "系统模块" : "用户模块",
  time: `2024-07-01 14:${(i % 60).toString().padStart(2, "0")}:00`,
  user: i % 2 === 0 ? "admin" : "user" + i,
  ip: `192.168.1.${i + 10}`,
}));

// 创建 SystemLogsSlice
export const createSystemLogsSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never], ['zustand/persist', unknown]],
  [],
  SystemLogsState & SystemLogsActions
> = (set, get) => ({
  ...defaultState,

  setSystemLogsData: (data) => set({ systemLogsData: data }),
  setSystemLogsLoading: (loading) => set({ systemLogsLoading: loading }),
  setSystemLogsSorting: (sorting) => set({ systemLogsSorting: sorting }),
  setSystemLogsPagination: (pagination) => set({ systemLogsPagination: pagination }),
  setSystemLogsColumnFilters: (columnFilters) => set({ systemLogsColumnFilters: columnFilters }),
  setSystemLogsGlobalFilter: (globalFilter) => set({ systemLogsGlobalFilter: globalFilter }),
  setSystemLogsSearchParams: (params) => set((state) => ({
    systemLogsSearchParams: { ...state.systemLogsSearchParams, ...params }
  })),
  resetSystemLogsSearchParams: () => set({
    systemLogsSearchParams: defaultState.systemLogsSearchParams,
    systemLogsGlobalFilter: defaultState.systemLogsGlobalFilter
  }),

  loadSystemLogsData: async () => {
    set({ systemLogsLoading: true });
    try {
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 获取当前状态
      const { systemLogsSearchParams } = get();

      // 模拟筛选和搜索
      let filteredData = [...mockData];

      // 根据搜索条件筛选
      if (systemLogsSearchParams.level) {
        filteredData = filteredData.filter(item =>
          item.level === systemLogsSearchParams.level
        );
      }

      if (systemLogsSearchParams.content) {
        filteredData = filteredData.filter(item =>
          item.content.includes(systemLogsSearchParams.content)
        );
      }

      if (systemLogsSearchParams.module) {
        filteredData = filteredData.filter(item =>
          item.module === systemLogsSearchParams.module
        );
      }

      if (systemLogsSearchParams.user) {
        filteredData = filteredData.filter(item =>
          item.user.includes(systemLogsSearchParams.user)
        );
      }

      // 时间范围筛选
      if (systemLogsSearchParams.startTime && systemLogsSearchParams.endTime) {
        // 这里可以添加时间范围筛选逻辑
      }

      set({ systemLogsData: filteredData });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      set({ systemLogsLoading: false });
    }
  },
});