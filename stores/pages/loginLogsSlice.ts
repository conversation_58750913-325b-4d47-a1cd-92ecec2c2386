import { StateCreator } from 'zustand';
import { AppStore } from '../types';

// 定义登录日志项类型
export interface LoginLogItem {
  key: number;
  username: string;
  ip: string;
  location: string;
  time: string;
  status: string;
  browser: string;
  os: string;
  remark: string;
}

// 定义 LoginLogsState 类型
export interface LoginLogsState {
  loginLogsData: LoginLogItem[];
  loginLogsLoading: boolean;
  loginLogsSorting: { id: string; desc: boolean }[];
  loginLogsPagination: {
    pageIndex: number;
    pageSize: number;
  };
  loginLogsColumnFilters: { id: string; value: unknown }[];
  loginLogsGlobalFilter: string;
  loginLogsSearchParams: {
    username: string;
    ip: string;
    location: string;
    status: string;
    startTime: string;
    endTime: string;
  };
}

// 定义 LoginLogsActions 类型
export interface LoginLogsActions {
  setLoginLogsData: (data: LoginLogItem[]) => void;
  setLoginLogsLoading: (loading: boolean) => void;
  setLoginLogsSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setLoginLogsPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setLoginLogsColumnFilters: (filters: { id: string; value: unknown }[]) => void;
  setLoginLogsGlobalFilter: (filter: string) => void;
  setLoginLogsSearchParams: (params: Partial<LoginLogsState['loginLogsSearchParams']>) => void;
  resetLoginLogsSearchParams: () => void;
  loadLoginLogsData: () => Promise<void>;
}

// 默认状态
const defaultState: LoginLogsState = {
  loginLogsData: [],
  loginLogsLoading: false,
  loginLogsSorting: [],
  loginLogsPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  loginLogsColumnFilters: [],
  loginLogsGlobalFilter: '',
  loginLogsSearchParams: {
    username: '',
    ip: '',
    location: '',
    status: '',
    startTime: '',
    endTime: '',
  },
};

// 模拟数据
const mockData = Array.from({ length: 50 }).map((_, i) => ({
  key: i + 1,
  username: i % 3 === 0 ? "admin" : "user" + i,
  ip: `192.168.1.${i + 10}`,
  location: i % 2 === 0 ? "中国北京" : "中国上海",
  time: `2024-07-01 12:${(i % 60).toString().padStart(2, "0")}:00`,
  status: i % 5 === 0 ? "失败" : "成功",
  browser: i % 2 === 0 ? "Chrome" : "Firefox",
  os: i % 2 === 0 ? "Windows" : "macOS",
  remark: i % 5 === 0 ? "密码错误" : "-",
}));

// 创建 LoginLogsSlice
export const createLoginLogsSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never], ['zustand/persist', unknown]],
  [],
  LoginLogsState & LoginLogsActions
> = (set, get) => ({
  ...defaultState,

  setLoginLogsData: (data) => set({ loginLogsData: data }),
  setLoginLogsLoading: (loading) => set({ loginLogsLoading: loading }),
  setLoginLogsSorting: (sorting) => set({ loginLogsSorting: sorting }),
  setLoginLogsPagination: (pagination) => set({ loginLogsPagination: pagination }),
  setLoginLogsColumnFilters: (columnFilters) => set({ loginLogsColumnFilters: columnFilters }),
  setLoginLogsGlobalFilter: (globalFilter) => set({ loginLogsGlobalFilter: globalFilter }),
  setLoginLogsSearchParams: (params) => set((state) => ({
    loginLogsSearchParams: { ...state.loginLogsSearchParams, ...params }
  })),
  resetLoginLogsSearchParams: () => set({
    loginLogsSearchParams: defaultState.loginLogsSearchParams,
    loginLogsGlobalFilter: defaultState.loginLogsGlobalFilter
  }),

  loadLoginLogsData: async () => {
    set({ loginLogsLoading: true });
    try {
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 获取当前状态
      const { loginLogsSearchParams } = get();

      // 模拟筛选和搜索
      let filteredData = [...mockData];

      // 根据搜索条件筛选
      if (loginLogsSearchParams.username) {
        filteredData = filteredData.filter(item =>
          item.username.includes(loginLogsSearchParams.username)
        );
      }

      if (loginLogsSearchParams.ip) {
        filteredData = filteredData.filter(item =>
          item.ip.includes(loginLogsSearchParams.ip)
        );
      }

      if (loginLogsSearchParams.location) {
        filteredData = filteredData.filter(item =>
          item.location === loginLogsSearchParams.location
        );
      }

      if (loginLogsSearchParams.status) {
        filteredData = filteredData.filter(item =>
          item.status === loginLogsSearchParams.status
        );
      }

      // 时间范围筛选
      if (loginLogsSearchParams.startTime && loginLogsSearchParams.endTime) {
        const startTime = new Date(loginLogsSearchParams.startTime);
        const endTime = new Date(loginLogsSearchParams.endTime);
        
        filteredData = filteredData.filter(item => {
          const itemTime = new Date(item.time);
          return itemTime >= startTime && itemTime <= endTime;
        });
      }

      set({ loginLogsData: filteredData });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      set({ loginLogsLoading: false });
    }
  },
});