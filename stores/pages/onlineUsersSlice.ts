/**
 * 在线用户管理 Zustand Slice
 * 处理在线用户数据、筛选状态、加载状态等
 */

import { StateCreator } from 'zustand';
import { useMemo } from 'react';
import type { SortingState, PaginationState, ColumnFiltersState } from '@tanstack/react-table';

// 在线用户类型定义
export interface OnlineUser {
  id: number;
  序号: number;
  用户名: string;
  登录IP: string;
  登录地点: string;
  操作系统: string;
  浏览器类型: string;
  登录时间: string;
  状态: 'online' | 'idle' | 'away';
}

// 在线用户管理状态接口
export interface OnlineUsersState {
  // 在线用户数据
  onlineUsers: OnlineUser[];
  loading: boolean;
  error: string | null;
  
  // 筛选状态
  statusFilter: string;
  osFilter: string;
  locationFilter: string;
  searchText: string;
  
  // TanStackTable 状态
  onlineUsersSorting: SortingState;
  onlineUsersPagination: PaginationState;
  onlineUsersColumnFilters: ColumnFiltersState;
  onlineUsersGlobalFilter: string;
  
  // 数据初始化状态
  dataInitialized: boolean;
  
  // 自动刷新状态
  autoRefreshEnabled: boolean;
  refreshInterval: number; // 秒
  
  // 最后更新时间
  lastUpdated: number;
}

// 在线用户管理操作接口
export interface OnlineUsersActions {
  // 数据操作
  setOnlineUsers: (users: OnlineUser[]) => void;
  addOnlineUser: (user: OnlineUser) => void;
  removeOnlineUser: (userId: number) => void;
  updateOnlineUser: (userId: number, updates: Partial<OnlineUser>) => void;
  
  // 加载状态
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 筛选操作
  setStatusFilter: (status: string) => void;
  setOsFilter: (os: string) => void;
  setLocationFilter: (location: string) => void;
  setSearchText: (text: string) => void;
  
  // TanStackTable 状态操作
  setOnlineUsersSorting: (sorting: SortingState) => void;
  setOnlineUsersPagination: (pagination: PaginationState) => void;
  setOnlineUsersColumnFilters: (filters: ColumnFiltersState) => void;
  setOnlineUsersGlobalFilter: (filter: string) => void;
  
  // 批量筛选操作
  setFilters: (filters: {
    statusFilter?: string;
    osFilter?: string;
    locationFilter?: string;
    searchText?: string;
  }) => void;
  
  // 重置操作
  resetFilters: () => void;
  resetOnlineUsersTableFilters: () => void;
  resetOnlineUsers: () => void;
  
  // 数据初始化
  setDataInitialized: (initialized: boolean) => void;
  
  // 自动刷新控制
  setAutoRefreshEnabled: (enabled: boolean) => void;
  setRefreshInterval: (interval: number) => void;
  
  // 刷新数据
  refreshOnlineUsers: () => Promise<void>;
}

// 默认状态
const DEFAULT_STATE: OnlineUsersState = {
  onlineUsers: [],
  loading: false,
  error: null,
  statusFilter: 'all',
  osFilter: 'all',
  locationFilter: 'all',
  searchText: '',
  // TanStackTable 默认状态
  onlineUsersSorting: [],
  onlineUsersPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  onlineUsersColumnFilters: [],
  onlineUsersGlobalFilter: '',
  dataInitialized: false,
  autoRefreshEnabled: true,
  refreshInterval: 30, // 30秒
  lastUpdated: 0,
};

// 生成模拟在线用户数据
const generateMockOnlineUsers = (): OnlineUser[] => {
  const usernames = ['admin', 'common', 'user01', 'test_user', 'developer', 'manager', 'analyst', 'operator'];
  const ips = [
    '**************', '**************', '*************', '********',
    '***********', '************', '***************', '*******'
  ];
  const locations = [
    '中国河南省信阳市', '中国广东省深圳市', '中国北京市朝阳区', '中国上海市浦东新区',
    '中国江苏省南京市', '中国浙江省杭州市', '中国四川省成都市', '中国湖南省长沙市'
  ];
  const systems = ['macOS', 'Windows', 'Linux', 'iOS', 'Android'];
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge', 'Opera'];
  const statuses: ('online' | 'idle' | 'away')[] = ['online', 'idle', 'away'];

  return Array.from({ length: 8 }, (_, index) => ({
    id: index + 1,
    序号: index + 1,
    用户名: usernames[index % usernames.length],
    登录IP: ips[index % ips.length],
    登录地点: locations[index % locations.length],
    操作系统: systems[index % systems.length],
    浏览器类型: browsers[index % browsers.length],
    登录时间: new Date(Date.now() - Math.random() * 86400000).toLocaleString('zh-CN'),
    状态: statuses[index % statuses.length],
  }));
};

// 创建在线用户管理切片
export const createOnlineUsersSlice: StateCreator<
  OnlineUsersState & OnlineUsersActions,
  [],
  [],
  OnlineUsersState & OnlineUsersActions
> = (set, get) => ({
  ...DEFAULT_STATE,

  // 数据操作
  setOnlineUsers: (users: OnlineUser[]) => {
    set({
      onlineUsers: users,
      lastUpdated: Date.now(),
    });
  },

  addOnlineUser: (user: OnlineUser) => {
    const { onlineUsers } = get();
    set({
      onlineUsers: [...onlineUsers, user],
      lastUpdated: Date.now(),
    });
  },

  removeOnlineUser: (userId: number) => {
    const { onlineUsers } = get();
    set({
      onlineUsers: onlineUsers.filter(user => user.id !== userId),
      lastUpdated: Date.now(),
    });
  },

  updateOnlineUser: (userId: number, updates: Partial<OnlineUser>) => {
    const { onlineUsers } = get();
    const updatedUsers = onlineUsers.map(user =>
      user.id === userId ? { ...user, ...updates } : user
    );
    set({
      onlineUsers: updatedUsers,
      lastUpdated: Date.now(),
    });
  },

  // 加载状态
  setLoading: (loading: boolean) => {
    set({ loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  // 筛选操作
  setStatusFilter: (status: string) => {
    set({ statusFilter: status });
  },

  setOsFilter: (os: string) => {
    set({ osFilter: os });
  },

  setLocationFilter: (location: string) => {
    set({ locationFilter: location });
  },

  setSearchText: (text: string) => {
    set({ searchText: text });
  },

  // TanStackTable 状态操作
  setOnlineUsersSorting: (sorting: SortingState) => {
    set({ onlineUsersSorting: sorting });
  },

  setOnlineUsersPagination: (pagination: PaginationState) => {
    set({ onlineUsersPagination: pagination });
  },

  setOnlineUsersColumnFilters: (filters: ColumnFiltersState) => {
    set({ onlineUsersColumnFilters: filters });
  },

  setOnlineUsersGlobalFilter: (filter: string) => {
    set({ onlineUsersGlobalFilter: filter });
  },

  // 批量筛选操作
  setFilters: (filters) => {
    set((state) => ({
      ...state,
      ...filters,
    }));
  },

  // 重置操作
  resetFilters: () => {
    set({
      statusFilter: 'all',
      osFilter: 'all',
      locationFilter: 'all',
      searchText: '',
    });
  },

  resetOnlineUsersTableFilters: () => {
    set({
      onlineUsersSorting: [],
      onlineUsersPagination: {
        pageIndex: 0,
        pageSize: 10,
      },
      onlineUsersColumnFilters: [],
      onlineUsersGlobalFilter: '',
    });
  },

  resetOnlineUsers: () => {
    set({
      ...DEFAULT_STATE,
    });
  },

  // 数据初始化
  setDataInitialized: (initialized: boolean) => {
    set({ dataInitialized: initialized });
  },

  // 自动刷新控制
  setAutoRefreshEnabled: (enabled: boolean) => {
    set({ autoRefreshEnabled: enabled });
  },

  setRefreshInterval: (interval: number) => {
    set({ refreshInterval: interval });
  },

  // 刷新数据
  refreshOnlineUsers: async () => {
    try {
      set({ loading: true, error: null });
      console.log("📡 获取在线用户列表...");
      
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockData = generateMockOnlineUsers();
      set({
        onlineUsers: mockData,
        loading: false,
        dataInitialized: true,
        lastUpdated: Date.now(),
      });
      
      console.log("✅ 获取在线用户列表成功:", mockData.length, "个用户");
    } catch (error) {
      console.error("获取在线用户列表失败:", error);
      set({
        loading: false,
        error: error instanceof Error ? error.message : '获取在线用户列表失败',
      });
    }
  },
});
