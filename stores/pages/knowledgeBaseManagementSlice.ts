import { StateCreator } from 'zustand';
import { knowledgeBaseAPI } from '../../services/api';
import type { 
  KnowledgeBase, 
  KnowledgeBaseQueryParams,
  RAGTestResult,
  CreateKnowledgeBaseRequest,
  UpdateKnowledgeBaseRequest
} from '../../types/knowledgeBase';

// 知识库管理状态接口
export interface KnowledgeBaseManagementState {
  // 数据状态
  knowledgeBases: KnowledgeBase[];
  
  // 加载状态
  loading: boolean;
  saving: boolean;
  deleting: boolean;
  ragTestLoading: boolean;
  
  // 错误状态
  error: string | null;
  
  // 搜索和筛选
  searchKeyword: string;
  selectedType: string;
  selectedStatus: string;
  
  // 模态框状态
  formModalVisible: boolean;
  detailModalVisible: boolean;
  documentModalVisible: boolean;
  
  // 当前操作的知识库
  editingKnowledgeBase: KnowledgeBase | null;
  viewingKnowledgeBase: KnowledgeBase | null;
  
  // RAG测试相关
  kbActiveTab: string;
  selectedKnowledgeBase: string;
  testQuestion: string;
  ragTestResult: any;
  ragTestResults: RAGTestResult[];
  ragTestStats: {
    responseTime: number;
    embeddingTime: number;
    searchTime: number;
    totalResults: number;
  } | null;
  
  // 分页信息
  kbPagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 最后更新时间
  kbLastUpdated: string | null;
}

// 知识库管理操作接口
export interface KnowledgeBaseManagementActions {
  // 数据加载
  loadKnowledgeBases: (params?: KnowledgeBaseQueryParams) => Promise<void>;
  refreshKnowledgeBases: () => Promise<void>;
  
  // CRUD操作
  createKnowledgeBase: (data: CreateKnowledgeBaseRequest) => Promise<void>;
  updateKnowledgeBase: (id: string, data: UpdateKnowledgeBaseRequest) => Promise<void>;
  deleteKnowledgeBase: (id: string) => Promise<void>;
  batchDeleteKnowledgeBases: (ids: string[]) => Promise<void>;
  
  // RAG测试
  performRAGTest: () => Promise<void>;
  
  // UI状态管理
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  setRagTestLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 搜索和筛选
  setSearchKeyword: (keyword: string) => void;
  setSelectedType: (type: string) => void;
  setSelectedStatus: (status: string) => void;
  
  // 模态框控制
  setFormModalVisible: (visible: boolean) => void;
  setDetailModalVisible: (visible: boolean) => void;
  setDocumentModalVisible: (visible: boolean) => void;
  
  // 当前操作的知识库
  setEditingKnowledgeBase: (kb: KnowledgeBase | null) => void;
  setViewingKnowledgeBase: (kb: KnowledgeBase | null) => void;
  
  // RAG测试状态
  setKbActiveTab: (tab: string) => void;
  setSelectedKnowledgeBase: (id: string) => void;
  setTestQuestion: (question: string) => void;
  setRagTestResult: (result: any) => void;
  setRagTestResults: (results: RAGTestResult[]) => void;
  setRagTestStats: (stats: KnowledgeBaseManagementState['ragTestStats']) => void;
  
  // 分页控制
  setKbPagination: (pagination: Partial<KnowledgeBaseManagementState['kbPagination']>) => void;
  
  // 重置状态
  resetKnowledgeBaseManagement: () => void;
}

// 默认状态
const defaultState: KnowledgeBaseManagementState = {
  knowledgeBases: [],
  loading: false,
  saving: false,
  deleting: false,
  ragTestLoading: false,
  error: null,
  searchKeyword: '',
  selectedType: 'all',
  selectedStatus: 'all',
  formModalVisible: false,
  detailModalVisible: false,
  documentModalVisible: false,
  editingKnowledgeBase: null,
  viewingKnowledgeBase: null,
  kbActiveTab: 'management',
  selectedKnowledgeBase: '',
  testQuestion: '',
  ragTestResult: null,
  ragTestResults: [],
  ragTestStats: null,
  kbPagination: {
    current: 1,
    pageSize: 12,
    total: 0,
    totalPages: 0,
  },
  kbLastUpdated: null,
};

// 模拟知识库数据
const mockKnowledgeBases: KnowledgeBase[] = [
  {
    id: "kb-001",
    name: "技术文档库",
    description: "包含前端、后端、数据库等技术文档和最佳实践，为开发团队提供技术支持和参考资料。",
    type: "document",
    status: "ready",
    document_count: 156,
    vector_count: 2340,
    total_size: 52428800, // 50MB
    embedding_model: "text-embedding-ada-002",
    tags: ["技术", "开发", "文档"],
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-06-20T14:30:00Z",
    created_by: "张三",
    owner_id: "user-001",
  },
  {
    id: "kb-002",
    name: "产品手册库",
    description: "产品功能说明、用户指南、操作手册等产品相关文档，帮助用户更好地使用产品。",
    type: "document",
    status: "ready",
    document_count: 89,
    vector_count: 1567,
    total_size: 31457280, // 30MB
    embedding_model: "text-embedding-ada-002",
    tags: ["产品", "手册", "用户指南"],
    created_at: "2024-02-10T09:00:00Z",
    updated_at: "2024-06-18T16:20:00Z",
    created_by: "李四",
    owner_id: "user-002",
  },
  {
    id: "kb-003",
    name: "FAQ问答库",
    description: "常见问题解答集合，包含用户常问的问题和标准答案，提高客服效率。",
    type: "qa",
    status: "ready",
    document_count: 234,
    vector_count: 3456,
    total_size: 15728640, // 15MB
    embedding_model: "text-embedding-3-small",
    tags: ["FAQ", "问答", "客服"],
    created_at: "2024-03-05T14:00:00Z",
    updated_at: "2024-06-19T11:45:00Z",
    created_by: "王五",
    owner_id: "user-003",
  },
  {
    id: "kb-004",
    name: "法律法规库",
    description: "相关法律法规、政策文件、合规要求等法务资料，为业务决策提供法律支持。",
    type: "structured",
    status: "processing",
    document_count: 67,
    vector_count: 890,
    total_size: 41943040, // 40MB
    embedding_model: "text-embedding-3-large",
    tags: ["法律", "法规", "合规"],
    created_at: "2024-04-12T08:30:00Z",
    updated_at: "2024-06-21T09:15:00Z",
    created_by: "赵六",
    owner_id: "user-004",
  },
  {
    id: "kb-005",
    name: "培训资料库",
    description: "员工培训材料、课程内容、考试题库等培训相关资源，支持人力资源培训工作。",
    type: "document",
    status: "ready",
    document_count: 123,
    vector_count: 1890,
    total_size: 73400320, // 70MB
    embedding_model: "text-embedding-ada-002",
    tags: ["培训", "教育", "HR"],
    created_at: "2024-05-08T13:20:00Z",
    updated_at: "2024-06-20T10:30:00Z",
    created_by: "孙七",
    owner_id: "user-005",
  },
  {
    id: "kb-006",
    name: "市场分析库",
    description: "行业报告、市场调研、竞品分析等市场相关数据和分析报告。",
    type: "structured",
    status: "ready",
    document_count: 45,
    vector_count: 678,
    total_size: 26214400, // 25MB
    embedding_model: "text-embedding-3-small",
    tags: ["市场", "分析", "报告"],
    created_at: "2024-05-20T16:45:00Z",
    updated_at: "2024-06-19T14:20:00Z",
    created_by: "周八",
    owner_id: "user-006",
  },
  {
    id: "kb-007",
    name: "网页资源库",
    description: "收集的网页内容、在线资源、博客文章等互联网资料。",
    type: "web",
    status: "error",
    document_count: 78,
    vector_count: 1234,
    total_size: 20971520, // 20MB
    embedding_model: "text-embedding-ada-002",
    tags: ["网页", "资源", "博客"],
    created_at: "2024-06-01T11:00:00Z",
    updated_at: "2024-06-21T08:45:00Z",
    created_by: "吴九",
    owner_id: "user-007",
  },
  {
    id: "kb-008",
    name: "API文档库",
    description: "各种API接口文档、SDK说明、集成指南等开发者资源。",
    type: "document",
    status: "inactive",
    document_count: 92,
    vector_count: 1456,
    total_size: 18874368, // 18MB
    embedding_model: "text-embedding-3-large",
    tags: ["API", "SDK", "开发"],
    created_at: "2024-06-10T09:30:00Z",
    updated_at: "2024-06-20T15:10:00Z",
    created_by: "郑十",
    owner_id: "user-008",
  }
];

// 生成模拟RAG测试结果
const generateMockRAGResults = (query: string, knowledgeBaseId: string): RAGTestResult[] => {
  const knowledgeBase = mockKnowledgeBases.find(kb => kb.id === knowledgeBaseId);
  if (!knowledgeBase) return [];

  const mockResults: RAGTestResult[] = [
    {
      id: "result-001",
      document_id: "doc-001",
      document_name: "技术架构设计指南.pdf",
      chunk_id: "chunk-001",
      content: `关于${query}的详细说明：在现代软件架构中，${query}是一个重要的概念。它涉及到系统的可扩展性、可维护性和性能优化。通过合理的设计模式和最佳实践，我们可以构建出高质量的软件系统。`,
      similarity_score: 0.95,
      metadata: {
        page: 15,
        section: "第三章 架构设计原则",
        title: "系统架构最佳实践",
        source: "内部技术文档"
      },
      highlighted_content: `关于<mark>${query}</mark>的详细说明：在现代软件架构中，<mark>${query}</mark>是一个重要的概念。`
    },
    {
      id: "result-002",
      document_id: "doc-002",
      document_name: "开发规范手册.docx",
      chunk_id: "chunk-002",
      content: `${query}的实现方案包括多个方面：首先需要考虑技术选型，然后是架构设计，最后是具体的实现细节。在实际开发过程中，我们需要遵循一定的规范和标准，确保代码质量和项目的可维护性。`,
      similarity_score: 0.87,
      metadata: {
        page: 23,
        section: "第五章 实现规范",
        title: "开发实践指南",
        source: "开发团队文档"
      },
      highlighted_content: `<mark>${query}</mark>的实现方案包括多个方面：首先需要考虑技术选型，然后是架构设计。`
    },
    {
      id: "result-003",
      document_id: "doc-003",
      document_name: "常见问题解答.md",
      chunk_id: "chunk-003",
      content: `Q: 如何处理${query}相关的问题？\nA: 处理${query}问题时，建议采用以下步骤：1. 分析问题的根本原因；2. 制定解决方案；3. 实施并验证效果；4. 总结经验教训。这种系统性的方法可以帮助我们更好地解决类似问题。`,
      similarity_score: 0.82,
      metadata: {
        section: "技术问答",
        title: "常见技术问题",
        source: "FAQ文档"
      },
      highlighted_content: `Q: 如何处理<mark>${query}</mark>相关的问题？\nA: 处理<mark>${query}</mark>问题时，建议采用以下步骤。`
    }
  ];

  return mockResults;
};

// 创建知识库管理slice
export const createKnowledgeBaseManagementSlice: StateCreator<
  KnowledgeBaseManagementState & KnowledgeBaseManagementActions,
  [],
  [],
  KnowledgeBaseManagementState & KnowledgeBaseManagementActions
> = (set, get) => ({
  ...defaultState,

  // 数据加载
  loadKnowledgeBases: async (params?: KnowledgeBaseQueryParams) => {
    const { setLoading, setError, searchKeyword, selectedType, selectedStatus } = get();
    
    try {
      setLoading(true);
      setError(null);

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));

      // 过滤数据
      let filteredData = [...mockKnowledgeBases];

      // 搜索过滤
      const keyword = params?.search || searchKeyword;
      if (keyword) {
        filteredData = filteredData.filter(kb =>
          kb.name.toLowerCase().includes(keyword.toLowerCase()) ||
          kb.description.toLowerCase().includes(keyword.toLowerCase()) ||
          kb.tags.some((tag: string) => tag.toLowerCase().includes(keyword.toLowerCase()))
        );
      }

      // 类型过滤
      const type = params?.type || selectedType;
      if (type && type !== "all") {
        filteredData = filteredData.filter(kb => kb.type === type);
      }

      // 状态过滤
      const status = params?.status || selectedStatus;
      if (status && status !== "all") {
        filteredData = filteredData.filter(kb => kb.status === status);
      }

      set({
        knowledgeBases: filteredData,
        kbPagination: {
          ...get().kbPagination,
          total: filteredData.length,
        },
        kbLastUpdated: new Date().toISOString(),
      });
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      setError('加载知识库列表失败');
    } finally {
      setLoading(false);
    }
  },

  refreshKnowledgeBases: async () => {
    const { loadKnowledgeBases } = get();
    await loadKnowledgeBases();
  },

  // CRUD操作
  createKnowledgeBase: async (data: CreateKnowledgeBaseRequest) => {
    const { setSaving, setError, refreshKnowledgeBases } = get();
    
    try {
      setSaving(true);
      setError(null);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的API
      // await knowledgeBaseAPI.createKnowledgeBase(data);
      
      await refreshKnowledgeBases();
    } catch (error) {
      console.error('创建知识库失败:', error);
      setError('创建知识库失败');
      throw error;
    } finally {
      setSaving(false);
    }
  },

  updateKnowledgeBase: async (id: string, data: UpdateKnowledgeBaseRequest) => {
    const { setSaving, setError, refreshKnowledgeBases } = get();
    
    try {
      setSaving(true);
      setError(null);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的API
      // await knowledgeBaseAPI.updateKnowledgeBase(id, data);
      
      await refreshKnowledgeBases();
    } catch (error) {
      console.error('更新知识库失败:', error);
      setError('更新知识库失败');
      throw error;
    } finally {
      setSaving(false);
    }
  },

  deleteKnowledgeBase: async (id: string) => {
    const { setDeleting, setError, refreshKnowledgeBases } = get();
    
    try {
      setDeleting(true);
      setError(null);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // 这里应该调用实际的API
      // await knowledgeBaseAPI.deleteKnowledgeBase(id);
      
      await refreshKnowledgeBases();
    } catch (error) {
      console.error('删除知识库失败:', error);
      setError('删除知识库失败');
      throw error;
    } finally {
      setDeleting(false);
    }
  },

  batchDeleteKnowledgeBases: async (ids: string[]) => {
    const { setDeleting, setError, refreshKnowledgeBases } = get();
    
    try {
      setDeleting(true);
      setError(null);

      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // 这里应该调用实际的API
      // await knowledgeBaseAPI.batchDeleteKnowledgeBases(ids);
      
      await refreshKnowledgeBases();
    } catch (error) {
      console.error('批量删除知识库失败:', error);
      setError('批量删除知识库失败');
      throw error;
    } finally {
      setDeleting(false);
    }
  },

  // RAG测试
  performRAGTest: async () => {
    const { 
      setRagTestLoading, 
      setError, 
      selectedKnowledgeBase, 
      testQuestion,
      setRagTestResult,
      setRagTestResults,
      setRagTestStats
    } = get();
    
    if (!selectedKnowledgeBase || !testQuestion.trim()) {
      setError('请选择知识库并输入测试问题');
      return;
    }

    try {
      setRagTestLoading(true);
      setError(null);

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 生成模拟结果
      const results = generateMockRAGResults(testQuestion, selectedKnowledgeBase);
      
      // 创建模拟的RAG测试结果
      const mockResult = {
        answer: `基于您的问题"${testQuestion}"，我从知识库中找到了相关信息。这是一个综合性的回答，结合了多个文档的内容来为您提供准确的答案。`,
        relatedDocs: results.map(result => ({
          filename: result.document_name,
          similarity: result.similarity_score
        })),
        responseTime: Math.floor(Math.random() * 500) + 800
      };

      setRagTestResult(mockResult);

      // 模拟性能统计
      const stats = {
        responseTime: mockResult.responseTime,
        embeddingTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
        searchTime: Math.floor(Math.random() * 200) + 100,   // 100-300ms
        totalResults: results.length
      };

      setRagTestResults(results);
      setRagTestStats(stats);
    } catch (error) {
      console.error('RAG测试失败:', error);
      setError('RAG测试失败，请重试');
      throw error;
    } finally {
      setRagTestLoading(false);
    }
  },

  // UI状态管理
  setLoading: (loading: boolean) => set({ loading }),
  setSaving: (saving: boolean) => set({ saving }),
  setDeleting: (deleting: boolean) => set({ deleting }),
  setRagTestLoading: (ragTestLoading: boolean) => set({ ragTestLoading }),
  setError: (error: string | null) => set({ error }),

  // 搜索和筛选
  setSearchKeyword: (searchKeyword: string) => set({ searchKeyword }),
  setSelectedType: (selectedType: string) => set({ selectedType }),
  setSelectedStatus: (selectedStatus: string) => set({ selectedStatus }),

  // 模态框控制
  setFormModalVisible: (formModalVisible: boolean) => set({ formModalVisible }),
  setDetailModalVisible: (detailModalVisible: boolean) => set({ detailModalVisible }),
  setDocumentModalVisible: (documentModalVisible: boolean) => set({ documentModalVisible }),

  // 当前操作的知识库
  setEditingKnowledgeBase: (editingKnowledgeBase: KnowledgeBase | null) => set({ editingKnowledgeBase }),
  setViewingKnowledgeBase: (viewingKnowledgeBase: KnowledgeBase | null) => set({ viewingKnowledgeBase }),

  // RAG测试状态
  setKbActiveTab: (kbActiveTab: string) => set({ kbActiveTab }),
  setSelectedKnowledgeBase: (selectedKnowledgeBase: string) => set({ selectedKnowledgeBase }),
  setTestQuestion: (testQuestion: string) => set({ testQuestion }),
  setRagTestResult: (ragTestResult: any) => set({ ragTestResult }),
  setRagTestResults: (ragTestResults: RAGTestResult[]) => set({ ragTestResults }),
  setRagTestStats: (ragTestStats: KnowledgeBaseManagementState['ragTestStats']) => set({ ragTestStats }),

  // 分页控制
  setKbPagination: (pagination: Partial<KnowledgeBaseManagementState['kbPagination']>) => 
    set(state => ({ 
      kbPagination: { ...state.kbPagination, ...pagination } 
    })),

  // 重置状态
  resetKnowledgeBaseManagement: () => set(defaultState),
});