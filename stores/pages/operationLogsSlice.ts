import { StateCreator } from 'zustand';
import { AppState } from '../types';

// 定义操作日志项类型
export interface OperationLogItem {
  key: number;
  user: string;
  type: string;
  content: string;
  method: string;
  url: string;
  ip: string;
  time: string;
  status: string;
  duration: number;
}

// 定义 OperationLogsState 类型
export interface OperationLogsState {
  operationLogsData: OperationLogItem[];
  operationLogsLoading: boolean;
  operationLogsSorting: { id: string; desc: boolean }[];
  operationLogsPagination: {
    pageIndex: number;
    pageSize: number;
  };
  operationLogsColumnFilters: { id: string; value: unknown }[];
  operationLogsGlobalFilter: string;
  operationLogsSearchParams: {
    user: string;
    type: string;
    method: string;
    status: string;
    startTime: string;
    endTime: string;
  };
}

// 定义 OperationLogsActions 类型
export interface OperationLogsActions {
  setOperationLogsData: (data: OperationLogItem[]) => void;
  setOperationLogsLoading: (loading: boolean) => void;
  setOperationLogsSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setOperationLogsPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setOperationLogsColumnFilters: (filters: { id: string; value: unknown }[]) => void;
  setOperationLogsGlobalFilter: (filter: string) => void;
  setOperationLogsSearchParams: (params: Partial<OperationLogsState['operationLogsSearchParams']>) => void;
  resetOperationLogsSearchParams: () => void;
  loadOperationLogsData: () => Promise<void>;
}

// 默认状态
const defaultState: OperationLogsState = {
  operationLogsData: [],
  operationLogsLoading: false,
  operationLogsSorting: [],
  operationLogsPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  operationLogsColumnFilters: [],
  operationLogsGlobalFilter: '',
  operationLogsSearchParams: {
    user: '',
    type: '',
    method: '',
    status: '',
    startTime: '',
    endTime: '',
  },
};

// 模拟数据
const mockData = Array.from({ length: 50 }).map((_, i) => ({
  key: i + 1,
  user: i % 3 === 0 ? "admin" : "user" + i,
  type: i % 2 === 0 ? "新增" : "删除",
  content: i % 2 === 0 ? "添加数据" : "删除数据",
  method: i % 2 === 0 ? "POST" : "DELETE",
  url: "/api/data" + i,
  ip: `192.168.1.${i + 10}`,
  time: `2024-07-01 13:${(i % 60).toString().padStart(2, "0")}:00`,
  status: i % 5 === 0 ? "失败" : "成功",
  duration: 100 + i * 3,
}));

// 创建 OperationLogsSlice
export const createOperationLogsSlice: StateCreator<
  AppStore,
  [['zustand/devtools', never], ['zustand/persist', unknown]],
  [],
  OperationLogsState & OperationLogsActions
> = (set, get) => ({
  ...defaultState,

  setOperationLogsData: (data) => set({ operationLogsData: data }),
  setOperationLogsLoading: (loading) => set({ operationLogsLoading: loading }),
  setOperationLogsSorting: (sorting) => set({ operationLogsSorting: sorting }),
  setOperationLogsPagination: (pagination) => set({ operationLogsPagination: pagination }),
  setOperationLogsColumnFilters: (columnFilters) => set({ operationLogsColumnFilters: columnFilters }),
  setOperationLogsGlobalFilter: (globalFilter) => set({ operationLogsGlobalFilter: globalFilter }),
  setOperationLogsSearchParams: (params) => set((state) => ({
    operationLogsSearchParams: { ...state.operationLogsSearchParams, ...params }
  })),
  resetOperationLogsSearchParams: () => set({
    operationLogsSearchParams: defaultState.operationLogsSearchParams,
    operationLogsGlobalFilter: defaultState.operationLogsGlobalFilter
  }),

  loadOperationLogsData: async () => {
    set({ operationLogsLoading: true });
    try {
      // 模拟延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 获取当前状态
      const { operationLogsSearchParams } = get();

      // 模拟筛选和搜索
      let filteredData = [...mockData];

      // 根据搜索条件筛选
      if (operationLogsSearchParams.user) {
        filteredData = filteredData.filter(item =>
          item.user.includes(operationLogsSearchParams.user)
        );
      }

      if (operationLogsSearchParams.type) {
        filteredData = filteredData.filter(item =>
          item.type === operationLogsSearchParams.type
        );
      }

      if (operationLogsSearchParams.method) {
        filteredData = filteredData.filter(item =>
          item.method === operationLogsSearchParams.method
        );
      }

      if (operationLogsSearchParams.status) {
        filteredData = filteredData.filter(item =>
          item.status === operationLogsSearchParams.status
        );
      }

      // 时间范围筛选
      if (operationLogsSearchParams.startTime && operationLogsSearchParams.endTime) {
        // 这里可以添加时间范围筛选逻辑
      }

      set({ operationLogsData: filteredData });
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      set({ operationLogsLoading: false });
    }
  },
});