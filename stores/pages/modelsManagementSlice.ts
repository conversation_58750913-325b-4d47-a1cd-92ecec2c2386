/**
 * 模型管理 Zustand Slice
 * 处理模型数据、API源数据、筛选状态、加载状态等
 */

import { StateCreator } from 'zustand';
import { useMemo } from 'react';

// 导入模型相关类型
export interface Model {
  id: string;
  name: string;
  type: string;
  vendor: string;
  vendorId: string;
  apiSourceId: string;
  description?: string;
  status: boolean;
  enableVision: boolean;
  enableFunctionCalling: boolean;
  enableInference: boolean;
  enableOnline: boolean;
  parameters: ModelParameter[];
  createdAt: string;
  updatedAt: string;
}

export interface ModelParameter {
  name: string;
  value: string;
  type?: string;
  description?: string;
}

// API 源类型定义
export enum ApiSourceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error'
}

export interface ApiSource {
  id: string;
  name: string;
  description?: string;
  type: string;
  apiUrl: string;
  apiKey?: string;
  status: ApiSourceStatus;
  isActive?: boolean;
  provider?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 模型管理状态接口
export interface ModelsManagementState {
  // 模型数据
  modelsData: Model[];
  modelsLoading: boolean;
  modelsError: string | null;
  
  // API源数据
  apiSourcesData: ApiSource[];
  apiSourcesLoading: boolean;
  apiSourcesError: string | null;
  
  // 当前活动标签
  activeTab: 'models' | 'apiSources';
  
  // 模型筛选状态
  modelsSearchText: string;
  modelsTypeFilter: string;
  modelsVendorFilter: string;
  modelsStatusFilter: string;
  
  // API源筛选状态
  apiSourcesSearchText: string;
  apiSourcesTypeFilter: string;
  apiSourcesStatusFilter: string;
  
  // 模型表格状态
  modelsSorting: { id: string; desc: boolean }[];
  modelsPagination: {
    pageIndex: number;
    pageSize: number;
  };
  modelsColumnFilters: { id: string; value: unknown }[];
  modelsGlobalFilter: string;
  
  // API源表格状态
  apiSourcesSorting: { id: string; desc: boolean }[];
  apiSourcesPagination: {
    pageIndex: number;
    pageSize: number;
  };
  apiSourcesColumnFilters: { id: string; value: unknown }[];
  apiSourcesGlobalFilter: string;
  
  // 模态框状态
  isModelModalVisible: boolean;
  isApiSourceModalVisible: boolean;
  selectedModel: Model | null;
  selectedApiSource: ApiSource | null;
  
  // 数据初始化状态
  dataInitialized: boolean;
  
  // 最后更新时间
  modelsLastUpdated: number;
}

// 模型管理操作接口
export interface ModelsManagementActions {
  // 模型数据操作
  setModelsData: (models: Model[]) => void;
  addModel: (model: Model) => void;
  updateModel: (modelId: string, updates: Partial<Model>) => void;
  removeModel: (modelId: string) => void;
  toggleModelStatus: (modelId: string) => void;
  
  // API源数据操作
  setApiSourcesData: (apiSources: ApiSource[]) => void;
  addApiSource: (apiSource: ApiSource) => void;
  updateApiSource: (apiSourceId: string, updates: Partial<ApiSource>) => void;
  removeApiSource: (apiSourceId: string) => void;
  toggleApiSourceStatus: (apiSourceId: string) => void;
  
  // 加载状态
  setModelsLoading: (loading: boolean) => void;
  setApiSourcesLoading: (loading: boolean) => void;
  setModelsError: (error: string | null) => void;
  setApiSourcesError: (error: string | null) => void;
  
  // 标签切换
  setActiveTab: (tab: 'models' | 'apiSources') => void;
  
  // 模型筛选操作
  setModelsSearchText: (text: string) => void;
  setModelsTypeFilter: (type: string) => void;
  setModelsVendorFilter: (vendor: string) => void;
  setModelsStatusFilter: (status: string) => void;
  
  // API源筛选操作
  setApiSourcesSearchText: (text: string) => void;
  setApiSourcesTypeFilter: (type: string) => void;
  setApiSourcesStatusFilter: (status: string) => void;
  
  // 批量筛选操作
  setModelsFilters: (filters: {
    searchText?: string;
    typeFilter?: string;
    vendorFilter?: string;
    statusFilter?: string;
  }) => void;
  
  setApiSourcesFilters: (filters: {
    searchText?: string;
    typeFilter?: string;
    statusFilter?: string;
  }) => void;
  
  // 表格状态操作
  setModelsSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setModelsPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setModelsColumnFilters: (filters: { id: string; value: unknown }[]) => void;
  setModelsGlobalFilter: (filter: string) => void;
  
  setApiSourcesSorting: (sorting: { id: string; desc: boolean }[]) => void;
  setApiSourcesPagination: (pagination: { pageIndex: number; pageSize: number }) => void;
  setApiSourcesColumnFilters: (filters: { id: string; value: unknown }[]) => void;
  setApiSourcesGlobalFilter: (filter: string) => void;
  
  // 模态框操作
  setIsModelModalVisible: (visible: boolean) => void;
  setIsApiSourceModalVisible: (visible: boolean) => void;
  setSelectedModel: (model: Model | null) => void;
  setSelectedApiSource: (apiSource: ApiSource | null) => void;
  
  // 重置操作
  resetModelsFilters: () => void;
  resetApiSourcesFilters: () => void;
  resetModelsData: () => void;
  resetApiSourcesData: () => void;
  
  // 数据初始化
  setDataInitialized: (initialized: boolean) => void;
  
  // 刷新数据
  refreshModelsData: () => Promise<void>;
  refreshApiSourcesData: () => Promise<void>;
  refreshAllData: () => Promise<void>;
}

// 默认状态
const DEFAULT_STATE: ModelsManagementState = {
  modelsData: [],
  modelsLoading: false,
  modelsError: null,
  apiSourcesData: [],
  apiSourcesLoading: false,
  apiSourcesError: null,
  activeTab: 'models',
  modelsSearchText: '',
  modelsTypeFilter: 'all',
  modelsVendorFilter: 'all',
  modelsStatusFilter: 'all',
  apiSourcesSearchText: '',
  apiSourcesTypeFilter: 'all',
  apiSourcesStatusFilter: 'all',
  modelsSorting: [],
  modelsPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  modelsColumnFilters: [],
  modelsGlobalFilter: '',
  apiSourcesSorting: [],
  apiSourcesPagination: {
    pageIndex: 0,
    pageSize: 10,
  },
  apiSourcesColumnFilters: [],
  apiSourcesGlobalFilter: '',
  isModelModalVisible: false,
  isApiSourceModalVisible: false,
  selectedModel: null,
  selectedApiSource: null,
  dataInitialized: false,
  modelsLastUpdated: 0,
};

// 生成模拟模型数据
const generateMockModels = (): Model[] => {
  return [
    {
      id: '1',
      name: 'GPT-4 Turbo',
      type: '语言模型',
      vendor: 'OpenAI',
      vendorId: '1',
      apiSourceId: '1',
      status: true,
      enableVision: false,
      enableFunctionCalling: true,
      enableInference: true,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '2',
      name: 'GPT-4 Vision',
      type: '多模态模型',
      vendor: 'OpenAI',
      vendorId: '1',
      apiSourceId: '1',
      status: true,
      enableVision: true,
      enableFunctionCalling: true,
      enableInference: true,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '3',
      name: 'Claude-3 Sonnet',
      type: '语言模型',
      vendor: 'Anthropic',
      vendorId: '2',
      apiSourceId: '2',
      status: true,
      enableVision: false,
      enableFunctionCalling: true,
      enableInference: true,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '4',
      name: 'Claude-3 Haiku',
      type: '语言模型',
      vendor: 'Anthropic',
      vendorId: '2',
      apiSourceId: '2',
      status: false,
      enableVision: false,
      enableFunctionCalling: false,
      enableInference: true,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '5',
      name: 'Gemini Pro',
      type: '多模态模型',
      vendor: 'Google',
      vendorId: '3',
      apiSourceId: '3',
      status: true,
      enableVision: true,
      enableFunctionCalling: true,
      enableInference: true,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '6',
      name: 'Text-Embedding-3-Large',
      type: '嵌入模型',
      vendor: 'OpenAI',
      vendorId: '1',
      apiSourceId: '1',
      status: true,
      enableVision: false,
      enableFunctionCalling: false,
      enableInference: false,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '7',
      name: 'DALL-E 3',
      type: '图像模型',
      vendor: 'OpenAI',
      vendorId: '1',
      apiSourceId: '1',
      status: true,
      enableVision: false,
      enableFunctionCalling: false,
      enableInference: false,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    },
    {
      id: '8',
      name: 'Whisper Large',
      type: '语音模型',
      vendor: 'OpenAI',
      vendorId: '1',
      apiSourceId: '1',
      status: false,
      enableVision: false,
      enableFunctionCalling: false,
      enableInference: false,
      enableOnline: false,
      parameters: [],
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28'
    }
  ];
};

// 生成模拟API源数据
const generateMockApiSources = (): ApiSource[] => {
  return [
    {
      id: '1',
      name: 'OpenAI API',
      type: 'openai',
      apiUrl: 'https://api.openai.com',
      apiKey: 'sk-***',
      status: ApiSourceStatus.ACTIVE,
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28',
      description: 'OpenAI官方API'
    },
    {
      id: '2',
      name: 'Claude API',
      type: 'anthropic',
      apiUrl: 'https://api.anthropic.com',
      apiKey: 'sk-ant-***',
      status: ApiSourceStatus.ACTIVE,
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28',
      description: 'Anthropic Claude API'
    },
    {
      id: '3',
      name: 'Gemini API',
      type: 'google',
      apiUrl: 'https://generativelanguage.googleapis.com',
      apiKey: 'AIza***',
      status: ApiSourceStatus.ACTIVE,
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28',
      description: 'Google Gemini API'
    },
    {
      id: '4',
      name: 'Azure OpenAI',
      type: 'azure',
      apiUrl: 'https://your-resource.openai.azure.com',
      apiKey: 'your-api-key',
      status: ApiSourceStatus.INACTIVE,
      createdAt: '2025/6/28',
      updatedAt: '2025/6/28',
      description: 'Azure OpenAI Service'
    }
  ];
};

// 创建模型管理切片
export const createModelsManagementSlice: StateCreator<
  ModelsManagementState & ModelsManagementActions,
  [],
  [],
  ModelsManagementState & ModelsManagementActions
> = (set, get) => ({
  ...DEFAULT_STATE,

  // 模型数据操作
  setModelsData: (models: Model[]) => {
    set({
      modelsData: models,
      modelsLastUpdated: Date.now(),
    });
  },

  addModel: (model: Model) => {
    const { modelsData } = get();
    set({
      modelsData: [...modelsData, model],
      modelsLastUpdated: Date.now(),
    });
  },

  updateModel: (modelId: string, updates: Partial<Model>) => {
    const { modelsData } = get();
    const updatedModels = modelsData.map(model =>
      model.id === modelId ? { ...model, ...updates, updatedAt: new Date().toLocaleDateString('zh-CN') } : model
    );
    set({
      modelsData: updatedModels,
      modelsLastUpdated: Date.now(),
    });
  },

  removeModel: (modelId: string) => {
    const { modelsData } = get();
    set({
      modelsData: modelsData.filter(model => model.id !== modelId),
      modelsLastUpdated: Date.now(),
    });
  },

  toggleModelStatus: (modelId: string) => {
    const { modelsData } = get();
    const updatedModels = modelsData.map(model =>
      model.id === modelId
        ? { ...model, status: !model.status, updatedAt: new Date().toLocaleDateString('zh-CN') }
        : model
    );
    set({
      modelsData: updatedModels,
      modelsLastUpdated: Date.now(),
    });
  },

  // API源数据操作
  setApiSourcesData: (apiSources: ApiSource[]) => {
    set({
      apiSourcesData: apiSources,
      modelsLastUpdated: Date.now(),
    });
  },

  addApiSource: (apiSource: ApiSource) => {
    const { apiSourcesData } = get();
    set({
      apiSourcesData: [...apiSourcesData, apiSource],
      modelsLastUpdated: Date.now(),
    });
  },

  updateApiSource: (apiSourceId: string, updates: Partial<ApiSource>) => {
    const { apiSourcesData } = get();
    const updatedApiSources = apiSourcesData.map(apiSource =>
      apiSource.id === apiSourceId
        ? { ...apiSource, ...updates, updatedAt: new Date().toLocaleDateString('zh-CN') }
        : apiSource
    );
    set({
      apiSourcesData: updatedApiSources,
      modelsLastUpdated: Date.now(),
    });
  },

  removeApiSource: (apiSourceId: string) => {
    const { apiSourcesData } = get();
    set({
      apiSourcesData: apiSourcesData.filter(apiSource => apiSource.id !== apiSourceId),
      modelsLastUpdated: Date.now(),
    });
  },

  toggleApiSourceStatus: (apiSourceId: string) => {
    const { apiSourcesData } = get();
    const updatedApiSources = apiSourcesData.map(apiSource =>
      apiSource.id === apiSourceId
        ? {
            ...apiSource,
            status: apiSource.status === ApiSourceStatus.ACTIVE
              ? ApiSourceStatus.INACTIVE
              : ApiSourceStatus.ACTIVE,
            updatedAt: new Date().toLocaleDateString('zh-CN')
          }
        : apiSource
    );
    set({
      apiSourcesData: updatedApiSources,
      modelsLastUpdated: Date.now(),
    });
  },

  // 加载状态
  setModelsLoading: (loading: boolean) => {
    set({ modelsLoading: loading });
  },

  setApiSourcesLoading: (loading: boolean) => {
    set({ apiSourcesLoading: loading });
  },

  setModelsError: (error: string | null) => {
    set({ modelsError: error });
  },

  setApiSourcesError: (error: string | null) => {
    set({ apiSourcesError: error });
  },

  // 标签切换
  setActiveTab: (tab: 'models' | 'apiSources') => {
    set({ activeTab: tab });
  },

  // 模型筛选操作
  setModelsSearchText: (text: string) => {
    set({ modelsSearchText: text });
  },

  setModelsTypeFilter: (type: string) => {
    set({ modelsTypeFilter: type });
  },

  setModelsVendorFilter: (vendor: string) => {
    set({ modelsVendorFilter: vendor });
  },

  setModelsStatusFilter: (status: string) => {
    set({ modelsStatusFilter: status });
  },

  // API源筛选操作
  setApiSourcesSearchText: (text: string) => {
    set({ apiSourcesSearchText: text });
  },

  setApiSourcesTypeFilter: (type: string) => {
    set({ apiSourcesTypeFilter: type });
  },

  setApiSourcesStatusFilter: (status: string) => {
    set({ apiSourcesStatusFilter: status });
  },

  // 批量筛选操作
  setModelsFilters: (filters) => {
    set((state) => ({
      ...state,
      ...filters,
    }));
  },

  setApiSourcesFilters: (filters) => {
    set((state) => ({
      ...state,
      ...filters,
    }));
  },

  // 表格状态操作
  setModelsSorting: (sorting) => {
    set({ modelsSorting: sorting });
  },

  setModelsPagination: (pagination) => {
    set({ modelsPagination: pagination });
  },

  setModelsColumnFilters: (filters) => {
    set({ modelsColumnFilters: filters });
  },

  setModelsGlobalFilter: (filter) => {
    set({ modelsGlobalFilter: filter });
  },

  setApiSourcesSorting: (sorting) => {
    set({ apiSourcesSorting: sorting });
  },

  setApiSourcesPagination: (pagination) => {
    set({ apiSourcesPagination: pagination });
  },

  setApiSourcesColumnFilters: (filters) => {
    set({ apiSourcesColumnFilters: filters });
  },

  setApiSourcesGlobalFilter: (filter) => {
    set({ apiSourcesGlobalFilter: filter });
  },

  // 模态框操作
  setIsModelModalVisible: (visible) => {
    set({ isModelModalVisible: visible });
  },

  setIsApiSourceModalVisible: (visible) => {
    set({ isApiSourceModalVisible: visible });
  },

  setSelectedModel: (model) => {
    set({ selectedModel: model });
  },

  setSelectedApiSource: (apiSource) => {
    set({ selectedApiSource: apiSource });
  },

  // 重置操作
  resetModelsFilters: () => {
    set({
      modelsSearchText: '',
      modelsTypeFilter: 'all',
      modelsVendorFilter: 'all',
      modelsStatusFilter: 'all',
      modelsGlobalFilter: '',
    });
  },

  resetApiSourcesFilters: () => {
    set({
      apiSourcesSearchText: '',
      apiSourcesTypeFilter: 'all',
      apiSourcesStatusFilter: 'all',
      apiSourcesGlobalFilter: '',
    });
  },

  resetModelsData: () => {
    set({
      modelsData: [],
      modelsLoading: false,
      modelsError: null,
    });
  },

  resetApiSourcesData: () => {
    set({
      apiSourcesData: [],
      apiSourcesLoading: false,
      apiSourcesError: null,
    });
  },

  // 数据初始化
  setDataInitialized: (initialized) => {
    set({ dataInitialized: initialized });
  },

  // 刷新数据
  refreshModelsData: async () => {
    try {
      set({ modelsLoading: true, modelsError: null });
      console.log("📡 获取模型列表...");

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 800));

      const mockData = generateMockModels();
      set({
        modelsData: mockData,
        modelsLoading: false,
        dataInitialized: true,
        modelsLastUpdated: Date.now(),
      });

      console.log("✅ 获取模型列表成功:", mockData.length, "个模型");
    } catch (error) {
      console.error("获取模型列表失败:", error);
      set({
        modelsLoading: false,
        modelsError: error instanceof Error ? error.message : '获取模型列表失败',
      });
    }
  },

  refreshApiSourcesData: async () => {
    try {
      set({ apiSourcesLoading: true, apiSourcesError: null });
      console.log("📡 获取API源列表...");

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 600));

      const mockData = generateMockApiSources();
      set({
        apiSourcesData: mockData,
        apiSourcesLoading: false,
        modelsLastUpdated: Date.now(),
      });

      console.log("✅ 获取API源列表成功:", mockData.length, "个API源");
    } catch (error) {
      console.error("获取API源列表失败:", error);
      set({
        apiSourcesLoading: false,
        apiSourcesError: error instanceof Error ? error.message : '获取API源列表失败',
      });
    }
  },

  refreshAllData: async () => {
    const { refreshModelsData, refreshApiSourcesData } = get();
    await Promise.all([
      refreshModelsData(),
      refreshApiSourcesData(),
    ]);
  },
});
