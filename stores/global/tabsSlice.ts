/**
 * 标签页状态切片
 * 迁移自 useTabsHistory hook
 */

import type { StateCreator } from 'zustand';
import type { AppStore, TabRecord, TabsState, TabsActions, StoreSlice } from '../types';
import { getPageInfo } from '../../utils/pageInfo';

// 默认配置
const DEFAULT_CONFIG = {
  maxTabs: 20,
  storageKey: 'v2-admin-tabs-history',
  excludePaths: ['/404', '/login', '/register', '/refresh-placeholder', '/redirect'],
  defaultPinnedPaths: ['/dashboard']
};

// 初始状态
const initialTabsState: TabsState = {
  tabs: [],
  isLoading: true,
  ...DEFAULT_CONFIG,
};

/**
 * 创建标签页状态切片
 */
export const createTabsSlice: StoreSlice<TabsState & TabsActions, AppStore> = (set, get) => ({
  // ==================== 状态 ====================
  ...initialTabsState,

  // ==================== 基础操作 ====================
  
  /**
   * 从 localStorage 加载标签页
   */
  loadTabs: () => {
    try {
      const { storageKey, maxTabs, defaultPinnedPaths } = get();
      const stored = localStorage.getItem(storageKey);
      
      if (stored) {
        const parsedTabs = JSON.parse(stored) as TabRecord[];
        // 按时间戳排序，固定标签优先
        const sortedTabs = parsedTabs.sort((a, b) => {
          if (a.pinned && !b.pinned) return -1;
          if (!a.pinned && b.pinned) return 1;
          return a.timestamp - b.timestamp;
        });
        
        set({ 
          tabs: sortedTabs.slice(0, maxTabs),
          isLoading: false 
        });
      } else {
        // 初始化时添加默认固定标签
        const defaultTabs = defaultPinnedPaths.map(path => {
          const pageInfo = getPageInfo(path);
          return {
            id: `tab-${path}-${Date.now()}`,
            path,
            title: pageInfo.title,
            icon: pageInfo.icon,
            description: pageInfo.description,
            pinned: true,
            timestamp: Date.now()
          };
        });
        
        set({ 
          tabs: defaultTabs,
          isLoading: false 
        });
        
        // 保存到 localStorage
        get().saveTabs(defaultTabs);
      }
    } catch (error) {
      console.error('Failed to load tabs from localStorage:', error);
      set({ 
        tabs: [],
        isLoading: false 
      });
    }
  },

  /**
   * 保存标签页到 localStorage
   */
  saveTabs: (tabs: TabRecord[]) => {
    try {
      const { storageKey } = get();
      localStorage.setItem(storageKey, JSON.stringify(tabs));
    } catch (error) {
      console.error('Failed to save tabs to localStorage:', error);
    }
  },

  /**
   * 添加标签页
   */
  addTab: (path: string) => {
    const { tabs, excludePaths, defaultPinnedPaths, maxTabs, saveTabs } = get();
    
    // 检查是否应该排除
    if (excludePaths.some(excludePath => path.includes(excludePath))) {
      return;
    }

    const pageInfo = getPageInfo(path);

    // 检查是否已存在
    const existingTabIndex = tabs.findIndex(tab => tab.path === path);
    
    if (existingTabIndex !== -1) {
      // 更新现有标签的时间戳
      const updatedTabs = tabs.map((tab, index) =>
        index === existingTabIndex
          ? { ...tab, timestamp: Date.now() }
          : tab
      );
      
      set({ tabs: updatedTabs });
      saveTabs(updatedTabs);
      return;
    }

    // 创建新标签
    const newTab: TabRecord = {
      id: `tab-${path}-${Date.now()}`,
      path,
      title: pageInfo.title,
      icon: pageInfo.icon,
      description: pageInfo.description,
      pinned: defaultPinnedPaths.includes(path),
      timestamp: Date.now()
    };

    // 添加新标签，限制数量
    const newTabs = [...tabs, newTab].slice(-maxTabs);
    
    set({ tabs: newTabs });
    saveTabs(newTabs);
  },

  /**
   * 移除标签页
   */
  removeTab: (tabId: string) => {
    const { tabs, saveTabs } = get();
    
    const tabToRemove = tabs.find(tab => tab.id === tabId);
    if (!tabToRemove || tabToRemove.pinned) {
      return;
    }

    const newTabs = tabs.filter(tab => tab.id !== tabId);
    
    set({ tabs: newTabs });
    saveTabs(newTabs);
  },

  // ==================== 标签页管理 ====================

  /**
   * 固定/取消固定标签页
   */
  pinTab: (tabId: string, pinned: boolean) => {
    const { tabs, saveTabs } = get();
    
    const updatedTabs = tabs.map(tab =>
      tab.id === tabId ? { ...tab, pinned } : tab
    );
    
    set({ tabs: updatedTabs });
    saveTabs(updatedTabs);
  },

  /**
   * 刷新标签页
   */
  refreshTab: (tabId: string, navigate: (path: string) => void, currentPath: string) => {
    const { tabs } = get();
    
    const tab = tabs.find(t => t.id === tabId);
    if (!tab) return;

    // 设置加载状态
    const updatedTabs = tabs.map(t =>
      t.id === tabId ? { ...t, loading: true } : t
    );
    set({ tabs: updatedTabs });

    const targetPath = tab.path;

    if (targetPath === currentPath) {
      // 当前页面刷新
      window.location.reload();
    } else {
      // 导航到目标页面
      navigate(targetPath);
    }

    // 清除加载状态
    setTimeout(() => {
      const currentTabs = get().tabs;
      const finalTabs = currentTabs.map(t =>
        t.id === tabId ? { ...t, loading: false } : t
      );
      set({ tabs: finalTabs });
    }, 500);
  },

  /**
   * 关闭其他标签页 - 修复版本
   */
  clearOtherTabs: (keepTabId: string, navigate?: (path: string) => void) => {
    const { tabs, saveTabs } = get();
    
    if (!keepTabId) {
      console.warn('keepTabId is required for clearOtherTabs');
      return;
    }

    const keepTab = tabs.find(tab => tab.id === keepTabId);
    if (!keepTab) {
      console.warn('Keep tab not found:', keepTabId);
      return;
    }

    // 过滤标签页：保留指定标签和固定标签
    const newTabs = tabs.filter(tab =>
      tab.id === keepTabId || tab.pinned
    );

    // 更新状态
    set({ tabs: newTabs });
    saveTabs(newTabs);

    // 处理导航
    if (navigate && keepTab.path !== window.location.pathname) {
      navigate(keepTab.path);
    }
  },

  /**
   * 关闭所有标签页
   */
  clearAllTabs: (navigate?: (path: string) => void) => {
    const { tabs, saveTabs } = get();
    
    // 只保留固定标签
    const newTabs = tabs.filter(tab => tab.pinned);
    
    set({ tabs: newTabs });
    saveTabs(newTabs);

    // 如果有固定标签，跳转到第一个固定标签
    if (newTabs.length > 0 && navigate) {
      navigate(newTabs[0].path);
    } else if (navigate) {
      // 没有固定标签，跳转到首页
      navigate('/dashboard');
    }
  },

  // ==================== 工具方法 ====================

  /**
   * 获取当前活动标签
   */
  getActiveTab: (currentPath: string) => {
    const { tabs } = get();
    return tabs.find(tab => tab.path === currentPath);
  },

  /**
   * 根据ID获取标签
   */
  getTabById: (tabId: string) => {
    const { tabs } = get();
    return tabs.find(tab => tab.id === tabId);
  },

  /**
   * 根据路径获取标签
   */
  getTabByPath: (path: string) => {
    const { tabs } = get();
    return tabs.find(tab => tab.path === path);
  },
});
