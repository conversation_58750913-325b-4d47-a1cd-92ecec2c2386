/**
 * 路由历史状态切片
 * 迁移自 useRouteHistory hook
 */

import type { StateCreator } from 'zustand';
import type { AppStore, RouteHistoryState, RouteHistoryActions, RouteHistoryRecord, StoreSlice } from '../types';

// 初始路由历史状态
const initialRouteHistoryState: RouteHistoryState = {
  history: [],
  maxHistory: 50,
  currentPath: '',
};

/**
 * 创建路由历史状态切片
 */
export const createRouteHistorySlice: StoreSlice<RouteHistoryState & RouteHistoryActions, AppStore> = (set, get) => ({
  // ==================== 状态 ====================
  ...initialRouteHistoryState,

  // ==================== 操作 ====================

  /**
   * 添加路由记录
   */
  addRoute: (route: Omit<RouteHistoryRecord, 'timestamp'>) => {
    const { history, maxHistory } = get();
    
    // 检查是否已存在相同路径的记录
    const existingIndex = history.findIndex(item => item.path === route.path);
    
    const newRecord: RouteHistoryRecord = {
      ...route,
      timestamp: Date.now(),
    };

    let newHistory: RouteHistoryRecord[];

    if (existingIndex !== -1) {
      // 更新现有记录，移到最前面
      newHistory = [
        newRecord,
        ...history.filter((_, index) => index !== existingIndex)
      ];
    } else {
      // 添加新记录到最前面
      newHistory = [newRecord, ...history];
    }

    // 限制历史记录数量
    if (newHistory.length > maxHistory) {
      newHistory = newHistory.slice(0, maxHistory);
    }

    set({ history: newHistory });
  },

  /**
   * 移除路由记录
   */
  removeRoute: (path: string) => {
    const { history } = get();
    const newHistory = history.filter(item => item.path !== path);
    set({ history: newHistory });
  },

  /**
   * 清空路由历史
   */
  clearHistory: () => {
    set({ history: [] });
  },

  /**
   * 设置当前路径
   */
  setCurrentPath: (path: string) => {
    set({ currentPath: path });
  },

  /**
   * 获取最近的路由记录
   */
  getRecentRoutes: (limit = 10) => {
    const { history } = get();
    return history
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  },
});

// ==================== 工具函数 ====================

/**
 * 格式化路由路径
 */
export const formatRoutePath = (path: string): string => {
  // 移除查询参数和哈希
  return path.split('?')[0].split('#')[0];
};

/**
 * 解析查询参数
 */
export const parseQueryParams = (search: string): Record<string, string> => {
  const params: Record<string, string> = {};
  
  if (search.startsWith('?')) {
    search = search.slice(1);
  }
  
  search.split('&').forEach(param => {
    const [key, value] = param.split('=');
    if (key) {
      params[decodeURIComponent(key)] = decodeURIComponent(value || '');
    }
  });
  
  return params;
};

/**
 * 解析路径参数
 */
export const parsePathParams = (
  pattern: string, 
  path: string
): Record<string, string> => {
  const params: Record<string, string> = {};
  
  const patternParts = pattern.split('/');
  const pathParts = path.split('/');
  
  if (patternParts.length !== pathParts.length) {
    return params;
  }
  
  patternParts.forEach((part, index) => {
    if (part.startsWith(':')) {
      const paramName = part.slice(1);
      params[paramName] = pathParts[index];
    }
  });
  
  return params;
};

/**
 * 创建路由记录
 */
export const createRouteRecord = (
  path: string,
  title: string,
  location?: Location
): Omit<RouteHistoryRecord, 'timestamp'> => {
  const cleanPath = formatRoutePath(path);
  
  let params: Record<string, string> = {};
  let query: Record<string, string> = {};
  
  if (location) {
    query = parseQueryParams(location.search);
    // 路径参数需要根据路由配置来解析，这里暂时留空
    // params = parsePathParams(routePattern, cleanPath);
  }
  
  return {
    path: cleanPath,
    title,
    params,
    query,
  };
};

/**
 * 检查路由是否应该被记录
 */
export const shouldRecordRoute = (path: string): boolean => {
  const excludePaths = [
    '/login',
    '/register',
    '/404',
    '/500',
    '/refresh-placeholder',
    '/redirect',
  ];
  
  return !excludePaths.some(excludePath => 
    path.includes(excludePath) || path === excludePath
  );
};

/**
 * 获取路由面包屑
 */
export const getRouteBreadcrumbs = (
  path: string,
  routeConfig: Record<string, { title: string; parent?: string }>
): Array<{ path: string; title: string }> => {
  const breadcrumbs: Array<{ path: string; title: string }> = [];
  
  const pathParts = path.split('/').filter(Boolean);
  let currentPath = '';
  
  pathParts.forEach(part => {
    currentPath += `/${part}`;
    const config = routeConfig[currentPath];
    
    if (config) {
      breadcrumbs.push({
        path: currentPath,
        title: config.title,
      });
    }
  });
  
  return breadcrumbs;
};

/**
 * 搜索路由历史
 */
export const searchRouteHistory = (
  history: RouteHistoryRecord[],
  query: string
): RouteHistoryRecord[] => {
  if (!query.trim()) {
    return history;
  }
  
  const lowerQuery = query.toLowerCase();
  
  return history.filter(record => 
    record.title.toLowerCase().includes(lowerQuery) ||
    record.path.toLowerCase().includes(lowerQuery)
  );
};

/**
 * 按日期分组路由历史
 */
export const groupRouteHistoryByDate = (
  history: RouteHistoryRecord[]
): Record<string, RouteHistoryRecord[]> => {
  const groups: Record<string, RouteHistoryRecord[]> = {};
  
  history.forEach(record => {
    const date = new Date(record.timestamp);
    const dateKey = date.toDateString();
    
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    
    groups[dateKey].push(record);
  });
  
  return groups;
};
