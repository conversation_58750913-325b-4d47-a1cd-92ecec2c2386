/**
 * 页面动画状态切片
 * 基于 Zustand 的全局页面动画状态管理
 */

import { StateCreator } from 'zustand';

// 动画类型枚举
export enum AnimationType {
  FADE_IN_UP = 'fadeInUp',
  FADE_IN_DOWN = 'fadeInDown',
  FADE_IN_LEFT = 'fadeInLeft',
  FADE_IN_RIGHT = 'fadeInRight',
  FADE_IN = 'fadeIn',
  SLIDE_IN_UP = 'slideInUp',
  SLIDE_IN_DOWN = 'slideInDown',
  SCALE_IN = 'scaleIn',
}

// 缓动函数类型
export enum EasingType {
  EASE_OUT = 'easeOut',
  EASE_IN = 'easeIn',
  EASE_IN_OUT = 'easeInOut',
  LINEAR = 'linear',
  SPRING = 'spring',
  BOUNCE = 'bounce',
}

// 动画配置接口
export interface AnimationConfig {
  type: AnimationType;
  duration: number;
  delay: number;
  easing: EasingType;
  distance: number; // 移动距离（像素）
  scale: number; // 缩放比例
  stagger: number; // 交错延迟（用于多个元素）
}

// 预设动画配置
export interface AnimationPreset {
  name: string;
  config: AnimationConfig;
  description: string;
}

// Framer Motion 动画变体
export interface MotionVariants {
  initial: Record<string, any>;
  animate: Record<string, any>;
  exit: Record<string, any>;
}

// Framer Motion 过渡配置
export interface MotionTransition {
  type: string;
  ease: string | number[];
  duration: number;
  delay: number;
  [key: string]: any;
}

// 页面动画状态接口
export interface PageAnimationState {
  // 当前全局动画配置
  globalConfig: AnimationConfig;
  
  // 预设动画配置
  presets: AnimationPreset[];
  
  // 当前激活的预设
  activePreset: string | null;
  
  // 是否启用动画
  enabled: boolean;
  
  // 是否启用减少动画（无障碍功能）
  reducedMotion: boolean;
  
  // 性能模式（在低性能设备上简化动画）
  performanceMode: boolean;
  
  // 调试模式
  debugMode: boolean;
}

// 页面动画操作接口
export interface PageAnimationActions {
  // 设置全局动画配置
  setGlobalConfig: (config: Partial<AnimationConfig>) => void;
  
  // 设置动画类型
  setAnimationType: (type: AnimationType) => void;
  
  // 设置动画持续时间
  setDuration: (duration: number) => void;
  
  // 设置动画延迟
  setDelay: (delay: number) => void;
  
  // 设置缓动函数
  setEasing: (easing: EasingType) => void;
  
  // 应用预设配置
  applyPreset: (presetName: string) => void;
  
  // 添加自定义预设
  addPreset: (preset: AnimationPreset) => void;
  
  // 删除预设
  removePreset: (presetName: string) => void;
  
  // 切换动画启用状态
  toggleAnimation: () => void;
  
  // 设置减少动画模式
  setReducedMotion: (enabled: boolean) => void;
  
  // 设置性能模式
  setPerformanceMode: (enabled: boolean) => void;
  
  // 切换调试模式
  toggleDebugMode: () => void;
  
  // 重置为默认配置
  resetToDefault: () => void;
  
  // 获取 Framer Motion 配置
  getMotionConfig: (customConfig?: Partial<AnimationConfig>) => {
    variants: MotionVariants;
    transition: MotionTransition;
  };
}

// 页面动画切片类型
export type PageAnimationSlice = PageAnimationState & PageAnimationActions;

// 默认动画配置
const DEFAULT_CONFIG: AnimationConfig = {
  type: AnimationType.FADE_IN_UP,
  duration: 0.6,
  delay: 0,
  easing: EasingType.EASE_OUT,
  distance: 30,
  scale: 1,
  stagger: 0.1,
};

// 预设动画配置
const DEFAULT_PRESETS: AnimationPreset[] = [
  {
    name: 'default',
    config: DEFAULT_CONFIG,
    description: '默认淡入上移动画',
  },
  {
    name: 'fast',
    config: {
      ...DEFAULT_CONFIG,
      duration: 0.3,
      distance: 20,
    },
    description: '快速动画',
  },
  {
    name: 'slow',
    config: {
      ...DEFAULT_CONFIG,
      duration: 0.9,
      distance: 40,
    },
    description: '慢速动画',
  },
  {
    name: 'subtle',
    config: {
      ...DEFAULT_CONFIG,
      duration: 0.4,
      distance: 15,
      easing: EasingType.EASE_IN_OUT,
    },
    description: '微妙动画',
  },
  {
    name: 'dramatic',
    config: {
      type: AnimationType.SCALE_IN,
      duration: 0.8,
      delay: 0,
      easing: EasingType.SPRING,
      distance: 50,
      scale: 0.8,
      stagger: 0.15,
    },
    description: '戏剧性缩放动画',
  },
];

/**
 * 创建页面动画状态切片
 */
export const createPageAnimationSlice: StateCreator<
  PageAnimationSlice,
  [],
  [],
  PageAnimationSlice
> = (set, get) => ({
  // 初始状态
  globalConfig: DEFAULT_CONFIG,
  presets: DEFAULT_PRESETS,
  activePreset: 'default',
  enabled: true,
  reducedMotion: false,
  performanceMode: false,
  debugMode: false,

  // 操作函数
  setGlobalConfig: (config) =>
    set((state) => ({
      globalConfig: { ...state.globalConfig, ...config },
      activePreset: null, // 自定义配置时清除预设
    })),

  setAnimationType: (type) =>
    set((state) => ({
      globalConfig: { ...state.globalConfig, type },
      activePreset: null,
    })),

  setDuration: (duration) =>
    set((state) => ({
      globalConfig: { ...state.globalConfig, duration },
      activePreset: null,
    })),

  setDelay: (delay) =>
    set((state) => ({
      globalConfig: { ...state.globalConfig, delay },
      activePreset: null,
    })),

  setEasing: (easing) =>
    set((state) => ({
      globalConfig: { ...state.globalConfig, easing },
      activePreset: null,
    })),

  applyPreset: (presetName) => {
    const preset = get().presets.find((p) => p.name === presetName);
    if (preset) {
      set({
        globalConfig: preset.config,
        activePreset: presetName,
      });
    }
  },

  addPreset: (preset) =>
    set((state) => ({
      presets: [...state.presets.filter((p) => p.name !== preset.name), preset],
    })),

  removePreset: (presetName) =>
    set((state) => ({
      presets: state.presets.filter((p) => p.name !== presetName),
      activePreset: state.activePreset === presetName ? null : state.activePreset,
    })),

  toggleAnimation: () =>
    set((state) => ({
      enabled: !state.enabled,
    })),

  setReducedMotion: (enabled) =>
    set({
      reducedMotion: enabled,
    }),

  setPerformanceMode: (enabled) =>
    set({
      performanceMode: enabled,
    }),

  toggleDebugMode: () =>
    set((state) => ({
      debugMode: !state.debugMode,
    })),

  resetToDefault: () =>
    set({
      globalConfig: DEFAULT_CONFIG,
      activePreset: 'default',
      enabled: true,
      reducedMotion: false,
      performanceMode: false,
      debugMode: false,
    }),

  getMotionConfig: (customConfig) => {
    const state = get();
    const config = customConfig ? { ...state.globalConfig, ...customConfig } : state.globalConfig;
    
    // 如果禁用动画或启用减少动画模式，返回简化配置
    if (!state.enabled || state.reducedMotion) {
      return {
        variants: {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        },
        transition: {
          type: 'tween',
          ease: 'linear',
          duration: 0.2,
          delay: 0,
        },
      };
    }

    // 性能模式下简化动画
    if (state.performanceMode) {
      return {
        variants: {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        },
        transition: {
          type: 'tween',
          ease: 'easeOut',
          duration: Math.min(config.duration, 0.3),
          delay: config.delay,
        },
      };
    }

    // 根据动画类型生成变体
    const variants = generateMotionVariants(config);
    const transition = generateMotionTransition(config);

    return { variants, transition };
  },
});

/**
 * 根据动画配置生成 Framer Motion 变体
 */
function generateMotionVariants(config: AnimationConfig): MotionVariants {
  const { type, distance, scale } = config;

  switch (type) {
    case AnimationType.FADE_IN_UP:
      return {
        initial: { opacity: 0, y: distance },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -distance },
      };

    case AnimationType.FADE_IN_DOWN:
      return {
        initial: { opacity: 0, y: -distance },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: distance },
      };

    case AnimationType.FADE_IN_LEFT:
      return {
        initial: { opacity: 0, x: -distance },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: distance },
      };

    case AnimationType.FADE_IN_RIGHT:
      return {
        initial: { opacity: 0, x: distance },
        animate: { opacity: 1, x: 0 },
        exit: { opacity: 0, x: -distance },
      };

    case AnimationType.FADE_IN:
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
      };

    case AnimationType.SLIDE_IN_UP:
      return {
        initial: { y: distance, opacity: 0.8 },
        animate: { y: 0, opacity: 1 },
        exit: { y: -distance, opacity: 0.8 },
      };

    case AnimationType.SLIDE_IN_DOWN:
      return {
        initial: { y: -distance, opacity: 0.8 },
        animate: { y: 0, opacity: 1 },
        exit: { y: distance, opacity: 0.8 },
      };

    case AnimationType.SCALE_IN:
      return {
        initial: { opacity: 0, scale },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale },
      };

    default:
      return {
        initial: { opacity: 0, y: distance },
        animate: { opacity: 1, y: 0 },
        exit: { opacity: 0, y: -distance },
      };
  }
}

/**
 * 根据动画配置生成 Framer Motion 过渡
 */
function generateMotionTransition(config: AnimationConfig): MotionTransition {
  const { duration, delay, easing } = config;

  const baseTransition = {
    duration,
    delay,
  };

  switch (easing) {
    case EasingType.EASE_OUT:
      return {
        ...baseTransition,
        type: 'tween',
        ease: 'easeOut',
      };

    case EasingType.EASE_IN:
      return {
        ...baseTransition,
        type: 'tween',
        ease: 'easeIn',
      };

    case EasingType.EASE_IN_OUT:
      return {
        ...baseTransition,
        type: 'tween',
        ease: 'easeInOut',
      };

    case EasingType.LINEAR:
      return {
        ...baseTransition,
        type: 'tween',
        ease: 'linear',
      };

    case EasingType.SPRING:
      return {
        type: 'spring',
        damping: 25,
        stiffness: 120,
        delay,
      };

    case EasingType.BOUNCE:
      return {
        type: 'spring',
        damping: 10,
        stiffness: 100,
        delay,
      };

    default:
      return {
        ...baseTransition,
        type: 'tween',
        ease: 'easeOut',
      };
  }
}
