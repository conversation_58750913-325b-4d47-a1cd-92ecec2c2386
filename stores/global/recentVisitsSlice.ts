/**
 * 最近访问状态切片
 * 迁移自 useRecentVisits hook
 */

import type { StateCreator } from 'zustand';
import type { AppStore, RecentVisitsState, RecentVisitsActions, RecentVisitRecord, StoreSlice } from '../types';

// 初始最近访问状态
const initialRecentVisitsState: RecentVisitsState = {
  visits: [],
  maxVisits: 20,
  storageKey: 'v2-admin-recent-visits',
};

/**
 * 创建最近访问状态切片
 */
export const createRecentVisitsSlice: StoreSlice<RecentVisitsState & RecentVisitsActions, AppStore> = (set, get) => ({
  // ==================== 状态 ====================
  ...initialRecentVisitsState,

  // ==================== 操作 ====================

  /**
   * 添加访问记录
   */
  addVisit: (path: string, title: string, icon?: string) => {
    const { visits, maxVisits } = get();
    
    // 检查是否已存在
    const existingIndex = visits.findIndex(visit => visit.path === path);
    
    if (existingIndex !== -1) {
      // 更新现有记录
      const existingVisit = visits[existingIndex];
      const updatedVisit: RecentVisitRecord = {
        ...existingVisit,
        title, // 更新标题
        icon, // 更新图标
        timestamp: Date.now(),
        visitCount: existingVisit.visitCount + 1,
      };
      
      // 移到最前面
      const newVisits = [
        updatedVisit,
        ...visits.filter((_, index) => index !== existingIndex)
      ];
      
      set({ visits: newVisits });
    } else {
      // 创建新记录
      const newVisit: RecentVisitRecord = {
        id: `visit-${path}-${Date.now()}`,
        path,
        title,
        icon,
        timestamp: Date.now(),
        visitCount: 1,
      };
      
      // 添加到最前面，限制数量
      const newVisits = [newVisit, ...visits].slice(0, maxVisits);
      set({ visits: newVisits });
    }
  },

  /**
   * 移除访问记录
   */
  removeVisit: (id: string) => {
    const { visits } = get();
    const newVisits = visits.filter(visit => visit.id !== id);
    set({ visits: newVisits });
  },

  /**
   * 清空访问记录
   */
  clearVisits: () => {
    set({ visits: [] });
  },

  /**
   * 获取最近访问记录
   */
  getRecentVisits: (limit = 10) => {
    const { visits } = get();
    return visits
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit);
  },

  /**
   * 获取最常访问记录
   */
  getMostVisited: (limit = 10) => {
    const { visits } = get();
    return visits
      .sort((a, b) => {
        // 先按访问次数排序，再按时间排序
        if (b.visitCount !== a.visitCount) {
          return b.visitCount - a.visitCount;
        }
        return b.timestamp - a.timestamp;
      })
      .slice(0, limit);
  },
});

// ==================== 工具函数 ====================

/**
 * 检查路径是否应该被记录
 */
export const shouldRecordVisit = (path: string): boolean => {
  const excludePaths = [
    '/login',
    '/register',
    '/404',
    '/500',
    '/refresh-placeholder',
    '/redirect',
  ];
  
  return !excludePaths.some(excludePath => 
    path.includes(excludePath) || path === excludePath
  );
};

/**
 * 格式化访问时间
 */
export const formatVisitTime = (timestamp: number): string => {
  const now = Date.now();
  const diff = now - timestamp;
  
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  
  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    const minutes = Math.floor(diff / minute);
    return `${minutes}分钟前`;
  } else if (diff < day) {
    const hours = Math.floor(diff / hour);
    return `${hours}小时前`;
  } else if (diff < week) {
    const days = Math.floor(diff / day);
    return `${days}天前`;
  } else if (diff < month) {
    const weeks = Math.floor(diff / week);
    return `${weeks}周前`;
  } else {
    const date = new Date(timestamp);
    return date.toLocaleDateString('zh-CN');
  }
};

/**
 * 获取访问频率描述
 */
export const getVisitFrequencyText = (visitCount: number): string => {
  if (visitCount === 1) {
    return '首次访问';
  } else if (visitCount < 5) {
    return '偶尔访问';
  } else if (visitCount < 10) {
    return '经常访问';
  } else if (visitCount < 20) {
    return '频繁访问';
  } else {
    return '常用页面';
  }
};

/**
 * 搜索访问记录
 */
export const searchVisitRecords = (
  visits: RecentVisitRecord[],
  query: string
): RecentVisitRecord[] => {
  if (!query.trim()) {
    return visits;
  }
  
  const lowerQuery = query.toLowerCase();
  
  return visits.filter(visit => 
    visit.title.toLowerCase().includes(lowerQuery) ||
    visit.path.toLowerCase().includes(lowerQuery)
  );
};

/**
 * 按访问频率分组
 */
export const groupVisitsByFrequency = (
  visits: RecentVisitRecord[]
): Record<string, RecentVisitRecord[]> => {
  const groups: Record<string, RecentVisitRecord[]> = {
    '常用页面': [],
    '频繁访问': [],
    '经常访问': [],
    '偶尔访问': [],
    '首次访问': [],
  };
  
  visits.forEach(visit => {
    const frequency = getVisitFrequencyText(visit.visitCount);
    if (groups[frequency]) {
      groups[frequency].push(visit);
    }
  });
  
  return groups;
};

/**
 * 按日期分组访问记录
 */
export const groupVisitsByDate = (
  visits: RecentVisitRecord[]
): Record<string, RecentVisitRecord[]> => {
  const groups: Record<string, RecentVisitRecord[]> = {};
  
  visits.forEach(visit => {
    const date = new Date(visit.timestamp);
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    
    let dateKey: string;
    
    if (date.toDateString() === today.toDateString()) {
      dateKey = '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      dateKey = '昨天';
    } else {
      dateKey = date.toLocaleDateString('zh-CN');
    }
    
    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }
    
    groups[dateKey].push(visit);
  });
  
  return groups;
};

/**
 * 获取访问统计
 */
export const getVisitStatistics = (visits: RecentVisitRecord[]) => {
  const totalVisits = visits.reduce((sum, visit) => sum + visit.visitCount, 0);
  const uniquePages = visits.length;
  const averageVisits = uniquePages > 0 ? Math.round(totalVisits / uniquePages) : 0;
  
  const mostVisited = visits.reduce((max, visit) => 
    visit.visitCount > max.visitCount ? visit : max, 
    visits[0] || { visitCount: 0 }
  );
  
  const recentVisit = visits.reduce((latest, visit) => 
    visit.timestamp > latest.timestamp ? visit : latest,
    visits[0] || { timestamp: 0 }
  );
  
  return {
    totalVisits,
    uniquePages,
    averageVisits,
    mostVisited,
    recentVisit,
  };
};
