/**
 * 布局状态切片
 * 迁移自 useLayout hook
 */

import type { StateCreator } from 'zustand';
import type { AppStore, LayoutState, LayoutActions, StoreSlice } from '../types';

// 默认布局状态
const DEFAULT_LAYOUT_STATE: LayoutState = {
  // 侧边栏状态
  sidebarCollapsed: false,
  sidebarWidth: 240,
  sidebarCollapsedWidth: 80,

  // 响应式相关
  isMobile: false,
  currentBreakpoint: 'lg',
  mobileDrawerOpen: false,
  
  // 布局配置
  headerHeight: 64,
  contentPadding: 24,
  fixedHeader: true,
  fixedSidebar: true,
};

/**
 * 创建布局状态切片
 */
export const createLayoutSlice: StoreSlice<LayoutState & LayoutActions, AppStore> = (set, get) => ({
  // ==================== 状态 ====================
  ...DEFAULT_LAYOUT_STATE,

  // ==================== 侧边栏操作 ====================
  
  /**
   * 切换侧边栏折叠状态
   */
  toggleSidebar: () => {
    set((state) => ({
      sidebarCollapsed: !state.sidebarCollapsed
    }));
  },

  /**
   * 设置侧边栏折叠状态
   */
  setSidebarCollapsed: (collapsed: boolean) => {
    set({ sidebarCollapsed: collapsed });
  },

  // ==================== 主题操作 ====================



  // ==================== 移动端操作 ====================

  /**
   * 切换移动端抽屉
   */
  toggleMobileDrawer: () => {
    set((state) => ({
      mobileDrawerOpen: !state.mobileDrawerOpen
    }));
  },

  /**
   * 设置移动端抽屉状态
   */
  setMobileDrawerOpen: (open: boolean) => {
    set({ mobileDrawerOpen: open });
  },

  // ==================== 响应式更新 ====================

  /**
   * 更新响应式状态
   */
  updateResponsive: (isMobile: boolean, breakpoint: string) => {
    const currentState = get();
    
    set({
      isMobile,
      currentBreakpoint: breakpoint,
    });

    // 移动端自动关闭抽屉
    if (isMobile && currentState.mobileDrawerOpen) {
      set({ mobileDrawerOpen: false });
    }

    // 桌面端自动关闭移动端抽屉
    if (!isMobile && currentState.mobileDrawerOpen) {
      set({ mobileDrawerOpen: false });
    }
  },

  // ==================== 布局配置 ====================

  /**
   * 更新布局配置
   */
  updateLayoutConfig: (config: Partial<LayoutState>) => {
    set((state) => ({
      ...state,
      ...config
    }));
  },
});

// ==================== 工具函数 ====================

/**
 * 获取计算后的侧边栏宽度
 */
export const getComputedSidebarWidth = (state: LayoutState): number => {
  if (state.isMobile) {
    return 0; // 移动端不显示侧边栏
  }
  return state.sidebarCollapsed ? state.sidebarCollapsedWidth : state.sidebarWidth;
};

/**
 * 获取计算后的内容区域样式
 */
export const getComputedContentStyle = (state: LayoutState) => {
  const sidebarWidth = getComputedSidebarWidth(state);
  
  return {
    marginLeft: state.isMobile ? 0 : sidebarWidth,
    paddingTop: state.fixedHeader ? state.headerHeight : 0,
    padding: state.contentPadding,
    minHeight: `calc(100vh - ${state.fixedHeader ? state.headerHeight : 0}px)`,
  };
};

/**
 * 获取计算后的侧边栏样式
 */
export const getComputedSidebarStyle = (state: LayoutState) => {
  return {
    width: getComputedSidebarWidth(state),
    position: state.fixedSidebar ? 'fixed' : 'relative',
    height: state.fixedSidebar ? '100vh' : 'auto',
    top: state.fixedSidebar && state.fixedHeader ? state.headerHeight : 0,
    zIndex: state.isMobile ? 1000 : 100,
  };
};

/**
 * 获取计算后的头部样式
 */
export const getComputedHeaderStyle = (state: LayoutState) => {
  const sidebarWidth = getComputedSidebarWidth(state);
  
  return {
    height: state.headerHeight,
    position: state.fixedHeader ? 'fixed' : 'relative',
    top: 0,
    left: state.isMobile ? 0 : sidebarWidth,
    right: 0,
    zIndex: 1001,
    width: state.isMobile ? '100%' : `calc(100% - ${sidebarWidth}px)`,
  };
};

/**
 * 检查是否应该显示移动端抽屉遮罩
 */
export const shouldShowMobileOverlay = (state: LayoutState): boolean => {
  return state.isMobile && state.mobileDrawerOpen;
};

/**
 * 获取断点配置
 */
export const getBreakpointConfig = () => ({
  xs: 480,
  sm: 768,
  md: 1024,
  lg: 1440,
  xl: 1920,
  xxl: 2560,
});

/**
 * 根据屏幕宽度计算断点
 */
export const calculateBreakpoint = (width: number): string => {
  const breakpoints = getBreakpointConfig();
  
  if (width < breakpoints.xs) return 'xs';
  if (width < breakpoints.sm) return 'sm';
  if (width < breakpoints.md) return 'md';
  if (width < breakpoints.lg) return 'lg';
  if (width < breakpoints.xl) return 'xl';
  return 'xxl';
};

/**
 * 检查是否为移动端
 */
export const checkIsMobile = (width: number): boolean => {
  return width < getBreakpointConfig().md;
};
