/**
 * 响应式状态切片
 * 迁移自 useResponsive hook
 */

import type { StateCreator } from 'zustand';
import type { AppStore, ResponsiveState, ResponsiveActions, StoreSlice } from '../types';
import { calculateBreakpoint, checkIsMobile, getBreakpointConfig } from './layoutSlice';

// 初始响应式状态
const initialResponsiveState: ResponsiveState = {
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  currentBreakpoint: 'lg',
  screenWidth: typeof window !== 'undefined' ? window.innerWidth : 1440,
  screenHeight: typeof window !== 'undefined' ? window.innerHeight : 900,
};

/**
 * 创建响应式状态切片
 */
export const createResponsiveSlice: StoreSlice<ResponsiveState & ResponsiveActions, AppStore> = (set, get) => ({
  // ==================== 状态 ====================
  ...initialResponsiveState,

  // ==================== 操作 ====================

  /**
   * 更新屏幕尺寸
   */
  updateScreenSize: (width: number, height: number) => {
    const breakpoint = calculateBreakpoint(width);
    const isMobile = checkIsMobile(width);
    const breakpoints = getBreakpointConfig();
    
    const isTablet = width >= breakpoints.sm && width < breakpoints.lg;
    const isDesktop = width >= breakpoints.lg;

    set({
      screenWidth: width,
      screenHeight: height,
      currentBreakpoint: breakpoint,
      isMobile,
      isTablet,
      isDesktop,
    });

    // 同步更新布局状态
    const { updateResponsive } = get();
    updateResponsive(isMobile, breakpoint);
  },

  /**
   * 更新断点
   */
  updateBreakpoint: (breakpoint: string) => {
    const breakpoints = getBreakpointConfig();
    let width = 1440; // 默认宽度

    // 根据断点计算对应的宽度
    switch (breakpoint) {
      case 'xs':
        width = breakpoints.xs - 1;
        break;
      case 'sm':
        width = breakpoints.sm;
        break;
      case 'md':
        width = breakpoints.md;
        break;
      case 'lg':
        width = breakpoints.lg;
        break;
      case 'xl':
        width = breakpoints.xl;
        break;
      case 'xxl':
        width = breakpoints.xxl;
        break;
    }

    // 调用 updateScreenSize 来统一处理
    get().updateScreenSize(width, get().screenHeight);
  },
});

// ==================== 工具函数 ====================

/**
 * 获取当前媒体查询状态
 */
export const getMediaQueryState = () => {
  if (typeof window === 'undefined') {
    return initialResponsiveState;
  }

  const width = window.innerWidth;
  const height = window.innerHeight;
  const breakpoint = calculateBreakpoint(width);
  const isMobile = checkIsMobile(width);
  const breakpoints = getBreakpointConfig();
  
  return {
    isMobile,
    isTablet: width >= breakpoints.sm && width < breakpoints.lg,
    isDesktop: width >= breakpoints.lg,
    currentBreakpoint: breakpoint,
    screenWidth: width,
    screenHeight: height,
  };
};

/**
 * 创建媒体查询监听器
 */
export const createMediaQueryListener = (callback: (state: ResponsiveState) => void) => {
  if (typeof window === 'undefined') {
    return () => {}; // 服务端渲染时返回空函数
  }

  const handleResize = () => {
    const state = getMediaQueryState();
    callback(state);
  };

  // 立即执行一次
  handleResize();

  // 添加监听器
  window.addEventListener('resize', handleResize);

  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize);
  };
};

/**
 * 检查是否匹配指定断点
 */
export const matchBreakpoint = (breakpoint: string, currentBreakpoint: string): boolean => {
  const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
  const currentIndex = breakpoints.indexOf(currentBreakpoint);
  const targetIndex = breakpoints.indexOf(breakpoint);
  
  return currentIndex >= targetIndex;
};

/**
 * 检查是否在指定断点范围内
 */
export const matchBreakpointRange = (
  min: string, 
  max: string, 
  currentBreakpoint: string
): boolean => {
  const breakpoints = ['xs', 'sm', 'md', 'lg', 'xl', 'xxl'];
  const currentIndex = breakpoints.indexOf(currentBreakpoint);
  const minIndex = breakpoints.indexOf(min);
  const maxIndex = breakpoints.indexOf(max);
  
  return currentIndex >= minIndex && currentIndex <= maxIndex;
};

/**
 * 获取响应式类名
 */
export const getResponsiveClassName = (
  baseClass: string, 
  breakpoint: string
): string => {
  return `${baseClass}-${breakpoint}`;
};

/**
 * 获取响应式样式
 */
export const getResponsiveStyle = (
  styles: Record<string, any>, 
  breakpoint: string
): any => {
  const breakpointStyles = styles[breakpoint] || {};
  const defaultStyles = styles.default || {};
  
  return {
    ...defaultStyles,
    ...breakpointStyles,
  };
};

/**
 * 创建响应式值选择器
 */
export const createResponsiveValue = <T>(
  values: Partial<Record<string, T>>, 
  currentBreakpoint: string,
  fallback: T
): T => {
  const breakpoints = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
  const currentIndex = breakpoints.indexOf(currentBreakpoint);
  
  // 从当前断点开始向下查找
  for (let i = currentIndex; i < breakpoints.length; i++) {
    const breakpoint = breakpoints[i];
    if (values[breakpoint] !== undefined) {
      return values[breakpoint]!;
    }
  }
  
  return fallback;
};
