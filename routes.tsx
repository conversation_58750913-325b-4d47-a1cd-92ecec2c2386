import React from 'react';
import { Routes, Route } from 'react-router-dom';
import BasicLayout from './layouts/BasicLayout';
import RouteRenderer from './components/RouteRenderer';
import routeConfig from './config/routes';

/**
 * V2 Admin 路由组件
 * 使用新的层次化路由配置
 */
const V2Routes: React.FC = () => {
  return (
    <Routes>
      {/* V2 管理界面路由 - 使用BasicLayout包装 */}
      <Route path="/" element={<BasicLayout />}>
        {/* 使用RouteRenderer渲染层次化路由 */}
        <Route path="*" element={<RouteRenderer routes={routeConfig} />} />
      </Route>
    </Routes>
  );
};

export default V2Routes;
