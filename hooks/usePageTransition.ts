/**
 * 页面过渡动画 Hook
 * 基于 Zustand 的全局页面动画状态管理
 */

import { useEffect, useMemo } from 'react';
import { usePageAnimationStore } from '../stores';
import type { AnimationConfig, AnimationType, EasingType } from '../stores/global/pageAnimationSlice';

// Hook 配置选项
export interface UsePageTransitionOptions {
  // 自定义动画配置
  type?: AnimationType;
  duration?: number;
  delay?: number;
  easing?: EasingType;
  distance?: number;
  scale?: number;
  
  // 预设名称（优先级低于自定义配置）
  preset?: string;
  
  // 是否禁用动画
  disabled?: boolean;
  
  // 是否启用调试模式
  debug?: boolean;
  
  // 自定义类名
  className?: string;
  
  // 是否自动检测减少动画偏好
  respectReducedMotion?: boolean;
}

// Hook 返回值
export interface PageTransitionResult {
  // Framer Motion 配置
  variants: Record<string, any>;
  transition: Record<string, any>;
  initial: string;
  animate: string;
  exit: string;
  
  // 样式配置
  style: React.CSSProperties;
  className: string;
  
  // 状态信息
  isAnimationEnabled: boolean;
  isReducedMotion: boolean;
  isPerformanceMode: boolean;
  currentConfig: AnimationConfig;
  
  // 控制方法
  enableAnimation: () => void;
  disableAnimation: () => void;
  toggleAnimation: () => void;
  applyPreset: (presetName: string) => void;
  updateConfig: (config: Partial<AnimationConfig>) => void;
}

/**
 * 页面过渡动画 Hook
 * 
 * @param options 配置选项
 * @returns 动画配置和控制方法
 * 
 * @example
 * ```tsx
 * // 基础使用
 * const pageTransition = usePageTransition();
 * 
 * return (
 *   <motion.div
 *     {...pageTransition}
 *   >
 *     页面内容
 *   </motion.div>
 * );
 * 
 * // 自定义配置
 * const pageTransition = usePageTransition({
 *   type: AnimationType.FADE_IN_LEFT,
 *   duration: 0.8,
 *   delay: 0.1
 * });
 * 
 * // 使用预设
 * const pageTransition = usePageTransition({
 *   preset: 'fast'
 * });
 * ```
 */
export const usePageTransition = (options: UsePageTransitionOptions = {}): PageTransitionResult => {
  const {
    type,
    duration,
    delay,
    easing,
    distance,
    scale,
    preset,
    disabled = false,
    debug = false,
    className = '',
    respectReducedMotion = true,
  } = options;

  // 获取全局动画状态
  const {
    globalConfig,
    enabled,
    reducedMotion,
    performanceMode,
    debugMode,
    getMotionConfig,
    setGlobalConfig,
    toggleAnimation,
    applyPreset: applyGlobalPreset,
    setReducedMotion,
  } = usePageAnimationStore();

  // 检测用户的减少动画偏好
  useEffect(() => {
    if (respectReducedMotion && typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      
      const handleChange = (e: MediaQueryListEvent) => {
        setReducedMotion(e.matches);
      };
      
      // 初始设置
      setReducedMotion(mediaQuery.matches);
      
      // 监听变化
      mediaQuery.addEventListener('change', handleChange);
      
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  }, [respectReducedMotion, setReducedMotion]);

  // 计算当前动画配置
  const currentConfig = useMemo((): AnimationConfig => {
    // 如果指定了预设，先应用预设
    let config = { ...globalConfig };
    
    // 应用自定义配置（优先级更高）
    if (type !== undefined) config.type = type;
    if (duration !== undefined) config.duration = duration;
    if (delay !== undefined) config.delay = delay;
    if (easing !== undefined) config.easing = easing;
    if (distance !== undefined) config.distance = distance;
    if (scale !== undefined) config.scale = scale;
    
    return config;
  }, [globalConfig, type, duration, delay, easing, distance, scale]);

  // 计算是否启用动画
  const isAnimationEnabled = useMemo(() => {
    return enabled && !disabled && (!respectReducedMotion || !reducedMotion);
  }, [enabled, disabled, respectReducedMotion, reducedMotion]);

  // 获取 Framer Motion 配置
  const motionConfig = useMemo(() => {
    return getMotionConfig(currentConfig);
  }, [getMotionConfig, currentConfig]);

  // 计算样式
  const style = useMemo((): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      willChange: isAnimationEnabled ? 'transform, opacity' : 'auto',
    };

    // 性能模式下的优化
    if (performanceMode) {
      baseStyle.contain = 'layout style paint';
    }

    // 调试模式下的样式
    if ((debug || debugMode) && isAnimationEnabled) {
      baseStyle.outline = '2px dashed rgba(255, 0, 0, 0.3)';
      baseStyle.outlineOffset = '2px';
    }

    return baseStyle;
  }, [isAnimationEnabled, performanceMode, debug, debugMode]);

  // 计算类名
  const computedClassName = useMemo(() => {
    const classes = ['page-transition'];
    
    if (className) {
      classes.push(className);
    }
    
    if (!isAnimationEnabled) {
      classes.push('animation-disabled');
    }
    
    if (reducedMotion) {
      classes.push('reduced-motion');
    }
    
    if (performanceMode) {
      classes.push('performance-mode');
    }
    
    if (debug || debugMode) {
      classes.push('debug-mode');
    }
    
    return classes.join(' ');
  }, [className, isAnimationEnabled, reducedMotion, performanceMode, debug, debugMode]);

  // 控制方法
  const enableAnimation = () => {
    if (!enabled) {
      toggleAnimation();
    }
  };

  const disableAnimation = () => {
    if (enabled) {
      toggleAnimation();
    }
  };

  const applyPreset = (presetName: string) => {
    applyGlobalPreset(presetName);
  };

  const updateConfig = (config: Partial<AnimationConfig>) => {
    setGlobalConfig(config);
  };

  // 应用预设（如果指定）
  useEffect(() => {
    if (preset) {
      applyGlobalPreset(preset);
    }
  }, [preset, applyGlobalPreset]);

  // 调试信息
  useEffect(() => {
    if ((debug || debugMode) && typeof console !== 'undefined') {
      console.group('🎬 Page Transition Debug');
      console.log('Current Config:', currentConfig);
      console.log('Animation Enabled:', isAnimationEnabled);
      console.log('Reduced Motion:', reducedMotion);
      console.log('Performance Mode:', performanceMode);
      console.log('Motion Config:', motionConfig);
      console.groupEnd();
    }
  }, [debug, debugMode, currentConfig, isAnimationEnabled, reducedMotion, performanceMode, motionConfig]);

  return {
    // Framer Motion 配置
    variants: motionConfig.variants,
    transition: motionConfig.transition,
    initial: 'initial',
    animate: 'animate',
    exit: 'exit',
    
    // 样式配置
    style,
    className: computedClassName,
    
    // 状态信息
    isAnimationEnabled,
    isReducedMotion: reducedMotion,
    isPerformanceMode: performanceMode,
    currentConfig,
    
    // 控制方法
    enableAnimation,
    disableAnimation,
    toggleAnimation,
    applyPreset,
    updateConfig,
  };
};

/**
 * 简化版页面过渡 Hook
 * 只返回基本的 Framer Motion 配置
 * 
 * @param options 配置选项
 * @returns 简化的动画配置
 */
export const useSimplePageTransition = (options: UsePageTransitionOptions = {}) => {
  const { variants, transition, initial, animate, exit, style, className } = usePageTransition(options);
  
  return {
    variants,
    transition,
    initial,
    animate,
    exit,
    style,
    className,
  };
};

/**
 * 页面过渡控制 Hook
 * 只返回控制方法，不包含动画配置
 * 
 * @returns 动画控制方法
 */
export const usePageTransitionControl = () => {
  const {
    enableAnimation,
    disableAnimation,
    toggleAnimation,
    applyPreset,
    updateConfig,
    isAnimationEnabled,
    isReducedMotion,
    isPerformanceMode,
  } = usePageTransition();
  
  return {
    enableAnimation,
    disableAnimation,
    toggleAnimation,
    applyPreset,
    updateConfig,
    isAnimationEnabled,
    isReducedMotion,
    isPerformanceMode,
  };
};

export default usePageTransition;
