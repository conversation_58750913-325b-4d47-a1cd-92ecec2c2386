/**
 * 响应式监听 Hook
 * 用于监听窗口大小变化并更新 Zustand store
 */

import { useEffect } from 'react';
import { useResponsiveStore } from '../stores';
import { createMediaQueryListener } from '../stores/global/responsiveSlice';

/**
 * 响应式监听 Hook
 * 自动监听窗口大小变化并更新状态
 */
export const useResponsiveListener = () => {
  const { updateScreenSize } = useResponsiveStore();

  useEffect(() => {
    // 创建媒体查询监听器
    const cleanup = createMediaQueryListener((state) => {
      updateScreenSize(state.screenWidth, state.screenHeight);
    });

    // 返回清理函数
    return cleanup;
  }, [updateScreenSize]);
};

/**
 * 获取当前响应式状态的 Hook
 */
export const useResponsive = () => {
  const responsiveState = useResponsiveStore();
  
  return {
    ...responsiveState,
    // 便捷的断点检查方法
    isXs: responsiveState.currentBreakpoint === 'xs',
    isSm: responsiveState.currentBreakpoint === 'sm',
    isMd: responsiveState.currentBreakpoint === 'md',
    isLg: responsiveState.currentBreakpoint === 'lg',
    isXl: responsiveState.currentBreakpoint === 'xl',
    isXxl: responsiveState.currentBreakpoint === 'xxl',
    
    // 范围检查方法
    isSmAndUp: ['sm', 'md', 'lg', 'xl', 'xxl'].includes(responsiveState.currentBreakpoint),
    isMdAndUp: ['md', 'lg', 'xl', 'xxl'].includes(responsiveState.currentBreakpoint),
    isLgAndUp: ['lg', 'xl', 'xxl'].includes(responsiveState.currentBreakpoint),
    isXlAndUp: ['xl', 'xxl'].includes(responsiveState.currentBreakpoint),
    
    isSmAndDown: ['xs', 'sm'].includes(responsiveState.currentBreakpoint),
    isMdAndDown: ['xs', 'sm', 'md'].includes(responsiveState.currentBreakpoint),
    isLgAndDown: ['xs', 'sm', 'md', 'lg'].includes(responsiveState.currentBreakpoint),
    isXlAndDown: ['xs', 'sm', 'md', 'lg', 'xl'].includes(responsiveState.currentBreakpoint),
  };
};

export default useResponsive;
