/**
 * 主题系统 - 基础CSS变量定义
 * 使用 data-theme 属性来切换主题
 */

/* 默认主题（亮色） */
:root,
[data-theme="light"] {
  /* 基础颜色 */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-tertiary: #fafafa;

  /* 文字颜色 */
  --theme-text-primary: #000000d9;
  --theme-text-secondary: #00000073;
  --theme-text-tertiary: #00000040;

  /* 边框颜色 */
  --theme-border-color: #d9d9d9;
  --theme-border-color-split: #f0f0f0;

  /* 阴影 */
  --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
  --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.08);

  /* 表格特定颜色 */
  --theme-table-header-bg: #ffffff;
  --theme-table-row-hover: #f5f5f5;
  --theme-table-row-stripe: #fafafa;
}

/* 暗色主题 */
[data-theme="dark"] {
  /* 基础颜色 */
  --theme-bg-primary: #1f1f1f;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #262626;

  /* 文字颜色 */
  --theme-text-primary: rgba(255, 255, 255, 0.85);
  --theme-text-secondary: rgba(255, 255, 255, 0.65);
  --theme-text-tertiary: rgba(255, 255, 255, 0.45);

  /* 边框颜色 */
  --theme-border-color: #434343;
  --theme-border-color-split: #303030;

  /* 阴影 */
  --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.45);
  --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.25);

  /* 表格特定颜色 */
  --theme-table-header-bg: #1f1f1f;
  --theme-table-row-hover: #262626;
  --theme-table-row-stripe: #252525;
}

/* 主题切换过渡动画 - 优化的过渡控制 */
.theme-transitioning {
  /* 只对容器元素应用硬件加速 */
  will-change: auto;
  backface-visibility: hidden;
}

/* 精确控制需要过渡的元素，避免通用选择器 */
.theme-transitioning .ant-layout,
.theme-transitioning .ant-card,
.theme-transitioning .ant-menu,
.theme-transitioning .ant-table,
.theme-transitioning .ant-input,
.theme-transitioning .ant-select,
.theme-transitioning .ant-button,
.theme-transitioning [data-theme-sensitive] {
  /* 优化的过渡时长和属性 */
  transition: background-color 0.4s ease,
              color 0.4s ease,
              border-color 0.4s ease !important;

  /* 只对需要的元素启用硬件加速 */
  will-change: background-color, color;
}

/* 移除重复的选择器，已在上面统一处理 */

/* 禁用在过渡期间的其他动画，避免冲突 */
.theme-transitioning * {
  animation-duration: 0s !important;
  animation-delay: 0s !important;
}

/* 兼容旧的过渡类名 */
.theme-transition,
.theme-transition *,
.theme-transition *:before,
.theme-transition *:after {
  transition: background-color 0.3s ease,
              border-color 0.3s ease,
              color 0.3s ease,
              box-shadow 0.3s ease !important;
}

/* 应用主题变量到全局元素 */
body {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

/* 为现有组件提供主题变量支持 */
.ant-layout {
  background-color: var(--theme-bg-secondary) !important;
}

.ant-layout-header {
  background-color: var(--theme-bg-primary) !important;
  border-bottom: 1px solid var(--theme-border-color-split) !important;
}

.ant-layout-sider {
  background-color: var(--theme-bg-primary) !important;
}

.ant-menu {
  background-color: transparent !important;
  color: var(--theme-text-primary) !important;
}

.ant-card {
  background-color: var(--theme-bg-primary) !important;
  border-color: var(--theme-border-color-split) !important;
}
