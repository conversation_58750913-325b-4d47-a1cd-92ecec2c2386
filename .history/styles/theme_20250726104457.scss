// 全局主题样式
:root {
  // 主题过渡动画
  --theme-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  // 浅色主题变量
  --light-bg-primary: #ffffff;
  --light-bg-secondary: #f5f5f5;
  --light-bg-tertiary: #fafafa;
  --light-text-primary: rgba(0, 0, 0, 0.88);
  --light-text-secondary: rgba(0, 0, 0, 0.65);
  --light-text-tertiary: rgba(0, 0, 0, 0.45);
  --light-border-color: #d9d9d9;
  --light-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  // 深色主题变量
  --dark-bg-primary: #141414;
  --dark-bg-secondary: #1f1f1f;
  --dark-bg-tertiary: #262626;
  --dark-text-primary: rgba(255, 255, 255, 0.88);
  --dark-text-secondary: rgba(255, 255, 255, 0.65);
  --dark-text-tertiary: rgba(255, 255, 255, 0.45);
  --dark-border-color: #424242;
  --dark-shadow: 0 2px 8px rgba(0, 0, 0, 0.45);
}

// 主题过渡类
.theme-transition {
  * {
    transition: var(--theme-transition) !important;
  }
}

// 应用主题包装器
.app-theme-wrapper {
  min-height: 100vh;
  transition: var(--theme-transition);
  
  &.theme-transitioning {
    * {
      transition: var(--theme-transition) !important;
    }
  }
}

// 浅色主题样式
[data-theme='light'] {
  color-scheme: light;
  
  // 背景色
  --bg-primary: var(--light-bg-primary);
  --bg-secondary: var(--light-bg-secondary);
  --bg-tertiary: var(--light-bg-tertiary);
  
  // 文字色
  --text-primary: var(--light-text-primary);
  --text-secondary: var(--light-text-secondary);
  --text-tertiary: var(--light-text-tertiary);
  
  // 边框色
  --border-color: var(--light-border-color);
  
  // 阴影
  --shadow: var(--light-shadow);
  
  // 滚动条样式
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    
    &:hover {
      background: #a8a8a8;
    }
  }
  
  // 选中文本样式
  ::selection {
    background-color: rgba(22, 119, 255, 0.2);
    color: inherit;
  }
}

// 深色主题样式
[data-theme='dark'] {
  color-scheme: dark;
  
  // 背景色
  --bg-primary: var(--dark-bg-primary);
  --bg-secondary: var(--dark-bg-secondary);
  --bg-tertiary: var(--dark-bg-tertiary);
  
  // 文字色
  --text-primary: var(--dark-text-primary);
  --text-secondary: var(--dark-text-secondary);
  --text-tertiary: var(--dark-text-tertiary);
  
  // 边框色
  --border-color: var(--dark-border-color);
  
  // 阴影
  --shadow: var(--dark-shadow);
  
  // 滚动条样式
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #2f2f2f;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #6e6e6e;
    border-radius: 4px;
    
    &:hover {
      background: #8e8e8e;
    }
  }
  
  // 选中文本样式
  ::selection {
    background-color: rgba(22, 119, 255, 0.4);
    color: inherit;
  }
}

// 全局样式重置
html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: var(--theme-transition);
}

body {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

// 代码块样式
code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  background-color: var(--bg-tertiary);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.875em;
}

pre {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  
  code {
    background: none;
    padding: 0;
  }
}

// 链接样式
a {
  color: #1677ff;
  text-decoration: none;
  transition: var(--theme-transition);
  
  &:hover {
    color: #4096ff;
  }
  
  &:active {
    color: #0958d9;
  }
}

// 表单元素样式增强
input, textarea, select {
  transition: var(--theme-transition);
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
}

// 表格样式增强
table {
  border-collapse: collapse;
  width: 100%;
}

th, td {
  border: 1px solid var(--border-color);
  padding: 8px 12px;
  text-align: left;
}

th {
  background-color: var(--bg-tertiary);
  font-weight: 600;
}

// 响应式设计
@media (max-width: 768px) {
  .app-theme-wrapper {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .app-theme-wrapper {
    font-size: 13px;
  }
}

// 打印样式
@media print {
  [data-theme='dark'] {
    color-scheme: light;
    --bg-primary: white;
    --bg-secondary: white;
    --bg-tertiary: white;
    --text-primary: black;
    --text-secondary: #666;
    --text-tertiary: #999;
    --border-color: #ccc;
  }
}
