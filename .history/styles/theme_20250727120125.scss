/**
 * 主题系统 - 基础CSS变量定义
 * 使用 data-theme 属性来切换主题
 */

/* 默认主题（亮色） */
:root,
[data-theme="light"] {
  /* 基础颜色 */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-tertiary: #fafafa;

  /* 文字颜色 */
  --theme-text-primary: #000000d9;
  --theme-text-secondary: #00000073;
  --theme-text-tertiary: #00000040;

  /* 边框颜色 */
  --theme-border-color: #d9d9d9;
  --theme-border-color-split: #f0f0f0;

  /* 阴影 */
  --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.15);
  --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.08);

  /* 表格特定颜色 */
  --theme-table-header-bg: #ffffff;
  --theme-table-row-hover: #f5f5f5;
  --theme-table-row-stripe: #fafafa;

  /* 布局特定颜色 */
  --theme-mask-bg: rgba(0, 0, 0, 0.45);
  --theme-loading-bg: rgba(255, 255, 255, 0.8);

  /* 滚动条颜色 */
  --theme-scrollbar-thumb: rgba(0, 0, 0, 0.15);
  --theme-scrollbar-thumb-hover: rgba(0, 0, 0, 0.25);

  /* 状态颜色 */
  --theme-error-color: #ff4d4f;
}

/* 暗色主题 */
[data-theme="dark"] {
  /* 基础颜色 */
  --theme-bg-primary: #1f1f1f;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #262626;

  /* 文字颜色 */
  --theme-text-primary: rgba(255, 255, 255, 0.85);
  --theme-text-secondary: rgba(255, 255, 255, 0.65);
  --theme-text-tertiary: rgba(255, 255, 255, 0.45);

  /* 边框颜色 */
  --theme-border-color: #434343;
  --theme-border-color-split: #303030;

  /* 阴影 */
  --theme-shadow-1: 0 2px 8px rgba(0, 0, 0, 0.45);
  --theme-shadow-2: 0 6px 16px rgba(0, 0, 0, 0.25);

  /* 表格特定颜色 */
  --theme-table-header-bg: #1f1f1f;
  --theme-table-row-hover: #262626;
  --theme-table-row-stripe: #252525;

  /* 布局特定颜色 */
  --theme-mask-bg: rgba(0, 0, 0, 0.65);
  --theme-loading-bg: rgba(0, 0, 0, 0.8);

  /* 滚动条颜色 */
  --theme-scrollbar-thumb: rgba(255, 255, 255, 0.15);
  --theme-scrollbar-thumb-hover: rgba(255, 255, 255, 0.25);

  /* 状态颜色 */
  --theme-error-color: #ff7875;
}

/* ===== 主题切换过渡动画已移除 ===== */
/* 现在使用直接的主题切换，无过渡效果 */

/* 应用主题变量到全局元素 */
body {
  background-color: var(--theme-bg-secondary);
  color: var(--theme-text-primary);
}

/* 为现有组件提供主题变量支持 */
.ant-layout {
  background-color: var(--theme-bg-secondary) !important;
}

.ant-layout-header {
  background-color: var(--theme-bg-primary) !important;
  border-bottom: 1px solid var(--theme-border-color-split) !important;
}

.ant-layout-sider {
  background-color: var(--theme-bg-primary) !important;
}

.ant-menu {
  background-color: transparent !important;
  color: var(--theme-text-primary) !important;
}

.ant-card {
  background-color: var(--theme-bg-primary) !important;
  border-color: var(--theme-border-color-split) !important;
}
