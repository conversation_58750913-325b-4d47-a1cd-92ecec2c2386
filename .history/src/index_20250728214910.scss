// V2 Admin 独立项目全局样式 - SCSS架构

// SCSS @use 规则必须在最前面
@use '../styles/variables' as *;

// 知识库管理样式已移动到各自的组件目录中

// ===== 全局重置 =====
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  line-height: 1.5715;
  color: $text-color;
  background-color: $background-color-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // 防止body级别的滚动，让布局组件控制滚动
  overflow: hidden;
}

#root {
  height: 100%;
  min-height: 100vh;
  overflow: hidden;
}

// ===== V2 Admin 应用样式 =====
.v2-admin-app {
  height: 100%;
  min-height: 100vh;
  background: $background-color-base;

  // 强制移除侧边栏相关的边框
  .v2-sidebar-wrapper,
  .v2-sidebar,
  .sidebar-menu-component,
  .ant-menu,
  .ant-menu-inline,
  .ant-menu-root {
    border: none !important;
    border-right: none !important;
    border-inline-end: none !important;
  }

  // 重新添加必要的边框
  .v2-header {
    border-bottom: 1px solid rgba(5, 5, 5, 0.06) !important;
  }

  .sidebar-logo {
    border-bottom: 1px solid rgba(5, 5, 5, 0.06) !important;
  }

  .sidebar-footer {
    border-top: 1px solid rgba(5, 5, 5, 0.06) !important;
  }

  // 侧边栏阴影效果
  .v2-sidebar-wrapper {
    box-shadow: 2px 0 8px 0 rgba(0, 0, 0, 0.04) !important;
  }
}

// ===== 滚动条样式 - 只应用于特定容器 =====
// 移除全局滚动条样式，让各个组件自己控制滚动条样式
// 这样可以避免在不需要滚动的地方显示滚动条

// ===== 工具类 =====
.no-scroll {
  overflow: hidden !important;
}

.scroll-container {
  overflow-x: hidden;
  overflow-y: auto;
  max-width: 100%;
}

// ===== 设置页面样式 =====
.setting-description {
  color: $text-color-secondary;
  font-size: 12px;
  line-height: 1.4;
  margin-top: 4px;
  display: block;
}

// ===== Ant Design Card 组件全局样式覆盖 =====
// 移除 Card 组件的默认悬停阴影，使用原生样式
.ant-card-hoverable:hover {
  box-shadow: none !important;
  border-color: transparent !important;
}

// ===== 响应式工具类 =====
// 项目特定的响应式工具类
.hidden-mobile {
  @media (max-width: $breakpoint-sm) {
    display: none !important;
  }
}

// ===== 加载动画 =====
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $background-color-base;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid $primary-color;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== 打印样式 =====
@media print {
  .v2-sidebar-wrapper,
  .v2-header {
    display: none !important;
  }

  .layout-content {
    margin-left: 0 !important;
  }
}

// ===== 侧边栏折叠状态强制居中 - 全局最高优先级 =====
.ant-menu-inline-collapsed {
  .ant-menu-item,
  .ant-menu-submenu-title {
    padding: 0 !important;
    text-align: center !important;
    justify-content: center !important;
    display: flex !important;
    align-items: center !important;
    width: 64px !important;

    .anticon {
      margin: 0 !important;
      margin-left: 0 !important;
      margin-right: 0 !important;
      margin-inline-end: 0 !important;
      margin-inline-start: 0 !important;
      position: relative;
      left: 0 !important;
      right: 0 !important;
    }

    .ant-menu-title-content {
      display: none !important;
    }

    // 强制移除所有子元素的边距
    > * {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    // 子菜单箭头隐藏
    .ant-menu-submenu-arrow {
      display: none !important;
    }
  }
}