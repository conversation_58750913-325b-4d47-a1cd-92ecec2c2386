import React, { useEffect } from "react";
import {
  Card,
  Button,
  Input,
  Select,
  Row,
  Col,
  Tag,
  Modal,
  message,
  Spin,
  Empty,
  Avatar,
  Dropdown,
  Tabs,
  Form,
  List,
  Typography,
  Divider,
  Badge,
} from "antd";
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  MoreOutlined,
  DatabaseOutlined,
  ReloadOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  HighlightOutlined,
  BookOutlined,
  SyncOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import KnowledgeBaseFormModal from "../../components/KnowledgeBaseFormModal";
import KnowledgeBaseDetailModal from "../../components/KnowledgeBaseDetailModal";
import DocumentManagementModal from "../../components/DocumentManagementModal";
// import DocumentEditModal from "../../components/DocumentEditModal";
// import ChunkDetailDrawer from "../../components/ChunkDetailDrawer";
// 组件样式已移动到各自的组件目录中
import { useKnowledgeBaseManagementStore } from "../../stores";
import type { KnowledgeBase, RAGTestResult } from "../../types/knowledgeBase";
import "./style.scss";

const { Search, TextArea } = Input;
const { Option } = Select;
const { Text, Paragraph } = Typography;

// 模拟知识库数据
const mockKnowledgeBases: KnowledgeBase[] = [
  {
    id: "kb-001",
    name: "技术文档库",
    description: "包含前端、后端、数据库等技术文档和最佳实践，为开发团队提供技术支持和参考资料。",
    type: "document",
    status: "ready",
    document_count: 156,
    vector_count: 2340,
    total_size: 52428800, // 50MB
    embedding_model: "text-embedding-ada-002",
    tags: ["技术", "开发", "文档"],
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-06-20T14:30:00Z",
    created_by: "张三",
    owner_id: "user-001",
  },
  {
    id: "kb-002",
    name: "产品手册库",
    description: "产品功能说明、用户指南、操作手册等产品相关文档，帮助用户更好地使用产品。",
    type: "document",
    status: "ready",
    document_count: 89,
    vector_count: 1567,
    total_size: 31457280, // 30MB
    embedding_model: "text-embedding-ada-002",
    tags: ["产品", "手册", "用户指南"],
    created_at: "2024-02-10T09:00:00Z",
    updated_at: "2024-06-18T16:20:00Z",
    created_by: "李四",
    owner_id: "user-002",
  },
  {
    id: "kb-003",
    name: "FAQ问答库",
    description: "常见问题解答集合，包含用户常问的问题和标准答案，提高客服效率。",
    type: "qa",
    status: "ready",
    document_count: 234,
    vector_count: 3456,
    total_size: 15728640, // 15MB
    embedding_model: "text-embedding-3-small",
    tags: ["FAQ", "问答", "客服"],
    created_at: "2024-03-05T14:00:00Z",
    updated_at: "2024-06-19T11:45:00Z",
    created_by: "王五",
    owner_id: "user-003",
  },
  {
    id: "kb-004",
    name: "法律法规库",
    description: "相关法律法规、政策文件、合规要求等法务资料，为业务决策提供法律支持。",
    type: "structured",
    status: "processing",
    document_count: 67,
    vector_count: 890,
    total_size: 41943040, // 40MB
    embedding_model: "text-embedding-3-large",
    tags: ["法律", "法规", "合规"],
    created_at: "2024-04-12T08:30:00Z",
    updated_at: "2024-06-21T09:15:00Z",
    created_by: "赵六",
    owner_id: "user-004",
  },
  {
    id: "kb-005",
    name: "培训资料库",
    description: "员工培训材料、课程内容、考试题库等培训相关资源，支持人力资源培训工作。",
    type: "document",
    status: "ready",
    document_count: 123,
    vector_count: 1890,
    total_size: 73400320, // 70MB
    embedding_model: "text-embedding-ada-002",
    tags: ["培训", "教育", "HR"],
    created_at: "2024-05-08T13:20:00Z",
    updated_at: "2024-06-20T10:30:00Z",
    created_by: "孙七",
    owner_id: "user-005",
  },
  {
    id: "kb-006",
    name: "市场分析库",
    description: "行业报告、市场调研、竞品分析等市场相关数据和分析报告。",
    type: "structured",
    status: "ready",
    document_count: 45,
    vector_count: 678,
    total_size: 26214400, // 25MB
    embedding_model: "text-embedding-3-small",
    tags: ["市场", "分析", "报告"],
    created_at: "2024-05-20T16:45:00Z",
    updated_at: "2024-06-19T14:20:00Z",
    created_by: "周八",
    owner_id: "user-006",
  },
  {
    id: "kb-007",
    name: "网页资源库",
    description: "收集的网页内容、在线资源、博客文章等互联网资料。",
    type: "web",
    status: "error",
    document_count: 78,
    vector_count: 1234,
    total_size: 20971520, // 20MB
    embedding_model: "text-embedding-ada-002",
    tags: ["网页", "资源", "博客"],
    created_at: "2024-06-01T11:00:00Z",
    updated_at: "2024-06-21T08:45:00Z",
    created_by: "吴九",
    owner_id: "user-007",
  },
  {
    id: "kb-008",
    name: "API文档库",
    description: "各种API接口文档、SDK说明、集成指南等开发者资源。",
    type: "document",
    status: "inactive",
    document_count: 92,
    vector_count: 1456,
    total_size: 18874368, // 18MB
    embedding_model: "text-embedding-3-large",
    tags: ["API", "SDK", "开发"],
    created_at: "2024-06-10T09:30:00Z",
    updated_at: "2024-06-20T15:10:00Z",
    created_by: "郑十",
    owner_id: "user-008",
  }
];

const KnowledgeBaseManagement: React.FC = () => {
  // 使用 Zustand store
  const {
    // State
    knowledgeBases,
    loading,
    searchKeyword,
    selectedType,
    selectedStatus,
    formModalVisible,
    detailModalVisible,
    documentModalVisible,
    editingKnowledgeBase,
    viewingKnowledgeBase,
    kbActiveTab,
    selectedKnowledgeBase,
    testQuestion,
    ragTestResult,
    ragTestResults,
    ragTestStats,
    ragTestLoading,
    kbPagination,
    // Actions
    loadKnowledgeBases,
    setSearchKeyword,
    setSelectedType,
    setSelectedStatus,
    setFormModalVisible,
    setDetailModalVisible,
    setDocumentModalVisible,
    setEditingKnowledgeBase,
    setViewingKnowledgeBase,
    setKbActiveTab,
    setSelectedKnowledgeBase,
    setTestQuestion,
    setRagTestResult,
    setRagTestResults,
    setRagTestStats,
    setRagTestLoading,
    deleteKnowledgeBase,
    performRAGTest,
    refreshKnowledgeBases,
  } = useKnowledgeBaseManagementStore();
  // RAG测试表单
  const [ragTestForm] = Form.useForm();

  // 初始化数据
  useEffect(() => {
    loadKnowledgeBases();
  }, [loadKnowledgeBases]);

  // 过滤数据
  const filteredKnowledgeBases = knowledgeBases;



  // 删除知识库
  const handleDeleteKnowledgeBase = async (id: string) => {
    try {
      await deleteKnowledgeBase(id);
      message.success("知识库删除成功");
    } catch (error) {
      console.error("删除知识库失败:", error);
      message.error("删除知识库失败");
    }
  };

  // 模拟RAG测试结果数据
  const generateMockRAGResults = (query: string, knowledgeBaseId: string): RAGTestResult[] => {
    const knowledgeBase = mockKnowledgeBases.find(kb => kb.id === knowledgeBaseId);
    if (!knowledgeBase) return [];

    const mockResults: RAGTestResult[] = [
      {
        id: "result-001",
        document_id: "doc-001",
        document_name: "技术架构设计指南.pdf",
        chunk_id: "chunk-001",
        content: `关于${query}的详细说明：在现代软件架构中，${query}是一个重要的概念。它涉及到系统的可扩展性、可维护性和性能优化。通过合理的设计模式和最佳实践，我们可以构建出高质量的软件系统。`,
        similarity_score: 0.95,
        metadata: {
          page: 15,
          section: "第三章 架构设计原则",
          title: "系统架构最佳实践",
          source: "内部技术文档"
        },
        highlighted_content: `关于<mark>${query}</mark>的详细说明：在现代软件架构中，<mark>${query}</mark>是一个重要的概念。`
      },
      {
        id: "result-002",
        document_id: "doc-002",
        document_name: "开发规范手册.docx",
        chunk_id: "chunk-002",
        content: `${query}的实现方案包括多个方面：首先需要考虑技术选型，然后是架构设计，最后是具体的实现细节。在实际开发过程中，我们需要遵循一定的规范和标准，确保代码质量和项目的可维护性。`,
        similarity_score: 0.87,
        metadata: {
          page: 23,
          section: "第五章 实现规范",
          title: "开发实践指南",
          source: "开发团队文档"
        },
        highlighted_content: `<mark>${query}</mark>的实现方案包括多个方面：首先需要考虑技术选型，然后是架构设计。`
      },
      {
        id: "result-003",
        document_id: "doc-003",
        document_name: "常见问题解答.md",
        chunk_id: "chunk-003",
        content: `Q: 如何处理${query}相关的问题？\nA: 处理${query}问题时，建议采用以下步骤：1. 分析问题的根本原因；2. 制定解决方案；3. 实施并验证效果；4. 总结经验教训。这种系统性的方法可以帮助我们更好地解决类似问题。`,
        similarity_score: 0.82,
        metadata: {
          section: "技术问答",
          title: "常见技术问题",
          source: "FAQ文档"
        },
        highlighted_content: `Q: 如何处理<mark>${query}</mark>相关的问题？\nA: 处理<mark>${query}</mark>问题时，建议采用以下步骤。`
      }
    ];

    return mockResults;
  };

  // RAG测试函数
  const handleRAGTest = async () => {
    await performRAGTest();
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      ready: {
        color: '#52c41a',
        bgColor: '#f6ffed',
        borderColor: '#b7eb8f',
        icon: <CheckCircleOutlined />,
        text: '就绪'
      },
      processing: {
        color: '#1890ff',
        bgColor: '#e6f7ff',
        borderColor: '#91d5ff',
        icon: <SyncOutlined spin />,
        text: '处理中'
      },
      error: {
        color: '#ff4d4f',
        bgColor: '#fff2f0',
        borderColor: '#ffccc7',
        icon: <ExclamationCircleOutlined />,
        text: '错误'
      },
      inactive: {
        color: '#8c8c8c',
        bgColor: '#f5f5f5',
        borderColor: '#d9d9d9',
        icon: <ClockCircleOutlined />,
        text: '未激活'
      }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || { 
      color: '#8c8c8c', 
      bgColor: '#f5f5f5', 
      borderColor: '#d9d9d9', 
      icon: <ClockCircleOutlined />, 
      text: status 
    };
    
    return (
      <Tag
        icon={config.icon}
        style={{
          color: config.color,
          backgroundColor: config.bgColor,
          borderColor: config.borderColor,
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          padding: '0 8px',
          borderRadius: '12px',
          fontSize: '12px',
          lineHeight: '20px',
          fontWeight: 500,
        }}
      >
        {config.text}
      </Tag>
    );
  };

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    const typeLabels = {
      document: '文档库',
      qa: '问答库',
      structured: '结构化库',
      web: '网页库',
    };
    return typeLabels[type as keyof typeof typeLabels] || '未知类型';
  };

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // 渲染知识库卡片 - 采用与助手管理页面相同的设计模式
  const renderKnowledgeBaseCard = (kb: KnowledgeBase) => {
    const moreMenuItems = [
      {
        key: 'view',
        label: '查看详情',
        icon: <EyeOutlined />,
      },
      {
        key: 'edit',
        label: '编辑',
        icon: <EditOutlined />,
      },
      {
        key: 'upload',
        label: '文档管理',
        icon: <UploadOutlined />,
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
      },
    ];

    // 格式化文件大小
    const formatFileSize = (bytes: number) => {
      if (bytes < 1024) return bytes + ' B';
      else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
      else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
      else return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
    };

    const handleMenuClick = ({ key }: { key: string }) => {
      switch (key) {
        case 'view':
          setViewingKnowledgeBase(kb);
          setDetailModalVisible(true);
          break;
        case 'edit':
          setEditingKnowledgeBase(kb);
          setFormModalVisible(true);
          break;
        case 'upload':
          setViewingKnowledgeBase(kb);
          setDocumentModalVisible(true);
          break;
        case 'delete':
          Modal.confirm({
            title: '确认删除',
            content: `确定要删除知识库"${kb.name}"吗？此操作不可恢复。`,
            okText: '确定',
            cancelText: '取消',
            onOk: () => handleDeleteKnowledgeBase(kb.id),
          });
          break;
      }
    };

    return (
      <div className="knowledge-base-card-wrapper" key={kb.id}>
        <Card className="knowledge-base-card-new" hoverable>
          {/* 卡片头部 */}
          <div className="card-header">
            <div className="header-left">
              <Avatar
                size={32}
                icon={<DatabaseOutlined />}
                className="kb-avatar"
              />

              <div className="header-info">
                <h4 className="kb-title">
                  {kb.name}
                </h4>
                <div className="kb-meta">
                  <Tag className="type-tag">
                    {getTypeLabel(kb.type)}
                  </Tag>
                  {kb.status === 'ready' && (
                    <span className="status-dot"></span>
                  )}
                </div>
              </div>
            </div>
            <Dropdown
              menu={{ items: moreMenuItems, onClick: handleMenuClick }}
              trigger={["click"]}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                className="more-action-btn"
              />
            </Dropdown>
          </div>

          {/* 卡片内容 */}
          <div className="card-body">
            <p className="kb-description">
              {kb.description}
            </p>

            {/* 标签区域 */}
            {kb.tags.length > 0 && (
              <div className="tags-area">
                {kb.tags.slice(0, 3).map((tag: string) => (
                  <Tag key={tag} className="feature-tag">
                    {tag}
                  </Tag>
                ))}
                {kb.tags.length > 3 && (
                  <span className="more-tags-indicator">
                    +{kb.tags.length - 3}
                  </span>
                )}
              </div>
            )}

            {/* 统计数据区域 */}
            <div className="stats-grid">
              <div className="stat-cell">
                <div className="stat-number">
                  {formatNumber(kb.document_count)}
                </div>
                <div className="stat-text">
                  文档数量
                </div>
              </div>
              <div className="stat-cell">
                <div className="stat-number">
                  {formatNumber(kb.vector_count)}
                </div>
                <div className="stat-text">
                  向量数量
                </div>
              </div>
              <div className="stat-cell">
                <div className="stat-number">
                  {formatFileSize(kb.total_size)}
                </div>
                <div className="stat-text">
                  总大小
                </div>
              </div>
            </div>

            {/* 嵌入模型信息 */}
            <div className="model-info">
              <HighlightOutlined />
              <span>
                {kb.embedding_model.split('-').pop()}
              </span>
            </div>
          </div>

          {/* 卡片底部 */}
          <div className="card-footer">
            <span className="create-time">
              {new Date(kb.created_at)
                .toLocaleDateString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                })
                .replace(/\//g, "/")}
            </span>
            {getStatusTag(kb.status)}
          </div>
        </Card>
      </div>
    );
  };

  // Tabs items 配置
  const tabItems = [
    {
      key: 'management',
      label: (
        <span>
          <DatabaseOutlined />
          知识库管理
        </span>
      ),
      children: (
        <>
          {/* 工具栏 */}
          <div className="toolbar-section">
            <Card className="toolbar-card">
              <div className="toolbar-content">
                <div className="search-filters">
                  <Search
                    placeholder="搜索知识库名称或描述"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    style={{ width: 300, marginRight: 16 }}
                    allowClear
                  />
                  <Select
                    placeholder="状态筛选"
                    value={selectedStatus}
                    onChange={setSelectedStatus}
                    style={{ width: 120, marginRight: 16 }}
                    allowClear
                  >
                    <Option value="ready">就绪</Option>
                    <Option value="processing">处理中</Option>
                    <Option value="error">错误</Option>
                  </Select>
                </div>
                <div className="action-buttons">
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => setFormModalVisible(true)}
                  >
                    新建知识库
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={refreshKnowledgeBases}
                    loading={loading}
                  >
                    刷新
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* 知识库网格 */}
          <div className="content-section">
            <Spin spinning={loading}>
              {filteredKnowledgeBases.length === 0 ? (
                <Empty
                  description="暂无知识库"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
              ) : (
                <div className="knowledge-base-grid">
                  {filteredKnowledgeBases.map(renderKnowledgeBaseCard)}
                </div>
              )}
            </Spin>
          </div>
        </>
      )
    },
    {
      key: 'rag-test',
      label: (
        <span>
          <ThunderboltOutlined />
          RAG测试
        </span>
      ),
      children: (
        <div className="rag-test-section">
          <Row gutter={[24, 24]}>
            <Col xs={24} lg={12}>
              <Card title="测试配置" className="test-config-card">
                <div className="test-form">
                  <div className="form-item">
                    <label>选择知识库：</label>
                    <Select
                      placeholder="请选择知识库"
                      value={selectedKnowledgeBase}
                      onChange={setSelectedKnowledgeBase}
                      style={{ width: "100%" }}
                    >
                      {knowledgeBases
                        .filter((kb) => kb.status === "ready")
                        .map((kb) => (
                          <Option key={kb.id} value={kb.id}>
                            {kb.name}
                          </Option>
                        ))}
                    </Select>
                  </div>
                  <div className="form-item">
                    <label>测试问题：</label>
                    <TextArea
                      placeholder="请输入要测试的问题"
                      value={testQuestion}
                      onChange={(e) => setTestQuestion(e.target.value)}
                      rows={4}
                    />
                  </div>
                  <div className="form-actions">
                    <Button
                      type="primary"
                      icon={<ThunderboltOutlined />}
                      onClick={handleRAGTest}
                      loading={ragTestLoading}
                      disabled={!selectedKnowledgeBase || !testQuestion.trim()}
                    >
                      开始测试
                    </Button>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="测试结果" className="test-result-card">
                {ragTestResult ? (
                  <div className="test-result">
                    <div className="result-item">
                      <Text strong>回答：</Text>
                      <Paragraph>{ragTestResult.answer}</Paragraph>
                    </div>
                    <div className="result-item">
                      <Text strong>相关文档：</Text>
                      <div className="related-docs">
                        {ragTestResult.relatedDocs.map((doc: any, index: number) => (
                          <div key={index} className="doc-item">
                            <Text code>{doc.filename}</Text>
                            <Text type="secondary">
                              {" "}
                              (相似度: {(doc.similarity * 100).toFixed(1)}%)
                            </Text>
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="result-item">
                      <Text strong>响应时间：</Text>
                      <Text>{ragTestResult.responseTime}ms</Text>
                    </div>
                  </div>
                ) : (
                  <Empty
                    description="暂无测试结果"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Card>
            </Col>
          </Row>
        </div>
      )
    }
  ];

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: 'fadeInUp' as any,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
    >
      <div className="knowledge-base-management">
        {/* 主要内容标签页 */}
        <div className="main-content-section">
        <Card className="main-content">
          <Tabs
            activeKey={kbActiveTab}
            onChange={setKbActiveTab}
            className="knowledge-base-tabs"
            items={tabItems}
          />
        </Card>
      </div>

      {/* 模态框 */}
      <KnowledgeBaseFormModal
        visible={formModalVisible}
        knowledgeBase={editingKnowledgeBase}
        onCancel={() => setFormModalVisible(false)}
        onSuccess={() => {
          setFormModalVisible(false);
          refreshKnowledgeBases();
        }}
      />

      <KnowledgeBaseDetailModal
        visible={detailModalVisible}
        knowledgeBase={viewingKnowledgeBase}
        onCancel={() => setDetailModalVisible(false)}
      />

      <DocumentManagementModal
        visible={documentModalVisible}
        knowledgeBase={viewingKnowledgeBase}
        onCancel={() => setDocumentModalVisible(false)}
        onSuccess={() => {
          setDocumentModalVisible(false);
          refreshKnowledgeBases();
        }}
      />
      </div>
    </motion.div>
  );
};

export default KnowledgeBaseManagement;