// 知识库管理页面样式 - 简化版本
.knowledge-base-management {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
  background: #f5f5f5 !important;
  min-height: 100%;

  .toolbar-section {
    margin-bottom: 16px;

    .toolbar-card {
      border: 1px solid #f0f0f0;
      border-radius: 8px;
      background: #fafafa;
      padding: 16px;

      .toolbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
      }
    }
  }

  .knowledge-base-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 16px;

    .knowledge-base-card-wrapper {
      width: 100%;

      .knowledge-base-card-new {
        border: 1px solid #f0f0f0;
        border-radius: 8px;
        background: #ffffff;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
      }
    }
  }
}
