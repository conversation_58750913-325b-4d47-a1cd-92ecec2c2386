@use '../../styles/variables' as *;

// 知识库管理页面样式 - 与项目整体设计风格保持一致
.knowledge-base-management {
  // 使用flex布局进行页面结构 - 与Dashboard保持一致
  display: flex;
  flex-direction: column;
  gap: $spacing-md; // 电脑端：16px 间距
  padding: 0;
  background: var(--theme-bg-secondary);
  min-height: 100%;
  transition: background-color 0.2s ease;

  // 页面sections共同样式
  .page-header-section,
  .toolbar-section,
  .content-section {
    width: 100%;
  }



  // 页面标题区域 - 增强视觉效果
  .page-header {
    margin-bottom: 0; // 使用gap控制间距
    
    h1 {
      color: var(--theme-text-primary);
      font-weight: $font-weight-semibold;
      font-size: $font-size-xxl;
      transition: color 0.2s ease;
      margin-bottom: $spacing-md;
      line-height: $line-height-tight;
      display: flex;
      align-items: center;
    }

    p {
      color: var(--theme-text-secondary);
      font-size: $font-size-base;
      margin: 0;
      transition: color 0.2s ease;
      line-height: $line-height-base;
    }
  }

  // 工具栏区域 - 统一Dashboard卡片风格
  .toolbar-section {
    margin-bottom: $spacing-md; // 电脑端：16px 间距
    
    .toolbar-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-lg; // 与Dashboard一致
      box-shadow: none; // 移除阴影
      background: var(--theme-bg-tertiary);
      transition: background-color 0.2s ease, border-color 0.2s ease;
      // 移除动画效果

      // 移除hover效果
    }

      .ant-card-body {
        padding: 16px;
      }

      .toolbar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: $spacing-md;

        .search-filters {
          display: flex;
          align-items: center;
          gap: $spacing-md;
          flex: 1;

          .ant-input-search {
            .ant-input {
              border-radius: $border-radius-sm;
            }
          }

          .ant-select {
            .ant-select-selector {
              border-radius: $border-radius-sm;
            }
          }
        }

        .toolbar-actions,
        .action-buttons {
          display: flex;
          align-items: center;
          gap: $spacing-md; // 按钮之间8px间距

          .ant-btn {
            border-radius: $button-border-radius;
            font-size: $button-font-size-base;
            font-weight: $font-weight-medium;
            // 移除按钮动画效果
          }
        }
      }
    }
  }

  // 内容区域 - 简化容器样式
  .content-section {
    .content-card {
      border: none;
      border-radius: 0;
      box-shadow: none;
      background: transparent;
      transition: none;
      overflow: hidden;
      
      &:hover {
        box-shadow: none;
      }

      .ant-card-body {
        padding: 0;
      }

      .empty-state {
        padding: 48px 0;
        text-align: center;
      }
    }
  }

  // 知识库卡片网格 - 使用 CSS Grid 布局实现响应式网格
  .knowledge-base-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); // 自动填充，最小320px
    gap: $spacing-md 12px; // 垂直间距：电脑端16px，水平间距12px
    justify-items: start; // 卡片在网格内左对齐
    align-items: start; // 卡片在网格内顶部对齐

    .knowledge-base-card-wrapper {
      width: 100%; // 占满网格单元格
      min-height: 200px; // 统一卡片最小高度
      display: flex;
      flex-direction: column;

      .knowledge-base-card-new {
        width: 100% !important;
        height: 100% !important;
        display: flex;
        flex-direction: column;
        border: 1px solid var(--theme-border-color-split);
        border-radius: $border-radius-lg;
        box-shadow: var(--theme-shadow-1);
        background: var(--theme-bg-primary);
        transition: all 0.3s ease;
        overflow: visible; // 允许内容显示

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--theme-shadow-2);
          border-color: $primary-color-light;
        }

        .ant-card-body {
          padding: $spacing-md !important; // 减小内边距
          display: flex;
          flex-direction: column;
          height: 100%;
          justify-content: flex-start; // 改为从上到下排列
          gap: $spacing-md; // 减小间距
          overflow: visible; // 允许内容显示
        }

        // 卡片头部
        .card-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: $spacing-md; // 减小头部间距

          .header-left {
            display: flex;
            align-items: flex-start;
            gap: $spacing-md;
            flex: 1;

            .kb-avatar {
              background: linear-gradient(135deg, $primary-color-light, rgba(24, 144, 255, 0.2));
              color: $primary-color;
              border: 2px solid rgba(24, 144, 255, 0.1);
              flex-shrink: 0;
            }

            .header-info {
              flex: 1;
              min-width: 0;

              .kb-title {
                font-size: $font-size-base; // 适中的标题大小
                font-weight: $font-weight-semibold;
                color: var(--theme-text-primary);
                margin-bottom: 4px;
                line-height: $line-height-tight;
                transition: color 0.2s ease;
                @include text-ellipsis;
              }

              .kb-meta {
                display: flex;
                align-items: center;
                gap: $spacing-md;

                .type-tag {
                  font-size: $font-size-xs;
                  padding: 2px 8px;
                  border-radius: $border-radius-sm;
                  background: var(--theme-bg-tertiary);
                  border: 1px solid var(--theme-border-color);
                  color: var(--theme-text-secondary);
                  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
                  font-weight: $font-weight-medium;
                }

                .status-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: $success-color;
                  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
                }
              }
            }
          }

          .more-action-btn {
            color: var(--theme-text-tertiary);
            border: none;
            box-shadow: none;
            padding: 4px;
            transition: color 0.2s ease, background-color 0.2s ease;

            &:hover {
              color: var(--theme-text-secondary);
              background: var(--theme-bg-tertiary);
            }
          }
        }

        // 卡片主体内容
        .card-body {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: $spacing-md;

          .kb-description {
            color: var(--theme-text-secondary);
            font-size: $font-size-xs; // 减小字体
            line-height: 1.4;
            transition: color 0.2s ease;
            margin-bottom: $spacing-md;
            display: -webkit-box;
            -webkit-line-clamp: 2; // 限制2行
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            min-height: 2.4em; // 减小描述区域高度
          }

          .tags-area {
            margin-bottom: $spacing-md;
            min-height: 20px; // 减小标签区域高度

            .feature-tag {
              font-size: 10px; // 更小的标签字体
              padding: 1px 4px; // 减小内边距
              margin-right: 3px;
              margin-bottom: 3px;
              border-radius: $border-radius-xs;
              background: rgba($primary-color, 0.1);
              border: 1px solid rgba($primary-color, 0.2);
              color: $primary-color;
            }
          }

          .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 4px; // 更小的间距
            padding: $spacing-md 0; // 减小上下内边距
            border-top: 1px solid var(--theme-border-color-split);
            border-bottom: 1px solid var(--theme-border-color-split);
            transition: border-color 0.2s ease;

            .stat-cell {
              text-align: center;

              .stat-number {
                font-size: $font-size-sm; // 减小数字字体
                font-weight: $font-weight-semibold;
                color: var(--theme-text-primary);
                transition: color 0.2s ease;
                line-height: 1.2;
              }

              .stat-text {
                font-size: 10px; // 更小的描述字体
                color: var(--theme-text-secondary);
                transition: color 0.2s ease;
                margin-top: 1px;
                line-height: 1.2;
              }
            }
          }

          .model-info {
            display: flex;
            align-items: center;
            gap: $spacing-md;
            font-size: $font-size-xs;
            color: var(--theme-text-secondary);
            transition: color 0.2s ease;

            .anticon {
              color: $primary-color;
            }
          }

          .progress-info {
            display: flex;
            flex-direction: column;
            gap: $spacing-md;

            .progress-text {
              font-size: $font-size-xs;
              color: var(--theme-text-secondary);
              transition: color 0.2s ease;
              text-align: center;
            }
          }
        }

        // 卡片底部
        .card-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-top: $spacing-md;
          border-top: 1px solid var(--theme-border-color-split);
          transition: border-color 0.2s ease;
          margin-top: auto;

          .create-time {
            font-size: $font-size-xs;
            color: var(--theme-text-tertiary);
            transition: color 0.2s ease;
          }

          .ant-tag {
            margin: 0;
          }
        }

        // 更多操作按钮
        .more-action-btn {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: $border-radius-sm;
          color: var(--theme-text-secondary);
          flex-shrink: 0;
          transition: background-color 0.2s ease, color 0.2s ease;

          &:hover {
            background: var(--theme-bg-tertiary);
            color: $primary-color;
          }

          .anticon {
            font-size: 14px;
          }
        }
      }
    }
  }

  // ===== 动画效果已移除，使用统一的 PageLoadingAnimation =====


// ===== 主题支持现在通过CSS变量自动处理 =====

  // 主要内容区域
  .main-content-section {
    .main-content {
      .ant-card-body {
        display: flex;
        flex-direction: column;
        // gap: 16px; // 使用统一的gap间距，类似助手页面
      }
    }
  }

  // 模态框内容优化 - 统一间距策略
  .ant-modal {
    .ant-modal-content {
      .ant-modal-body {
        padding: $spacing-md;

        // 表单区域统一间距
        .ant-form {
          .ant-form-item {
            margin-bottom: $spacing-md;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        // 内容区域统一间距
        .modal-content-section {
          margin-bottom: $spacing-md;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // RAG测试相关样式
  .rag-test-section {
    .rag-test-form-card {
      .ant-card-head {
        border-bottom: 1px solid var(--theme-border-color-split);

        .ant-card-head-title {
          font-weight: 600;
          color: var(--theme-text-primary);
        }
      }

      .ant-form-item-label > label {
        font-weight: 500;
        color: var(--theme-text-primary);
      }

      .ant-select-selector {
        border-radius: 8px;
        border: 1px solid var(--theme-border-color);

        &:hover {
          border-color: $primary-color;
        }
      }

      .ant-input {
        border-radius: 8px;
        border: 1px solid var(--theme-border-color);

        &:hover {
          border-color: $primary-color;
        }

        &:focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }

      .ant-btn-primary {
        background: $primary-color;
        border-color: $primary-color;
        border-radius: 8px;
        height: 48px;
        font-weight: 500;

        &:hover {
          background: $primary-color-hover;
          border-color: $primary-color-hover;
        }
      }
    }

    .rag-stats {
      margin-top: 24px;
      padding: 16px;
      background: var(--theme-bg-tertiary);
      border-radius: 8px;
      border: 1px solid var(--theme-border-color-split);

      .ant-divider {
        margin: 0 0 16px 0;

        .ant-divider-inner-text {
          font-weight: 500;
          color: var(--theme-text-primary);
        }
      }

      .ant-statistic {
        .ant-statistic-title {
          color: var(--theme-text-secondary);
          font-size: 12px;
          margin-bottom: 4px;
        }

        .ant-statistic-content {
          color: var(--theme-text-primary);
          font-weight: 600;
        }
      }
    }

    .rag-results-card {
      .ant-card-head {
        border-bottom: 1px solid var(--theme-border-color-split);

        .ant-card-head-title {
          font-weight: 600;
          color: var(--theme-text-primary);
        }
      }

      .ant-list {
        .rag-result-item {
          padding: 20px 0;
          border-bottom: 1px solid var(--theme-border-color-split);

          &:last-child {
            border-bottom: none;
          }

          .result-content {
            width: 100%;

            .result-header {
              margin-bottom: 12px;

              .result-title {
                display: flex;
                align-items: center;
                margin-bottom: 4px;

                .ant-typography {
                  font-size: 16px;
                  margin: 0;
                }

                .ant-tag {
                  border-radius: 12px;
                  font-size: 11px;
                  padding: 2px 8px;
                }
              }

              .result-meta {
                font-size: 12px;
                color: var(--theme-text-secondary);
              }
            }

            .result-body {
              margin-bottom: 12px;

              .result-text {
                margin: 0;
                line-height: 1.6;
                color: var(--theme-text-primary);

                mark {
                  background-color: rgba(255, 193, 7, 0.2);
                  color: var(--theme-text-primary);
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-weight: 500;
                }
              }
            }

            .result-footer {
              .ant-typography {
                margin: 0;
              }
            }
          }
        }
      }
    }
}
