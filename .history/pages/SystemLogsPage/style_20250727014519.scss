@use '../styles/variables' as *;

.system-logs-page {
  padding: 0;
  min-height: 100vh;
  background: var(--theme-bg-secondary);
  transition: background-color 0.2s ease;
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .page-header {
    margin-bottom: 16px;

    h2 {
      margin: 0;
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
      color: $text-color-primary;
    }
  }

  .search-section {
    margin-bottom: 16px;
    padding: $spacing-md;
    background: var(--color-bg-secondary);
    border-radius: $border-radius-base;
    border: 1px solid var(--theme-border-color);

    // 自定义按钮边框样式
    .ant-btn {
      border-color: var(--theme-border-color) !important;
      transition: border-color 0.2s ease;

      &:hover {
        border-color: var(--theme-border-color) !important;
      }

      // 保持选中状态的蓝色
      &.ant-btn-primary {
        border-color: $primary-color !important;

        &:hover {
          border-color: $primary-color-hover !important;
        }
      }
    }
  }

  // 工具栏区域
  .toolbar-section {
    margin-bottom: $spacing-md;
    display: flex;
    justify-content: flex-end;
  }

  // 表格区域
  .table-section {
    .system-logs-table {
      // TanStackTable 样式覆盖
      .tanstack-table {
        // 表头样式
        thead th {
          background: $background-color-light;
          border-bottom: 2px solid $border-color-split;
          font-weight: $font-weight-semibold;
          color: $text-color-primary;
          font-size: $font-size-sm;
          padding: $spacing-sm $spacing-md;
          text-align: center;
        }

        // 表格数据行
        tbody td {
          padding: $spacing-sm $spacing-md;
          border-bottom: 1px solid $border-color-split;
          font-size: $font-size-sm;
          vertical-align: middle;
          text-align: center;

          // 内容文本省略
          .content-text {
            display: block;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: help;
          }

          // 状态标签
          .ant-tag {
            margin: 0;
            border-radius: $border-radius-sm;
            font-size: $font-size-xs;
            line-height: 1.2;
            padding: 2px 6px;
          }
        }

        // 悬浮行效果
        tbody tr:hover td {
          background: rgba($primary-color, 0.03);
        }

        // 边框样式
        &.bordered {
          border: 1px solid $border-color-split;

          thead th,
          tbody td {
            border-right: 1px solid $border-color-split;
          }
        }
      }

      // TanStackTable 分页器样式
      .table-pagination {
        margin: 16px 0;
        text-align: right;

        .pagination-info {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .search-section {
      .ant-space {
        width: 100%;

        .ant-space-item {
          width: 100%;

          .ant-input,
          .ant-select,
          .ant-picker {
            width: 100% !important;
          }
        }
      }
    }

    .table-section .system-logs-table .tanstack-table {
      font-size: $font-size-xs;

      thead th,
      tbody td {
        padding: $spacing-xs;
      }
    }
  }
}

// ===== 主题支持现在通过CSS变量自动处理 =====