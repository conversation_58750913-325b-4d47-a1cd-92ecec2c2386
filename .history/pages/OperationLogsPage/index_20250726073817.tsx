import React, { useEffect, useCallback, useMemo } from "react";
import { Tag, Space, Button, Select, DatePicker } from "antd";
import { UserOutlined, SearchOutlined } from "@ant-design/icons";
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import TanStackTable from "../../components/TanStackTable";
import { fuzzyFilter } from '../../components/TanStackTable/utils';
import { useOperationLogsStore } from '../../stores';
import type { OperationLogItem } from '../../stores/pages/operationLogsSlice';
import FramerPageAnimation from "../../components/FramerPageAnimation";
import "./style.scss";

const { RangePicker } = DatePicker;

const OperationLogsPage: React.FC = () => {
  // 使用 Zustand store 状态管理
  const {
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData,
  } = useOperationLogsStore();

  // 创建列助手
  const columnHelper = createColumnHelper<OperationLogItem>();

  // TanStackTable 列定义
  const columns = useMemo<ColumnDef<OperationLogItem, any>[]>(() => [
    columnHelper.display({
      id: 'index',
      header: '序号',
      size: 48,
      cell: ({ row }) => (
        <span className="sequence-number">{row.index + 1}</span>
      ),
    }),
    columnHelper.accessor('user', {
      id: 'user',
      header: '操作用户',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <div className="user-cell">
          <UserOutlined style={{ marginRight: 4 }} />
          {getValue()}
        </div>
      ),
    }),
    columnHelper.accessor('type', {
      id: 'type',
      header: '操作类型',
      size: 100,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="type-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('content', {
      id: 'content',
      header: '操作内容',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="content-text" title={getValue()}>{getValue()}</span>
      ),
    }),
    columnHelper.accessor('method', {
      id: 'method',
      header: '请求方法',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const method = getValue();
        const colorMap: Record<string, string> = {
          'GET': 'blue',
          'POST': 'green',
          'PUT': 'orange',
          'DELETE': 'red',
        };
        return <Tag color={colorMap[method] || 'default'}>{method}</Tag>;
      },
    }),
    columnHelper.accessor('url', {
      id: 'url',
      header: '请求地址',
      size: 180,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="url-text" title={getValue()}>{getValue()}</span>
      ),
    }),
    columnHelper.accessor('ip', {
      id: 'ip',
      header: '操作IP',
      size: 140,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="ip-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('time', {
      id: 'time',
      header: '操作时间',
      size: 170,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="time-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('status', {
      id: 'status',
      header: '操作状态',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const status = getValue();
        return status === "失败" ?
          <Tag color="error">失败</Tag> :
          <Tag color="success">成功</Tag>;
      },
    }),
    columnHelper.accessor('duration', {
      id: 'duration',
      header: '耗时(ms)',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const duration = getValue();
        const color = duration > 1000 ? 'red' : duration > 500 ? 'orange' : 'green';
        return <Tag color={color}>{duration}ms</Tag>;
      },
    }),
  ], [columnHelper]);

  // 初始化数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // TanStackTable 事件处理
  const handlePageChange = useCallback((pageIndex: number, pageSize: number) => {
    setPagination({ pageIndex, pageSize });
  }, []);

  const handleRowClick = useCallback((row: OperationLogItem) => {
    console.log('点击日志行:', row);
  }, []);

  // 刷新处理
  const handleRefresh = useCallback(() => {
    console.log('刷新操作日志数据');
    loadData();
  }, [loadData]);

  // 导出处理
  const handleExport = useCallback(() => {
    console.log('导出操作日志数据');
    // 这里可以添加导出逻辑
  }, []);

  // 搜索处理
  const handleSearch = useCallback(() => {
    loadData();
  }, [loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    resetSearchParams();
  }, [resetSearchParams]);

  return (
    <div className="operation-logs-page">
      <FramerPageAnimation delay={0}>


          {/* 搜索区域 */}
          <div className="search-section">
            <Space direction="horizontal" size={16} wrap style={{ width: '100%', justifyContent: 'space-between' }}>
              {/* 左侧：搜索框和筛选器 */}
              <Space size={16} wrap>
                <Select
                  placeholder="操作类型"
                  value={searchParams.type || undefined}
                  onChange={(value) => setSearchParams({ ...searchParams, type: value || '' })}
                  style={{ width: 150, height: 32 }}
                  allowClear
                  options={[
                    { label: '新增', value: '新增' },
                    { label: '删除', value: '删除' },
                    { label: '修改', value: '修改' },
                    { label: '查询', value: '查询' },
                  ]}
                />
                <Select
                  placeholder="操作状态"
                  value={searchParams.status || undefined}
                  onChange={(value) => setSearchParams({ ...searchParams, status: value || '' })}
                  style={{ width: 150, height: 32 }}
                  allowClear
                  options={[
                    { label: '成功', value: '成功' },
                    { label: '失败', value: '失败' },
                  ]}
                />
                <RangePicker
                  placeholder={['开始时间', '结束时间']}
                  onChange={(dates, dateStrings) => {
                    setSearchParams({
                      ...searchParams,
                      startTime: dateStrings[0],
                      endTime: dateStrings[1],
                    });
                  }}
                  style={{ width: 240, height: 32 }}
                />
              </Space>

              {/* 右侧：操作按钮 */}
              <Space size={12}>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Space>
          </div>



          {/* 表格区域 */}
          <div className="table-section">
            <TanStackTable
              data={data}
              columns={columns}
              loading={loading}
              sorting={sorting}
              pagination={pagination}
              columnFilters={columnFilters}
              globalFilter={globalFilter}
              filterFns={{
                fuzzy: fuzzyFilter,
              }}
              config={{
                size: 'compact',
                emptyStateHeight: 280,
                minRowsForDynamic: 10,
                pageSizeOptions: [10, 20, 50],
                defaultPageSize: 10,
                minTableWidth: 1200,
              }}
              events={{
                onSortingChange: setSorting,
                onPageChange: handlePageChange,
                onColumnFiltersChange: setColumnFilters,
                onGlobalFilterChange: setGlobalFilter,
                onRowClick: handleRowClick,
              }}
              showPagination={true}
              className="operation-logs-table"
            />
          </div>
      </FramerPageAnimation>
    </div>
  );
};

export default OperationLogsPage;