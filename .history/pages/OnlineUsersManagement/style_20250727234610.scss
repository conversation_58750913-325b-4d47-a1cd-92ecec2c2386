@import '../../styles/variables.scss';

.online-users-management {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0;
  // background: var(--theme-bg-secondary);
  transition: background-color 0.2s ease;
  min-height: 100%;

  .toolbar-section,
  .table-section {
    width: 100%;
  }

  // 页面标题区域 - 增强视觉效果，包含副标题
  .page-header {
    margin-bottom: 0; // 使用gap控制间距
    
    h2 {
      color: var(--theme-text-primary);
      font-weight: $font-weight-semibold;
      font-size: $font-size-xxl;
      margin-bottom: $spacing-md;
      line-height: $line-height-tight;
      display: flex;
      align-items: center;
      transition: color 0.2s ease;

      .subtitle {
        font-size: $font-size-sm;
        color: var(--theme-text-secondary);
        font-weight: $font-weight-normal;
        margin-left: $spacing-md;
        transition: color 0.2s ease;
      }
    }

    .page-description {
      color: var(--theme-text-secondary);
      transition: color 0.2s ease;
      font-size: $font-size-base;
      margin: 0;
      line-height: $line-height-base;
    }
  }






  // 工具栏区域 - 统一Dashboard卡片风格
  .toolbar-section {
    .toolbar-card {
      border: 1px solid $border-color-light;
      border-radius: $border-radius-lg; // 与Dashboard一致
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08); // 与Dashboard一致
      background: $background-color-white;
      transition: all 0.3s ease;
      margin-bottom: 0; // 使用gap控制间距
      
      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
      }

      .ant-card-body {
        padding: $spacing-md 16px; // 增加内边距与Dashboard一致
      }

      .online-info {
        color: $text-color-secondary;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
      }
    }
  }

  // 表格卡片 - 增强Dashboard风格，特殊设计
  .table-section {
    .table-card {
      border: 1px solid $border-color-light;
      border-radius: $border-radius-lg; // 与Dashboard一致
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06), 0 1px 2px rgba(0, 0, 0, 0.08); // 与Dashboard一致
      background: $background-color-white;
      transition: all 0.3s ease;
      overflow: hidden;
      
      &:hover {
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
      }

      .ant-card-body {
        padding: 0;
      }

      // 表格标题 - 基于图片设计
      .table-title {
        padding: $spacing-md 16px;
        background: $background-color-light;
        border-bottom: 1px solid $border-color-split;
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: $text-color-primary;
      }

      // TanStackTable 在线用户表格样式
      .online-users-table {
        // 表格容器
        .table-section {
          width: 100%;
        }

        // TanStackTable 样式覆盖
        .tanstack-table {
          // 表头样式
          thead th {
            background: $background-color-light;
            border-bottom: 2px solid $border-color-split;
            font-weight: $font-weight-semibold;
            color: $text-color-primary;
            font-size: $font-size-sm;
            padding: $spacing-md $spacing-md;
            text-align: center;
          }

          // 表格数据行
          tbody td {
            padding: $spacing-md $spacing-md;
            border-bottom: 1px solid $border-color-split;
            font-size: $font-size-sm;
            vertical-align: middle;
            text-align: center;

            // 序号列样式
            .sequence-number {
              font-weight: $font-weight-medium;
              color: $text-color-secondary;
            }

            // 用户名单元格
            .username-cell {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              text-align: left;

              .username-text {
                font-weight: $font-weight-medium;
                color: $text-color-primary;
              }
            }

            // IP地址单元格
            .ip-cell {
              display: flex;
              align-items: center;
              justify-content: flex-start;
              text-align: left;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;

              .ip-text {
                font-size: $font-size-xs;
                color: $text-color-primary;
              }
            }

            // 登录地点文本
            .location-text {
              color: $text-color-secondary;
              font-size: $font-size-xs;
            }

            // 系统单元格
            .system-cell {
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: $font-size-xs;
            }

            // 浏览器文本
            .browser-text {
              color: $text-color-secondary;
              font-size: $font-size-xs;
            }

            // 时间文本
            .time-text {
              color: $text-color-secondary;
              font-size: $font-size-xs;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }

            // 状态标签
            .status-tag {
              font-size: $font-size-xs;
              font-weight: $font-weight-medium;
              border-radius: $border-radius-base;
              display: inline-flex;
              align-items: center;
            }
          }

          // 悬浮行效果
          tbody tr:hover td {
            background: rgba($primary-color, 0.03);
          }

          // 边框样式
          &.bordered {
            border: 1px solid $border-color-split;

            thead th,
            tbody td {
              border-right: 1px solid $border-color-split;
            }
          }
        }

        // TanStackTable 分页器样式
        .table-pagination {
          margin: 16px;
          text-align: right;

          .pagination-info {
            color: $text-color-secondary;
            font-size: $font-size-sm;
          }
        }
      }
    }
  }

  // ===== 动画效果已移除，使用统一的 FramerPageAnimation =====

  // ===== 响应式设计 =====
  @media (max-width: 768px) {
    .online-users-management {
      padding: $spacing-md;
      gap: $spacing-md;
    }

    .page-header h2 {
      flex-direction: column;
      align-items: flex-start;

      .subtitle {
        margin-left: 0;
        margin-top: $spacing-md;
      }
    }

    .toolbar-section .toolbar-card {
      .ant-row {
        flex-direction: column;
        gap: $spacing-md;
      }

      .ant-input {
        width: 100% !important;
      }
    }

    .table-section .table-card {
      .online-users-table .tanstack-table {
        font-size: $font-size-xs;

        thead th,
        tbody td {
          padding: $spacing-md;
        }
      }
    }
  }



// ===== 主题支持现在通过CSS变量自动处理 =====
}