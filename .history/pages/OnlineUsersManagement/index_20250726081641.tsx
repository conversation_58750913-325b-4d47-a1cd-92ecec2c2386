import React, { useEffect, useCallback, useMemo } from "react";
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import {
  Tag,
  Tooltip,
} from "antd";
import {
  ProFormText,
  ProFormSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import TanStackTable from '../../components/TanStackTable';
import { fuzzyFilter } from '../../components/TanStackTable/utils';
import { useOnlineUsersStore, useFilteredOnlineUsers, useOnlineUsersStats } from '../../stores';
import type { OnlineUser } from '../../stores/pages/onlineUsersSlice';
import FramerPageAnimation from "../../components/FramerPageAnimation";
import "../../components/FramerPageAnimation/style.scss"; // 导入表格固定高度样式
import {
  WifiOutlined,
  UserOutlined,
  GlobalOutlined,
  DesktopOutlined,
  MobileOutlined,
  TabletOutlined,
} from "@ant-design/icons";
import "./style.scss";




/**
 * 在线用户管理页面
 * 基于图片设计的特殊表格布局
 */
const OnlineUsersManagement: React.FC = () => {
  // 使用 Zustand store
  const {
    onlineUsers,
    loading,
    searchText,

    dataInitialized,
    autoRefreshEnabled,
    refreshInterval,
    // TanStackTable 状态
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    // 操作函数

    setFilters,
    resetFilters,
    refreshOnlineUsers,
    setDataInitialized,
    // TanStackTable 操作函数
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    resetTableFilters,
  } = useOnlineUsersStore();

  // 获取筛选后的用户列表和统计信息
  const filteredUsers = useFilteredOnlineUsers();
  const stats = useOnlineUsersStats();

  // TanStackTable 状态现在由 Zustand 管理

  // 创建列助手
  const columnHelper = createColumnHelper<OnlineUser>();

  // 同步搜索文本到 globalFilter
  useEffect(() => {
    setGlobalFilter(searchText);
  }, [searchText, setGlobalFilter]);

  // 筛选处理函数
  const handleFilterSubmit = (values: any) => {
    console.log('筛选提交:', values);

    // 使用批量更新筛选状态
    setFilters({
      searchText: values.username || '',
      statusFilter: values.status || 'all',
      osFilter: values.os || 'all',
      locationFilter: values.location || 'all',
    });
  };

  // 重置筛选
  const handleFilterReset = () => {
    console.log('筛选重置');
    resetFilters();
    resetTableFilters();
  };



  // 初始化数据
  useEffect(() => {
    if (!dataInitialized) {
      refreshOnlineUsers();
    }
  }, [dataInitialized, refreshOnlineUsers]);

  // 自动刷新逻辑
  useEffect(() => {
    if (!autoRefreshEnabled || !dataInitialized) return;

    const interval = setInterval(() => {
      refreshOnlineUsers();
    }, refreshInterval * 1000);

    return () => clearInterval(interval);
  }, [autoRefreshEnabled, refreshInterval, dataInitialized, refreshOnlineUsers]);



  // TanStackTable 事件处理
  const handlePageChange = useCallback((pageIndex: number, pageSize: number) => {
    setPagination({ pageIndex, pageSize });
  }, [setPagination]);

  const handleRowClick = useCallback((row: OnlineUser) => {
    console.log('点击用户行:', row);
  }, []);

  // 获取系统图标
  const getSystemIcon = (system: string) => {
    switch (system.toLowerCase()) {
      case 'macos':
      case 'ios':
        return <DesktopOutlined style={{ color: '#007AFF' }} />;
      case 'windows':
        return <DesktopOutlined style={{ color: '#0078D4' }} />;
      case 'android':
        return <MobileOutlined style={{ color: '#3DDC84' }} />;
      case 'linux':
        return <DesktopOutlined style={{ color: '#FCC624' }} />;
      default:
        return <TabletOutlined />;
    }
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
        return 'success';
      case 'idle':
        return 'warning';
      case 'away':
        return 'error';
      default:
        return 'default';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'online':
        return '在线';
      case 'idle':
        return '空闲';
      case 'away':
        return '离开';
      default:
        return '未知';
    }
  };

  // TanStackTable 列定义
  const columns = useMemo<ColumnDef<OnlineUser, any>[]>(() => [
    columnHelper.accessor('序号', {
      id: '序号',
      header: '序号',
      size: 80,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="sequence-number">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('用户名', {
      id: '用户名',
      header: '用户名',
      size: 140,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <div className="username-cell">
          <UserOutlined style={{ marginRight: 6, color: '#1890ff' }} />
          <span className="username-text">{getValue()}</span>
        </div>
      ),
    }),
    columnHelper.accessor('登录IP', {
      id: '登录IP',
      header: '登录IP',
      size: 150,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <div className="ip-cell">
          <GlobalOutlined style={{ marginRight: 6, color: '#52c41a' }} />
          <span className="ip-text">{getValue()}</span>
        </div>
      ),
    }),
    columnHelper.accessor('登录地点', {
      id: '登录地点',
      header: '登录地点',
      size: 180,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => {
        const text = getValue();
        return (
          <Tooltip placement="topLeft" title={text}>
            <span className="location-text">{text}</span>
          </Tooltip>
        );
      },
    }),
    columnHelper.accessor('操作系统', {
      id: '操作系统',
      header: '操作系统',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => {
        const text = getValue();
        return (
          <div className="system-cell">
            {getSystemIcon(text)}
            <span style={{ marginLeft: 6 }}>{text}</span>
          </div>
        );
      },
    }),
    columnHelper.accessor('浏览器类型', {
      id: '浏览器类型',
      header: '浏览器类型',
      size: 130,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="browser-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('登录时间', {
      id: '登录时间',
      header: '登录时间',
      size: 180,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="time-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('状态', {
      id: '状态',
      header: '状态',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const status = getValue();
        return (
          <Tag color={getStatusColor(status)} className="status-tag">
            <WifiOutlined style={{ marginRight: 4 }} />
            {getStatusText(status)}
          </Tag>
        );
      },
    }),
  ], [columnHelper]);

  return (
    <FramerPageAnimation delay={0}>
      <div className="online-users-management">
        {/* QueryFilter 筛选区域 */}
        <QueryFilter
          defaultCollapsed
          split
          onFinish={handleFilterSubmit}
          onReset={handleFilterReset}
        >
          <ProFormText
            name="username"
            label="用户名"
            placeholder="请输入用户名"
          />
          <ProFormSelect
            name="status"
            label="在线状态"
            placeholder="请选择状态"
            options={[
              { label: '在线', value: 'online' },
              { label: '空闲', value: 'idle' },
              { label: '离开', value: 'away' },
            ]}
          />
          <ProFormSelect
            name="os"
            label="操作系统"
            placeholder="请选择操作系统"
            options={[
              { label: 'Windows', value: 'Windows' },
              { label: 'macOS', value: 'macOS' },
              { label: 'Linux', value: 'Linux' },
              { label: 'Android', value: 'Android' },
              { label: 'iOS', value: 'iOS' },
            ]}
          />
          <ProFormText
            name="location"
            label="登录地点"
            placeholder="请输入登录地点"
          />
        </QueryFilter>

        {/* 表格区域 */}
        <section>
          <div style={{ marginBottom: 16, fontWeight: 500 }}>
            在线用户 (仅显示，操作系统不允许)
          </div>
          <div className="table-section">
            <TanStackTable
              data={onlineUsers}
              columns={columns}
              loading={loading}
              sorting={sorting}
              pagination={pagination}
              columnFilters={columnFilters}
              globalFilter={globalFilter}
              filterFns={{
                fuzzy: fuzzyFilter,
              }}
              config={{
                size: 'compact', // 使用 compact 预设
                emptyStateHeight: 280,
                minRowsForDynamic: 10,
                pageSizeOptions: [10, 20, 50],
                defaultPageSize: 10,
                minTableWidth: 1200,
              }}
              events={{
                onSortingChange: setSorting,
                onPageChange: handlePageChange,
                onColumnFiltersChange: setColumnFilters,
                onGlobalFilterChange: setGlobalFilter,
                onRowClick: handleRowClick,
              }}
              showPagination={true}
              className="online-users-table"
            />
          </div>
        </section>
      </div>
      </FramerPageAnimation>
    );
  };

export default OnlineUsersManagement;