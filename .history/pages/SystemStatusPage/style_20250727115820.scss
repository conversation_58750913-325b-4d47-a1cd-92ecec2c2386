@use '../../styles/variables' as *;

/* ===== 系统状态页面样式 ===== */

/* 页面容器 */
.system-status-page {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;
}

/* 系统卡片 */
.system-status-page .sys-card {
  border-radius: $border-radius-lg;
  margin-bottom: 0;
  border: 1px solid var(--theme-border-color-split);
  box-shadow: none !important;
}

.system-status-page .sys-card:hover {
  border-color: var(--theme-border-color);
  box-shadow: none !important;
}

.system-status-page .sys-card:focus,
.system-status-page .sys-card:focus-within {
  box-shadow: none !important;
}

/* 卡片头部 */
.system-status-page .sys-card .ant-card-head {
  min-height: 40px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  background: var(--theme-bg-tertiary);
  border-bottom: 1px solid var(--theme-border-color-split);
  border-radius: $border-radius-lg $border-radius-lg 0 0;
}

/* 卡片额外内容 */
.system-status-page .sys-card .ant-card-extra {
  color: var(--theme-text-secondary);
}

/* 卡片主体 */
.system-status-page .sys-card .ant-card-body {
  padding: 16px;
}

  // Remove shadows from all components within system status page
  .ant-card,
  .ant-table,
  .ant-btn,
  .ant-input,
  .ant-select,
  .ant-dropdown,
  .ant-tooltip,
  .ant-popover {
    box-shadow: none !important;

    &:hover,
    &:focus,
    &:active,
    &.ant-btn-primary:hover,
    &.ant-btn-primary:focus,
    &.ant-btn-primary:active {
      box-shadow: none !important;
    }
  }

  .sys-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: $spacing-md;
    font-size: $font-size-sm;
    tr {
      border-bottom: 1px solid $border-color-split;
    }
    .sys-table-label {
      width: 120px;
      color: $text-color-secondary;
      padding: 6px 12px;
      background: none;
      font-weight: $font-weight-medium;
      text-align: left;
    }
    .sys-table-value {
      color: $text-color-primary;
      padding: 6px 12px;
      text-align: left;
      background: none;
      font-weight: $font-weight-normal;
      &.highlight {
        color: #ff4d4f;
        font-weight: $font-weight-bold;
      }
    }
  }

  .sys-table-group-title {
    font-size: $font-size-sm;
    color: $text-color-secondary;
    font-weight: $font-weight-medium;
    margin: $spacing-md 0 $spacing-xs 0;
  }

  .sys-disk-table {
    margin-top: $spacing-md;
    .ant-table {
      font-size: $font-size-sm;
      .ant-table-thead > tr > th {
        background: $background-color-light;
        color: $text-color-primary;
        font-weight: $font-weight-medium;
        border-bottom: 1px solid $border-color-split;
        padding: 8px 12px;
      }
      .ant-table-tbody > tr > td {
        padding: 8px 12px;
        border-bottom: 1px solid $border-color-split;
        color: $text-color-primary;
      }
    }
  }

  // 动画效果
  .sys-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
  }
  .sys-card:nth-child(1) { animation-delay: 0s; }
  .sys-card:nth-child(2) { animation-delay: 0.1s; }
  .sys-card:nth-child(3) { animation-delay: 0.2s; }
  .sys-card:nth-child(4) { animation-delay: 0.3s; }
  .sys-card:nth-child(5) { animation-delay: 0.4s; }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // 响应式
  @media (max-width: 768px) {
    gap: $spacing-md;
    .ant-card-body {
      padding: $spacing-md $spacing-sm;
    }
    .sys-table-label, .sys-table-value {
      padding: 4px 6px;
    }
    .sys-disk-table .ant-table-thead > tr > th,
    .sys-disk-table .ant-table-tbody > tr > td {
      padding: 6px 6px;
    }
  }

  // 暗色主题
  .dark & {
    background: #141414;
    .sys-card {
      background: #1f1f1f;
      border-color: #303030;
      .ant-card-head {
        background: #262626;
        color: rgba(255,255,255,0.85);
        border-color: #303030;
      }
      .ant-card-body {
        color: rgba(255,255,255,0.85);
      }
    }
    .sys-table-label, .sys-table-value {
      color: rgba(255,255,255,0.65);
    }
    .sys-table-value.highlight {
      color: #ff7875;
    }
    .sys-disk-table .ant-table-thead > tr > th {
      background: #262626;
      color: rgba(255,255,255,0.85);
      border-color: #303030;
    }
    .sys-disk-table .ant-table-tbody > tr > td {
      color: rgba(255,255,255,0.85);
      border-color: #303030;
    }
  }
} 