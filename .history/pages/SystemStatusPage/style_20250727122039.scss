@use '../../styles/variables' as *;

/* ===== 系统状态页面样式 ===== */

/* 页面容器 */
.system-status-page {
  display: flex;
  flex-direction: column;
  padding: 0;
  min-height: 100%;
}

/* 系统卡片 */
.system-status-page .sys-card {
  border-radius: $border-radius-lg;
  margin-bottom: 0;
  border: 1px solid var(--theme-border-color-split);
  box-shadow: none !important;
}

.system-status-page .sys-card:hover {
  border-color: var(--theme-border-color);
  box-shadow: none !important;
}

.system-status-page .sys-card:focus,
.system-status-page .sys-card:focus-within {
  box-shadow: none !important;
}

/* 卡片头部 */
.system-status-page .sys-card .ant-card-head {
  min-height: 40px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  background: var(--theme-bg-tertiary);
  border-bottom: 1px solid var(--theme-border-color-split);
  border-radius: $border-radius-lg $border-radius-lg 0 0;
}

/* 卡片额外内容 */
.system-status-page .sys-card .ant-card-extra {
  color: var(--theme-text-secondary);
}

/* 卡片主体 */
.system-status-page .sys-card .ant-card-body {
  padding: 16px;
}

/* 移除所有组件阴影效果 */
.system-status-page .ant-card,
.system-status-page .ant-table,
.system-status-page .ant-btn,
.system-status-page .ant-input,
.system-status-page .ant-select,
.system-status-page .ant-dropdown,
.system-status-page .ant-tooltip,
.system-status-page .ant-popover {
  box-shadow: none !important;
}

.system-status-page .ant-card:hover,
.system-status-page .ant-table:hover,
.system-status-page .ant-btn:hover,
.system-status-page .ant-btn:focus,
.system-status-page .ant-btn:active,
.system-status-page .ant-btn-primary:hover,
.system-status-page .ant-btn-primary:focus,
.system-status-page .ant-btn-primary:active {
  box-shadow: none !important;
}

/* 系统表格 */
.system-status-page .sys-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: $spacing-md;
  font-size: $font-size-sm;
}

.system-status-page .sys-table tr {
  border-bottom: 1px solid var(--theme-border-color-split);
}

.system-status-page .sys-table-label {
  width: 120px;
  color: var(--theme-text-secondary);
  padding: 6px 12px;
  background: none;
  font-weight: $font-weight-medium;
  text-align: left;
}

.system-status-page .sys-table-value {
  color: var(--theme-text-primary);
  padding: 6px 12px;
  text-align: left;
  background: none;
  font-weight: $font-weight-normal;
}

.system-status-page .sys-table-value.highlight {
  color: var(--theme-error-color);
  font-weight: $font-weight-bold;
}

/* 表格分组标题 */
.system-status-page .sys-table-group-title {
  font-size: $font-size-sm;
  color: var(--theme-text-secondary);
  font-weight: $font-weight-medium;
  margin: $spacing-md 0 $spacing-xs 0;
}

/* 磁盘表格 */
.system-status-page .sys-disk-table {
  margin-top: $spacing-md;
}

.system-status-page .sys-disk-table .ant-table {
  font-size: $font-size-sm;
}

.system-status-page .sys-disk-table .ant-table-thead > tr > th {
  background: var(--theme-bg-tertiary);
  color: var(--theme-text-primary);
  font-weight: $font-weight-medium;
  border-bottom: 1px solid var(--theme-border-color-split);
  padding: 8px 12px;
}

.system-status-page .sys-disk-table .ant-table-tbody > tr > td {
  padding: 8px 12px;
  border-bottom: 1px solid var(--theme-border-color-split);
  color: var(--theme-text-primary);
}

/* 网格布局 */
.system-status-page .sys-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: $spacing-md;
  margin-bottom: $spacing-md;
}

/* ===== 响应式设计 ===== */

/* 移动端 */
@media (max-width: 768px) {
  .system-status-page .sys-grid {
    gap: $spacing-md;
  }

  .system-status-page .sys-card .ant-card-body {
    padding: $spacing-md;
  }

  .system-status-page .sys-table-label,
  .system-status-page .sys-table-value {
    padding: 4px 6px;
  }

  .system-status-page .sys-disk-table .ant-table-thead > tr > th,
  .system-status-page .sys-disk-table .ant-table-tbody > tr > td {
    padding: 6px;
  }
}

/* 平板端 */
@media (min-width: 769px) and (max-width: 1024px) {
  .system-status-page .sys-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: $spacing-md;
  }
}

/* 大屏幕 */
@media (min-width: 1200px) {
  .system-status-page .sys-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  }
}

/* ===== 暗色主题样式已移除，现在使用CSS变量自动处理 ===== */