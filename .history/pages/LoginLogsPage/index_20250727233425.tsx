import React, { useEffect, useCallback, useMemo } from "react";
import { Tag, Card, Space, Button, Input, Select, DatePicker } from "antd";
import { UserOutlined, ReloadOutlined, DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import TanStackTable from "../../components/TanStackTable";
import { fuzzyFilter } from '../../components/TanStackTable/utils';
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { useLoginLogsStore } from '../../stores';
import moment from 'moment';
import "./style.scss";

const { RangePicker } = DatePicker;

// 模拟数据
const mockData = Array.from({ length: 50 }).map((_, i) => ({
  key: i + 1,
  username: i % 3 === 0 ? "admin" : "user" + i,
  ip: `192.168.1.${i + 10}`,
  location: i % 2 === 0 ? "中国北京" : "中国上海",
  time: `2024-07-01 12:${(i % 60).toString().padStart(2, "0")}:00`,
  status: i % 5 === 0 ? "失败" : "成功",
  browser: i % 2 === 0 ? "Chrome" : "Firefox",
  os: i % 2 === 0 ? "Windows" : "macOS",
  remark: i % 5 === 0 ? "密码错误" : "-",
}));

// 定义登录日志数据类型
interface LoginLogItem {
  key: number;
  username: string;
  ip: string;
  location: string;
  time: string;
  status: string;
  browser: string;
  os: string;
  remark: string;
}

const LoginLogsPage: React.FC = () => {
  // 使用 Zustand store 状态管理
  const {
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData
  } = useLoginLogsStore();

  // 创建列助手
  const columnHelper = createColumnHelper<LoginLogItem>();

  // TanStackTable 列定义
  const columns = useMemo(() => [
    columnHelper.display({
      id: 'index',
      header: '序号',
      size: 48,
      cell: ({ row }) => (
        <span className="sequence-number">{row.index + 1}</span>
      ),
    }),
    columnHelper.accessor('username', {
      id: 'username',
      header: '用户名',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <div className="username-cell">
          <UserOutlined style={{ marginRight: 4 }} />
          {getValue<string>()}
        </div>
      ),
    }),
    columnHelper.accessor('ip', {
      id: 'ip',
      header: '登录IP',
      size: 140,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="ip-text">{getValue<string>()}</span>
      ),
    }),
    columnHelper.accessor('location', {
      id: 'location',
      header: '登录地点',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="location-text">{getValue<string>()}</span>
      ),
    }),
    columnHelper.accessor('time', {
      id: 'time',
      header: '登录时间',
      size: 170,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="time-text">{getValue<string>()}</span>
      ),
    }),
    columnHelper.accessor('status', {
      id: 'status',
      header: '登录状态',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const status = getValue<string>();
        return status === "失败" ?
          <Tag color="error">失败</Tag> :
          <Tag color="success">成功</Tag>;
      },
    }),
    columnHelper.accessor('browser', {
      id: 'browser',
      header: '浏览器',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="browser-text">{getValue<string>()}</span>
      ),
    }),
    columnHelper.accessor('os', {
      id: 'os',
      header: '操作系统',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="os-text">{getValue<string>()}</span>
      ),
    }),
    columnHelper.accessor('remark', {
      id: 'remark',
      header: '备注',
      size: 120,
      enableSorting: true,
      cell: ({ getValue }) => {
        const text = getValue<string>();
        return text === "-" ? null : <Tag color="red">{text}</Tag>;
      },
    }),
  ], [columnHelper]);

  // 初始化数据
  useEffect(() => {
    loadData(); // 初始加载数据
  }, []); // 只在组件挂载时加载一次

  // TanStackTable 事件处理
  const handlePageChange = useCallback((pageIndex: number, pageSize?: number) => {
    setPagination({
      pageIndex,
      pageSize: pageSize || pagination.pageSize,
    });
  }, [pagination.pageSize, setPagination]);

  const handleRowClick = useCallback((row: LoginLogItem) => {
    console.log('点击日志行:', row);
  }, []);

  // 刷新处理
  const handleRefresh = useCallback(() => {
    console.log('刷新登录日志数据');
    loadData();
  }, [loadData]);

  // 导出处理
  const handleExport = useCallback(() => {
    console.log('导出登录日志数据');
    // 这里可以添加导出逻辑
  }, []);

  // 搜索处理
  const handleSearch = useCallback(() => {
    loadData();
  }, [loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    resetSearchParams();
  }, [resetSearchParams]);

  return (
    <div className="login-logs-page">
      <FramerPageAnimation delay={0}>

          {/* 搜索区域 */}
          <div className="search-section">
            <Space direction="horizontal" size={16} wrap style={{ width: '100%', justifyContent: 'space-between' }}>
              {/* 左侧：搜索框和筛选器 */}
              <Space size={16} wrap>
                <Input
                  placeholder="用户名"
                  value={searchParams.username}
                  onChange={(e) => setSearchParams({ ...searchParams, username: e.target.value })}
                  style={{ width: 170, height: 32 }}
                  allowClear
                />
                <Input
                  placeholder="登录IP"
                  value={searchParams.ip}
                  onChange={(e) => setSearchParams({ ...searchParams, ip: e.target.value })}
                  style={{ width: 170, height: 32 }}
                  allowClear
                />
                <Select
                  placeholder="登录状态"
                  value={searchParams.status || undefined}
                  onChange={(value) => setSearchParams({ ...searchParams, status: value || '' })}
                  style={{ width: 150, height: 32 }}
                  allowClear
                  options={[
                    { label: '成功', value: '成功' },
                    { label: '失败', value: '失败' },
                  ]}
                />
                <RangePicker
                placeholder={['开始时间', '结束时间']}
                value={searchParams.startTime && searchParams.endTime ? [moment(searchParams.startTime), moment(searchParams.endTime)] as [moment.Moment, moment.Moment] : undefined}
                onChange={(dates, dateStrings) => {
                  setSearchParams({
                    ...searchParams,
                    startTime: dateStrings[0] || '',
                    endTime: dateStrings[1] || '',
                  });
                }}
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                style={{ width: 320, height: 32 }}
              />
              </Space>

              {/* 右侧：操作按钮 */}
              <Space size={12}>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Space>
          </div>



          {/* 表格区域 */}
          <div className="table-section">
            <TanStackTable
              data={data}
              columns={columns}
              loading={loading}
              sorting={sorting}
              pagination={{
              pageIndex: pagination.pageIndex,
              pageSize: pagination.pageSize,
            }}
              columnFilters={columnFilters}
              globalFilter={globalFilter}
              filterFns={{
                fuzzy: fuzzyFilter,
              }}
              config={{
                size: 'compact',
                emptyStateHeight: 280,
                minRowsForDynamic: 10,
                pageSizeOptions: [10, 20, 50],
                defaultPageSize: 10,
                minTableWidth: 1000,
              }}
              events={{
                onSortingChange: setSorting,
                onPageChange: handlePageChange,
                onColumnFiltersChange: setColumnFilters,
                onGlobalFilterChange: setGlobalFilter,
                onRowClick: handleRowClick,
              }}
              showPagination={true}
              className="login-logs-table"
            />
          </div>
      </FramerPageAnimation>
    </div>
  );
};

export default LoginLogsPage;