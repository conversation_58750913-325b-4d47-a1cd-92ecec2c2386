@use '../../styles/variables' as *;

// Dashboard 新版样式
.dashboard-v2 {
  // 使用flex布局进行页面结构
  display: flex;
  flex-direction: column;
  gap: $spacing-md; // 电脑端：16px 间距
  padding: 0;
  background: transparent;
  min-height: 100%;

  // 响应式间距策略
  @media (max-width: 1024px) {
    gap: $spacing-sm; // 平板端：8px 间距
  }

  @media (max-width: 768px) {
    gap: $spacing-xs; // 手机端：4px 间距
  }

  // 页面sections共同样式
  .stats-section,
  .charts-section,
  .bottom-section {
    width: 100%;
  }

  // ===== 顶部统计卡片区域 - 响应式Grid布局 =====
  .stats-section {
    width: 100%;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: $spacing-md; // 电脑端：16px 间距
      width: 100%;

      // 平板端优化
      @media (max-width: 1024px) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-sm; // 平板端：8px 间距
      }

      // 手机端优化
      @media (max-width: 768px) {
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-xs; // 手机端：4px 间距

        // 超小屏幕时单列显示
        @media (max-width: 480px) {
          grid-template-columns: 1fr;
          gap: $spacing-xs; // 保持手机端间距
        }
      }

      // 大屏幕优化
      @media (min-width: 1200px) {
        grid-template-columns: repeat(4, 1fr);
        gap: $spacing-md; // 保持电脑端间距
      }
    }
    // 统计卡片使用Ant Design Statistic组件，与其他管理页面保持统一
    .ant-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-lg;
      box-shadow: var(--theme-shadow-1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      background: var(--theme-bg-primary);

      // 统一固定高度控制 - 适应Dashboard的额外信息内容
      height: 140px; // 桌面端固定高度，为额外信息留出空间
      display: flex;
      flex-direction: column;

      // 移动端高度优化
      @media (max-width: 768px) {
        height: 130px; // 手机端2列显示
      }

      @media (max-width: 480px) {
        height: 120px; // 超小屏幕
      }

      &:hover {
        box-shadow: var(--theme-shadow-2);
        transform: translateY(-2px);
        border-color: $primary-color-light;
      }

      .ant-card-body {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding: $spacing-md; // 电脑端：16px 内边距

        // 平板端优化
        @media (max-width: 1024px) {
          padding: $spacing-sm; // 平板端：8px 内边距
        }

        // 手机端优化
        @media (max-width: 768px) {
          padding: $spacing-xs; // 手机端：4px 内边距
        }
      }

      // Statistic组件样式优化
      .ant-statistic {
        .ant-statistic-title {
          font-size: 14px;
          color: var(--theme-text-secondary);
          margin-bottom: 4px;
          transition: color 0.2s ease;
        }

        .ant-statistic-content {
          .ant-statistic-content-value {
            font-size: 24px;
            font-weight: $font-weight-bold;
            color: var(--theme-text-primary);
            transition: color 0.2s ease;

            @media (max-width: 768px) {
              font-size: 20px;
            }
          }
        }
      }

      // 额外信息样式 - 优化布局防止溢出
      .stat-extra-info {
        margin-top: 6px;
        flex-shrink: 0; // 防止被压缩
        overflow: hidden; // 防止溢出

        .stat-progress {
          width: 100%;
          margin-top: 2px;

          .ant-progress {
            margin-bottom: 0; // 移除默认底部边距
          }
        }

        .trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px; // 稍微减小字体
          font-weight: $font-weight-medium;
          margin-top: 2px;
          white-space: nowrap; // 防止换行
          overflow: hidden;
          text-overflow: ellipsis;

          .anticon {
            font-size: 11px;
            flex-shrink: 0; // 防止图标被压缩
          }
        }

        .sub-value {
          font-size: 11px; // 稍微减小字体
          margin-top: 1px;
          white-space: nowrap; // 防止换行
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }

  // ===== 中间图表区域 - 响应式Grid布局 =====
  .charts-section {
    .charts-grid {
      display: grid;
      grid-template-columns: 2fr 1fr;
      gap: $spacing-md; // 电脑端：16px 间距
      width: 100%;

      // 平板端布局
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: $spacing-sm; // 平板端：8px 间距
      }

      // 手机端布局
      @media (max-width: 768px) {
        gap: $spacing-xs; // 手机端：4px 间距
      }
    }

    .main-chart-card,
    .ranking-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-lg;
      box-shadow: var(--theme-shadow-1);
      background: var(--theme-bg-primary);
      transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

      // 动态高度，确保内容完整显示
      min-height: 400px;
      height: auto;

      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: var(--theme-shadow-2);
        transform: translateY(-1px);
      }

      .ant-card-body {
        padding: $spacing-md; // 电脑端：16px 内边距
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;

        // 平板端优化
        @media (max-width: 1024px) {
          padding: $spacing-sm; // 平板端：8px 内边距
        }

        // 手机端优化
        @media (max-width: 768px) {
          padding: $spacing-xs; // 手机端：4px 内边距
        }
      }
    }

    .main-chart-card {
      .chart-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0; // 移除底部边距，让 Divider 控制间距
        flex-wrap: wrap;
        gap: $spacing-md;

        .chart-title {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          font-size: $font-size-lg;
          font-weight: 600;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }

        .chart-controls {
          display: flex;
          align-items: center;
          gap: $spacing-md; // 电脑端：16px 间距
          flex-wrap: wrap;

          .chart-tabs {
            display: flex;
            gap: $spacing-sm;
          }

          .time-filters {
            display: flex;
            gap: $spacing-xs;
            flex-wrap: wrap;

            .ant-tag-checkable {
              border-radius: $border-radius-base;
              font-size: $font-size-sm;
              cursor: pointer;
              transition: $transition-base;
              padding: 4px 12px;

              &:not(.ant-tag-checkable-checked) {
                background: var(--theme-bg-secondary);
                border-color: var(--theme-border-color);
                color: var(--theme-text-secondary);

                &:hover {
                  background: $primary-color-light;
                  border-color: $primary-color;
                  color: $primary-color;
                }
              }
            }
          }
        }
      }

      .chart-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        
        .chart-placeholder {
          flex: 1;
          min-height: 300px;
          border-radius: $border-radius-base;
          background: var(--theme-bg-tertiary);
          display: flex;
          align-items: center;
          justify-content: center;
          border: 1px solid var(--theme-border-color-split);
          transition: background-color 0.2s ease, border-color 0.2s ease;

          .chart-mock {
            width: 100%;
            height: 100%;
            padding: $spacing-md; // 电脑端：16px 内边距
            display: flex;
            align-items: flex-end;
            justify-content: center;

            .chart-bars {
              display: flex;
              align-items: flex-end;
              gap: 12px;
              height: 85%;
              width: 90%;

              .bar {
                flex: 1;
                background: linear-gradient(to top, $primary-color, $primary-color-hover);
                border-radius: 4px 4px 0 0;
                min-height: 20px;
                transition: all 0.3s ease;

                &:nth-child(odd) {
                  background: linear-gradient(to top, $success-color, #73d13d);
                }

                &:hover {
                  opacity: 0.8;
                  transform: scale(1.05);
                }
              }
            }
          }
        }
      }
    }

    .ranking-card {
      .card-header {
        margin-bottom: 0;

        .card-title {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          font-size: $font-size-lg;
          font-weight: 600;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }
      }

      .ranking-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start; // 改为顶部对齐，更自然
        gap: $spacing-xs; // 添加项目间距
        overflow: hidden; // 防止内容溢出
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        
        .ranking-item {
          display: flex;
          align-items: center;
          gap: $spacing-sm; // 减少间距
          padding: $spacing-sm $spacing-md; // 减少上下内边距
          border-radius: $border-radius-base; // 添加圆角
          transition: $transition-base;
          min-height: 48px; // 减少高度以适应容器

          &:hover {
            background: var(--theme-bg-secondary);
            border: 1px solid rgba(24, 144, 255, 0.1);
            transform: translateX(2px);
          }

          .rank-badge {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: $font-size-sm;
            font-weight: 600;
            color: white;
            flex-shrink: 0; // 防止压缩
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1); // 调低阴影 30%

            &.rank-1 {
              background: linear-gradient(135deg, #ffd700, #ffed4e);
              color: #d4af37;
            }

            &.rank-2 {
              background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
              color: #999;
            }

            &.rank-3 {
              background: linear-gradient(135deg, #cd7f32, #daa520);
              color: #8b4513;
            }

            &.rank-other {
              background: linear-gradient(135deg, $text-color-tertiary, #bfbfbf);
              color: white;
            }
          }

          .store-info {
            flex: 1;
            overflow: hidden; // 防止文字溢出
            min-width: 0; // 允许 flex 项目缩小

            .store-name {
              font-size: $font-size-sm;
              color: var(--theme-text-primary);
              font-weight: $font-weight-medium;
              margin-bottom: 4px;
              transition: color 0.2s ease;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis; // 超长文字显示省略号
            }

            .store-sales {
              font-size: $font-size-xs;
              color: var(--theme-text-secondary);
              font-weight: $font-weight-medium;
              white-space: nowrap;
              transition: color 0.2s ease;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  // ===== 底部数据区域 - 响应式Grid布局 =====
  .bottom-section {
    .bottom-grid {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: $spacing-md; // 电脑端：16px 间距
      width: 100%;

      // 平板端布局
      @media (max-width: 1024px) {
        grid-template-columns: 1fr;
        gap: $spacing-sm; // 平板端：8px 间距
      }

      // 手机端布局
      @media (max-width: 768px) {
        gap: $spacing-xs; // 手机端：4px 间距
      }
    }

    .online-stores-card,
    .category-card {
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-lg;
      box-shadow: var(--theme-shadow-1);
      background: var(--theme-bg-primary);
      display: flex;
      flex-direction: column;
      transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

      // 动态高度，确保内容完整显示
      min-height: 400px;
      height: auto;

      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);
      }

      .ant-card-body {
        padding: $spacing-md; // 电脑端：16px 内边距
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
        box-sizing: border-box;

        // 平板端优化
        @media (max-width: 1024px) {
          padding: $spacing-sm; // 平板端：8px 内边距
        }

        // 手机端优化
        @media (max-width: 768px) {
          padding: $spacing-xs; // 手机端：4px 内边距
        }
      }

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 0;
        flex-wrap: wrap;
        gap: $spacing-md;
        flex-shrink: 0; // 防止头部被压缩
        min-height: 40px; // 设置最小高度

        .card-title {
          display: flex;
          align-items: center;
          gap: $spacing-sm;
          font-size: $font-size-lg;
          font-weight: 600;
          color: $text-color-primary;

          .anticon {
            color: $primary-color;
            font-size: $font-size-xl;
          }
        }

        .card-actions,
        .channel-tabs {
          display: flex;
          gap: $spacing-sm;
          flex-wrap: wrap;
        }
      }
    }

    .online-stores-card {
      .stores-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden; // 防止内容溢出
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        
        .stores-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: $spacing-md; // 电脑端：16px 间距
          flex: 1;
          align-content: start;
          overflow: auto; // 允许滚动
          padding: $spacing-xs; // 添加一些内边距

          // 平板端优化
          @media (max-width: 1024px) {
            gap: $spacing-sm; // 平板端：8px 间距
          }

          // 手机端优化
          @media (max-width: 768px) {
            gap: $spacing-xs; // 手机端：4px 间距
          }

          .store-card {
            display: flex;
            align-items: center;
            gap: $spacing-sm; // 减少间距
            padding: $spacing-md; // 减少内边距
            border: 1px solid $border-color-light;
            border-radius: $border-radius-lg;
            background: linear-gradient(135deg, #fafbfc 0%, #f6f9fc 100%);
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04); // 轻微阴影
            min-height: 70px; // 设置固定最小高度
            position: relative; // 为 z-index 做准备

            &:hover {
              border-color: $primary-color;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.08); // 进一步减轻阴影
              transform: translateY(-1px); // 减少位移避免遮挡
              z-index: 2; // 确保悬浮时在上层
            }

            .store-avatar {
              min-width: 40px; // 使用min-width确保响应式
              width: 40px;
              height: 40px;
              border-radius: $border-radius-lg;
              background: linear-gradient(135deg, $primary-color-light, rgba(24, 144, 255, 0.2));
              display: flex;
              align-items: center;
              justify-content: center;
              color: $primary-color;
              font-size: 18px; // 减少字体大小
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.06); // 调低阴影 30%
              flex-shrink: 0; // 防止压缩
            }

            .store-details {
              flex: 1;
              overflow: hidden; // 防止文字溢出
              min-width: 0; // 允许 flex 项目缩小

              .store-name {
                font-size: $font-size-base;
                color: $text-color-primary;
                font-weight: $font-weight-medium;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis; // 超长文字显示省略号
              }
            }
          }
        }
      }
    }

    .category-card {
      .category-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: visible; // 改为可见，确保图例显示
        max-height: calc(100% - 60px); // 确保不超出父容器，预留头部空间
        gap: $spacing-md; // 电脑端：16px 间距

        // 平板端优化
        @media (max-width: 1024px) {
          gap: $spacing-sm; // 平板端：8px 间距
        }

        // 手机端优化
        @media (max-width: 768px) {
          gap: $spacing-xs; // 手机端：4px 间距
        }
        
        .pie-chart-placeholder {
          height: 180px; // 减少高度给图例留更多空间
          border-radius: $border-radius-lg;
          background: linear-gradient(135deg, #f6f9fc 0%, #fafbfc 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;
          border: 1px solid rgba(0, 0, 0, 0.04);
          flex-shrink: 0; // 防止压缩
          z-index: 1; // 确保饼图在下层

          .pie-mock {
            width: 140px; // 适当减少尺寸
            height: 140px; // 适当减少尺寸
            border-radius: 50%;
            background: conic-gradient(
              #1890ff 0deg 126deg,
              #52c41a 126deg 226.8deg,
              #faad14 226.8deg 306deg,
              #f5222d 306deg 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05); // 进一步减轻阴影避免遮挡

            &::before {
              content: '';
              width: 80px; // 适当减少尺寸
              height: 80px; // 适当减少尺寸
              background: $background-color-white;
              border-radius: 50%;
              position: absolute;
              box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); // 进一步减轻阴影避免遮挡
            }

            .pie-center {
              position: relative;
              z-index: 1;
              text-align: center;

              .total-label {
                font-size: $font-size-xs;
                color: $text-color-secondary;
                margin-bottom: 2px;
                font-weight: $font-weight-medium;
              }

              .total-value {
                font-size: $font-size-lg;
                color: $text-color-primary;
                font-weight: 600;
              }
            }
          }
        }

        .category-legend {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start; // 改为顶部对齐，避免与饼图重叠
          gap: $spacing-sm; // 适当增加间距
          overflow: visible; // 改为可见，确保内容不被裁切
          padding: 0; // 移除内边距避免布局问题
          background: $background-color-white; // 添加背景确保显示清晰
          border-radius: $border-radius-base;
          z-index: 2; // 确保图例在饼图上层

          .legend-item {
            display: flex;
            align-items: center;
            gap: $spacing-sm; // 减少间距
            padding: $spacing-sm $spacing-md; // 恢复一些内边距
            border-radius: $border-radius-base;
            transition: all 0.3s ease;
            min-height: 36px; // 减少高度，紧凑布局
            overflow: hidden; // 防止内容溢出
            background: transparent; // 透明背景

            &:hover {
              background: $background-color-light;
              transform: translateX(2px); // 轻微右移提供视觉反馈
              box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08); // 添加轻微阴影
            }

            .legend-color {
              width: 14px;
              height: 14px;
              border-radius: 3px;
              flex-shrink: 0; // 防止压缩
              box-shadow: 0 1px 2px rgba(0, 0, 0, 0.14); // 调低阴影 30%
            }

            .legend-name {
              flex: 1;
              font-size: $font-size-sm;
              color: $text-color-primary;
              font-weight: $font-weight-medium;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis; // 超长文字显示省略号
              min-width: 0; // 允许 flex 项目缩小
            }

            .legend-value {
              font-size: $font-size-sm;
              color: $text-color-secondary;
              font-weight: 600;
              flex-shrink: 0; // 防止压缩
              min-width: 40px; // 确保百分比有足够空间
              text-align: right;
            }
          }
        }
      }
    }
  }

  // ===== 响应式设计优化 =====
  // 注意：主要的间距响应式设计已在上面各个组件中统一定义

  @media (max-width: $breakpoint-md) {
    // 注意：主要的gap间距已在上面定义，这里只处理其他样式

    .stats-section .ant-card {
      height: 130px; // 移动端固定高度
    }

    .charts-section {
      .main-chart-card,
      .ranking-card {
        height: auto; // 在中等屏幕上使用自适应高度
      }
      
      .main-chart-card {
        .chart-header {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-md;

          .chart-controls {
            width: 100%;
            justify-content: space-between;
          }
        }

        .chart-content .chart-placeholder {
          min-height: 280px;
        }
      }
    }

    .bottom-section {
      .online-stores-card,
      .category-card {
        height: auto; // 在中等屏幕上使用自适应高度
      }
      
      .online-stores-card .stores-content .stores-grid {
        // 中等屏幕（768px以下）：每行2个卡片，充分利用空间
        grid-template-columns: repeat(2, 1fr);
        gap: $spacing-sm; // 减少间距以适应更紧凑的布局

        .store-card {
          padding: $spacing-sm; // 减少内边距

          .store-avatar {
            min-width: 32px;
            height: 32px;
            font-size: 16px;
          }

          .store-details {
            .store-name {
              font-size: 13px;
              line-height: 1.3;
            }

            .store-status .ant-tag {
              font-size: 11px;
              padding: 2px 6px;
              line-height: 1.2;
            }
          }
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .charts-section {
      .main-chart-card .chart-header .chart-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-sm;
      }
    }
  }

  // ===== 动画效果 =====
  .stats-grid,
  .charts-grid,
  .bottom-grid {
    // Grid布局过渡动画
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .stats-section .ant-card,
  .main-chart-card,
  .ranking-card,
  .online-stores-card,
  .category-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;

    // 响应式过渡动画
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .stats-section .ant-card {
    @for $i from 1 through 4 {
      &:nth-child(#{$i}) {
        animation-delay: #{($i - 1) * 0.1}s;
      }
    }
  }

  .charts-section .main-chart-card {
    animation-delay: 0.4s;
  }

  .charts-section .ranking-card {
    animation-delay: 0.5s;
  }

  .bottom-section .category-card {
    animation-delay: 0.6s;
  }

  .bottom-section .online-stores-card {
    animation-delay: 0.7s;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // ===== 深色主题支持 =====
  .dark & {
    .stats-section .ant-card,
    .main-chart-card,
    .ranking-card,
    .online-stores-card,
    .category-card {
      background: $dark-component-background;
      border-color: $dark-border-color-base;

      .ant-card-body {
        background: transparent;
      }
    }

    .chart-content .chart-placeholder,
    .pie-chart-placeholder {
      background: rgba(255, 255, 255, 0.05);
    }

    .store-card {
      background: rgba(255, 255, 255, 0.05);
      border-color: $dark-border-color-base;
    }
  }
}

// ===== 统一的响应式间距系统 =====
// 注意：所有间距已在上面各个组件中使用统一的变量定义：
// - 电脑端 (>1024px): $spacing-md (16px)
// - 平板端 (768px-1024px): $spacing-sm (8px)
// - 手机端 (<768px): $spacing-xs (4px)

// 移动端页面边距优化
@media (max-width: 768px) {
  .dashboard-v2 {
    padding: 0 $spacing-sm; // 手机端页面边距：8px
  }
}

// 注意：所有响应式间距已在上面各个组件中统一定义，无需重复