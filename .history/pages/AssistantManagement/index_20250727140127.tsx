import React, { useEffect } from "react";
import {
  Button,
  Space,
  Modal,
  Tag,
  message,
  Card,
  Empty,
  Input,
  Select,
  Tabs,
  Avatar,
  Dropdown,
  Spin,
  Pagination
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  EyeOutlined,
  UserOutlined,
  SettingOutlined,
  MoreOutlined,
  StarFilled,
  DatabaseOutlined
} from "@ant-design/icons";
import FramerPageAnimation from "../../components/FramerPageAnimation";
import AssistantFormModal from "../../components/AssistantFormModal";
import AssistantDetailModal from "../../components/AssistantDetailModal";
import CategoryManagementModal from "../../components/CategoryManagementModal";
import { useAssistantManagementStore } from "../../stores";
import type {
  Assistant,
  AssistantCategory,
  AssistantQueryParams,
} from "../../types/assistant";
import { handleApiError } from "../../services/assistantService";
import "./style.scss";

const { TabPane } = Tabs;
const { Search } = Input;
const { Option } = Select;

// Mock数据 - 实际项目中应该从API获取
const mockCategories: AssistantCategory[] = [
  {
    key: "general",
    label: "通用助手",
    description: "适用于各种日常对话和问答",
  },
  {
    key: "writing",
    label: "写作助手",
    description: "专门用于各类文档写作和内容创作",
  },
  {
    key: "coding",
    label: "编程助手",
    description: "专门用于编程相关问题解答和代码生成",
  },
  {
    key: "analysis",
    label: "分析助手",
    description: "专门用于数据分析和报告生成",
  },
  {
    key: "translation",
    label: "翻译助手",
    description: "专门用于多语言翻译和本地化",
  },
  {
    key: "education",
    label: "教育助手",
    description: "专门用于教学和学习辅导",
  },
  {
    key: "business",
    label: "商务助手",
    description: "专门用于商务沟通和文档处理",
  },
  {
    key: "creative",
    label: "创意助手",
    description: "专门用于创意写作和内容创作",
  },
];

const mockAssistants: Assistant[] = [
  {
    id: "1",
    name: "Vue.js开发助手",
    description:
      "专门用于Vue.js开发的智能助手，提供代码建议、最佳实践和问题解答",
    category: "coding",
    tags: ["Vue.js", "JavaScript", "TypeScript", "前端开发"],
    config: {
      system_prompt: "你是一个专业的Vue.js开发助手...",
      temperature: 0.7,
      max_tokens: 2000,
      top_p: 0.9,
    },
    model_id: "gpt-4",
    is_public: true,
    is_active: true,
    is_system: true,
    owner_id: "system",
    created_by: "系统",
    knowledge_base_ids: ["kb-1", "kb-2"],
    rag_enabled: true,
    usage_count: 1250,
    last_used_at: "2024-06-20T15:30:00Z",
    message_count: 3200,
    total_tokens: 450000,
    rag_query_count: 890,
    average_rating: 4.8,
    rating_count: 156,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-06-15T14:30:00Z",
  },
  {
    id: "2",
    name: "通用问答助手",
    description: "通用的智能问答助手，可以回答各种日常问题",
    category: "general",
    tags: ["通用", "问答", "日常"],
    config: {
      system_prompt: "你是一个友好、专业的通用助手...",
      temperature: 0.8,
      max_tokens: 1500,
      top_p: 0.9,
    },
    model_id: "gpt-3.5-turbo",
    is_public: true,
    is_active: true,
    is_system: true,
    owner_id: "system",
    created_by: "系统",
    knowledge_base_ids: [],
    rag_enabled: false,
    usage_count: 2100,
    last_used_at: "2024-06-20T16:45:00Z",
    message_count: 5600,
    total_tokens: 680000,
    rag_query_count: 0,
    average_rating: 4.5,
    rating_count: 234,
    created_at: "2024-01-10T09:00:00Z",
    updated_at: "2024-06-18T11:20:00Z",
  },
];

const AssistantManagement: React.FC = () => {
  // 使用 Zustand store
  const {
    // State
    assistants,
    categories,
    assistantPagination,
    assistantActiveTab,
    assistantSearchKeyword,
    assistantSelectedCategory,
    assistantRagFilter,
    assistantLoading,
    assistantError,
    formModalVisible,
    detailModalVisible,
    categoryModalVisible,
    editingAssistant,
    // Actions
    loadAssistants,
    setAssistantActiveTab,
    setAssistantSearchKeyword,
    setAssistantSelectedCategory,
    setAssistantRagFilter,
    setAssistantPagination,
    setFormModalVisible,
    setDetailModalVisible,
    setCategoryModalVisible,
    setEditingAssistant,
    setViewingAssistant,
    createAssistant,
    updateAssistant,
    deleteAssistant,
    duplicateAssistant,
  } = useAssistantManagementStore();

  // 获取助手列表
  const fetchAssistants = async (_params?: AssistantQueryParams) => {
    await loadAssistants({
         keyword: assistantSearchKeyword,
         category: assistantSelectedCategory,
         rag_enabled: assistantRagFilter,
         page: assistantPagination.current,
         page_size: assistantPagination.pageSize,
       });
  };

  useEffect(() => {
    fetchAssistants();
  }, [assistantActiveTab, assistantSearchKeyword, assistantSelectedCategory, assistantRagFilter]);

  // 获取分类标签
  const getCategoryLabel = (categoryKey: string) => {
    const category = categories.find((c) => c.key === categoryKey);
    return category ? category.label : categoryKey;
  };

  // 处理操作
  const handleCreate = () => {
    setEditingAssistant(null);
    setFormModalVisible(true);
  };

  const handleEdit = (assistant: Assistant) => {
    setEditingAssistant(assistant);
    setFormModalVisible(true);
  };

  const handleView = (assistant: Assistant) => {
    setViewingAssistant(assistant);
    setDetailModalVisible(true);
  };

  const handleDuplicate = async (assistant: Assistant) => {
    try {
      const duplicatedAssistant = {
        ...assistant,
        id: undefined,
        name: `${assistant.name} (副本)`,
        is_public: false,
        is_system: false,
      };
      setEditingAssistant(duplicatedAssistant as unknown as Assistant);
      setFormModalVisible(true);
    } catch (error) {
      handleApiError(error, "复制助手失败");
    }
  };

  const handleDelete = (assistant: Assistant) => {
    Modal.confirm({
      title: "确认删除",
      content: `确定要删除助手 "${assistant.name}" 吗？此操作不可撤销。`,
      okText: "删除",
      okType: "danger",
      cancelText: "取消",
      onOk: async () => {
        try {
          // 这里应该调用删除API
          message.success("删除成功");
          fetchAssistants();
        } catch (error) {
          handleApiError(error, "删除助手失败");
        }
      },
    });
  };

  // 格式化数字
  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }
    return num.toString();
  };

  // 渲染助手卡片
  const renderAssistantCard = (assistant: Assistant) => {
    const menuItems = [
      {
        key: "view",
        icon: <EyeOutlined />,
        label: "查看详情",
        onClick: () => handleView(assistant),
      },
      {
        key: "edit",
        icon: <EditOutlined />,
        label: "编辑",
        onClick: () => handleEdit(assistant),
      },
      {
        key: "copy",
        icon: <CopyOutlined />,
        label: "复制",
        onClick: () => handleDuplicate(assistant),
      },
      {
        key: "config",
        icon: <SettingOutlined />,
        label: "配置",
        onClick: () => handleEdit(assistant),
      },
      {
        type: "divider" as const,
      },
      {
        key: "delete",
        icon: <DeleteOutlined />,
        label: "删除",
        danger: true,
        onClick: () => handleDelete(assistant),
      },
    ];

    return (
      <Col xs={24} sm={12} lg={8} xl={6} key={assistant.id}>
        <div className="assistant-card-wrapper">
          <Card className="assistant-card-new" hoverable>
            {/* 卡片头部 */}
            <div className="card-header">
              <div className="header-left">
                <Avatar
                  size={40}
                  icon={<UserOutlined />}
                  className="assistant-avatar"
                />

                <div className="header-info">
                  <h4 className="assistant-title">
                    {assistant.name}
                  </h4>
                  <div className="assistant-meta">
                    <Tag className="category-tag">
                      {getCategoryLabel(assistant.category)}
                    </Tag>
                    {assistant.is_active && (
                      <span className="status-dot"></span>
                    )}
                  </div>
                </div>
              </div>
              <Dropdown
                menu={{ items: menuItems }}
                trigger={["click"]}
                placement="bottomRight"
              >
                <Button
                  type="text"
                  icon={<MoreOutlined />}
                  className="more-action-btn"
                />
              </Dropdown>
            </div>

            {/* 卡片内容 */}
            <div className="card-body">
              <p className="assistant-description">
                {assistant.description}
              </p>

              {/* 标签区域 */}
              {assistant.tags.length > 0 && (
                <div className="tags-area">
                  {assistant.tags.slice(0, 3).map((tag: string) => (
                    <Tag key={tag} className="feature-tag">
                      {tag}
                    </Tag>
                  ))}
                  {assistant.tags.length > 3 && (
                    <span className="more-tags-indicator">
                      +{assistant.tags.length - 3}
                    </span>
                  )}
                </div>
              )}

              {/* 统计数据区域 */}
              <div className="stats-grid">
                <div className="stat-cell">
                  <div className="stat-number">
                    {formatNumber(assistant.usage_count)}
                  </div>
                  <div className="stat-text">
                    使用次数
                  </div>
                </div>
                <div className="stat-cell">
                  <div className="stat-number">
                    {assistant.average_rating.toFixed(1)}
                    <StarFilled className="rating-star" />
                  </div>
                  <div className="stat-text">
                    平均评分
                  </div>
                </div>
                <div className="stat-cell">
                  <div className="stat-number">
                    {formatNumber(assistant.message_count)}
                  </div>
                  <div className="stat-text">
                    消息数
                  </div>
                </div>
              </div>

              {/* 知识库信息 */}
              {assistant.rag_enabled &&
                assistant.knowledge_base_ids.length > 0 && (
                  <div className="knowledge-info">
                    <DatabaseOutlined />
                    <span>
                      {assistant.knowledge_base_ids.length} 个知识库
                    </span>
                  </div>
                )}
            </div>

            {/* 卡片底部 */}
            <div className="card-footer">
              <span className="create-time">
                {new Date(assistant.created_at)
                  .toLocaleDateString("zh-CN", {
                    year: "numeric",
                    month: "2-digit",
                    day: "2-digit",
                  })
                  .replace(/\//g, "/")}
              </span>
              <Tag color="green" className="running-status">
                运行中
              </Tag>
            </div>
          </Card>
        </div>
      </Col>
    );
  };

  return (
    <FramerPageAnimation delay={0}>
      <div className="assistant-management">
      {/* 主要内容区域 */}
      <div className="main-content-section">
        <Card className="main-content">
        <div className="content-header">
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
            >
              创建助手
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={() => setCategoryModalVisible(true)}
            >
              分类管理
            </Button>
          </Space>
        </div>

        <Tabs
          activeKey={assistantActiveTab}
          onChange={(key: string) => setAssistantActiveTab(key as "my" | "public" | "all")}
          className="assistant-tabs"
        >
          <TabPane tab="我的助手" key="my">
            <div className="tab-content">
              <div className="toolbar">
                <Space wrap>
                  <Search
                    placeholder="搜索助手名称、描述或标签"
                    value={assistantSearchKeyword}
                    onChange={(e) => setAssistantSearchKeyword(e.target.value)}
                    style={{ width: 300 }}
                    allowClear
                  />

                  <Select
                    placeholder="选择分类"
                    value={assistantSelectedCategory}
                    onChange={setAssistantSelectedCategory}
                    style={{ width: 150 }}
                    allowClear
                  >
                    {categories.map((category) => (
                      <Option key={category.key} value={category.key}>
                        {category.label}
                      </Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="RAG状态"
                    value={assistantRagFilter}
                    onChange={setAssistantRagFilter}
                    style={{ width: 120 }}
                    allowClear
                  >
                    <Option value={true}>已启用RAG</Option>
                    <Option value={false}>未启用RAG</Option>
                  </Select>
                </Space>
              </div>

              <Spin spinning={assistantLoading}>
                {assistants.length === 0 ? (
                  <Empty description="暂无助手数据" />
                ) : (
                  <>
                    <Row gutter={[16, 16]} className="assistant-grid">
                      {assistants.map(renderAssistantCard)}
                    </Row>

                    {assistantPagination.total > assistantPagination.pageSize && (
                      <div className="pagination-wrapper">
                        <Pagination
                          current={assistantPagination.current}
                          pageSize={assistantPagination.pageSize}
                          total={assistantPagination.total}
                          showSizeChanger
                          showQuickJumper
                          showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                          }
                          onChange={(page, pageSize) => {
                            setAssistantPagination({
                              ...assistantPagination,
                              current: page,
                              pageSize: pageSize || assistantPagination.pageSize,
                            });
                          }}
                        />
                      </div>
                    )}
                  </>
                )}
              </Spin>
            </div>
          </TabPane>

          <TabPane tab="公开助手" key="public">
            <div className="tab-content">
              <div className="toolbar">
                <Space wrap>
                  <Search
                    placeholder="搜索助手名称、描述或标签"
                    value={assistantSearchKeyword}
                    onChange={(e) => setAssistantSearchKeyword(e.target.value)}
                    style={{ width: 300 }}
                    allowClear
                  />

                  <Select
                    placeholder="选择分类"
                    value={assistantSelectedCategory}
                    onChange={setAssistantSelectedCategory}
                    style={{ width: 150 }}
                    allowClear
                  >
                    {categories.map((category) => (
                      <Option key={category.key} value={category.key}>
                        {category.label}
                      </Option>
                    ))}
                  </Select>
                  <Select
                    placeholder="RAG状态"
                    value={assistantRagFilter}
                    onChange={setAssistantRagFilter}
                    style={{ width: 120 }}
                    allowClear
                  >
                    <Option value={true}>已启用RAG</Option>
                    <Option value={false}>未启用RAG</Option>
                  </Select>
                </Space>
              </div>

              <Spin spinning={assistantLoading}>
                {assistants.length === 0 ? (
                  <Empty description="暂无助手数据" />
                ) : (
                  <>
                    <Row gutter={[16, 16]} className="assistant-grid">
                      {assistants.map(renderAssistantCard)}
                    </Row>

                    {assistantPagination.total > assistantPagination.pageSize && (
                      <div className="pagination-wrapper">
                        <Pagination
                          current={assistantPagination.current}
                          pageSize={assistantPagination.pageSize}
                          total={assistantPagination.total}
                          showSizeChanger
                          showQuickJumper
                          showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                          }
                          onChange={(page, pageSize) => {
                            setAssistantPagination({
                              ...assistantPagination,
                              current: page,
                              pageSize: pageSize || assistantPagination.pageSize,
                            });
                          }}
                        />
                      </div>
                    )}
                  </>
                )}
              </Spin>
            </div>
          </TabPane>
        </Tabs>
        </Card>
      </div>

      {/* 助手表单模态框 */}
      <AssistantFormModal
        visible={formModalVisible}
        assistant={editingAssistant}
        categories={categories}
        onCancel={() => setFormModalVisible(false)}
        onSuccess={() => {
          setFormModalVisible(false);
          fetchAssistants();
        }}
      />

      {/* 助手详情模态框 */}
      <AssistantDetailModal
        visible={detailModalVisible}
        assistant={editingAssistant}
        categories={categories}
        onCancel={() => setDetailModalVisible(false)}
        onEdit={(assistant: any) => {
          setDetailModalVisible(false);
          handleEdit(assistant);
        }}
        onDuplicate={(assistant: any) => {
          setDetailModalVisible(false);
          handleDuplicate(assistant);
        }}
        onDelete={(assistant: any) => {
          setDetailModalVisible(false);
          handleDelete(assistant);
        }}
      />

      {/* 分类管理模态框 */}
      <CategoryManagementModal
        visible={categoryModalVisible}
        categories={categories}
        onCancel={() => setCategoryModalVisible(false)}
        onSuccess={(newCategories: any) => {
          // 这里应该调用store中的更新分类方法
          setCategoryModalVisible(false);
        }}
      />
      </div>
    </FramerPageAnimation>
  );
};

export default AssistantManagement;
