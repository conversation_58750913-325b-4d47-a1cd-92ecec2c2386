@use '../../styles/variables' as *;

// 助手管理页面样式 - 与项目整体设计风格保持一致
.assistant-management {
  // 使用flex布局进行页面结构 - 与Dashboard保持一致
  display: flex;
  flex-direction: column;
  gap: 16px; // 与Dashboard保持一致的间距
  padding: 0;
  background: var(--theme-bg-secondary);
  min-height: 100%;
  transition: background-color 0.2s ease;

  // 页面sections共同样式
  .main-content-section {
    width: 100%;
  }

  // 页面标题区域 - 增强视觉效果
  .page-header {
    margin-bottom: 0; // 使用gap控制间距

    h1 {
      color: var(--theme-text-primary);
      font-weight: $font-weight-semibold;
      font-size: $font-size-xxl;
      transition: color 0.2s ease;
      margin-bottom: $spacing-xs;
      line-height: $line-height-tight;
      display: flex;
      align-items: center;
    }

    p {
      color: var(--theme-text-secondary);
      font-size: $font-size-base;
      margin: 0;
      transition: color 0.2s ease;
      line-height: $line-height-base;
    }
  }



  // 主要内容区域 - 统一Dashboard卡片风格
  .main-content-section {
    .main-content {
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-lg; // 与Dashboard一致
      box-shadow: var(--theme-shadow-1);
      background: var(--theme-bg-primary);
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        box-shadow: var(--theme-shadow-2);
      }

      .ant-card-body {
        padding: 0;
      }

      .content-header {
        padding: 16px;
        border-bottom: 1px solid var(--theme-border-color-split);
        background: var(--theme-bg-tertiary);
        transition: background-color 0.2s ease, border-color 0.2s ease;

        .ant-btn {
          border-radius: $button-border-radius;
          font-size: $button-font-size-base;
          font-weight: $font-weight-medium;
          transition: $transition-base;
        }
      }

      // 标签页样式优化
      .assistant-tabs {
        .ant-tabs-nav {
          padding: 0 16px;
          margin-bottom: 0;
          background: var(--theme-bg-primary);
          transition: background-color 0.2s ease;

          .ant-tabs-tab {
            font-weight: $font-weight-medium;
            font-size: $font-size-base;
            padding: $spacing-md 16px;

            &.ant-tabs-tab-active {
              font-weight: $font-weight-semibold;
            }
          }
        }

        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              .tab-content {
                padding: 16px;

                .toolbar {
                  margin-bottom: 16px;
                  padding: $spacing-md;
                  background: var(--theme-bg-tertiary);
                  border-radius: $border-radius-base;
                  border: 1px solid var(--theme-border-color-split);
                  transition: background-color 0.2s ease, border-color 0.2s ease;

                  .ant-input-search {
                    .ant-input {
                      border-radius: $border-radius-sm;
                    }
                  }

                  .ant-select {
                    .ant-select-selector {
                      border-radius: $border-radius-sm;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 助手卡片网格
  .assistant-grid {
    .assistant-card-wrapper {
      .assistant-card-new {
        border: 1px solid var(--theme-border-color-split);
        border-radius: $border-radius-lg;
        box-shadow: var(--theme-shadow-1);
        background: var(--theme-bg-primary);
        transition: all 0.3s ease;
        height: 100%;
        overflow: hidden;

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--theme-shadow-2);
          border-color: $primary-color-light;
        }

        .ant-card-body {
          padding: 16px;
          display: flex;
          flex-direction: column;
          height: 100%;
        }

        // 卡片头部
        .card-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: $spacing-md;

          .header-left {
            display: flex;
            align-items: flex-start;
            gap: $spacing-sm;
            flex: 1;

            .assistant-avatar {
              background: linear-gradient(135deg, $primary-color-light, rgba(24, 144, 255, 0.2));
              color: $primary-color;
              border: 2px solid rgba(24, 144, 255, 0.1);
              flex-shrink: 0;
            }

            .header-info {
              flex: 1;
              min-width: 0;

              .assistant-title {
                font-size: $font-size-lg;
                font-weight: $font-weight-semibold;
                color: var(--theme-text-primary);
                margin-bottom: 4px;
                line-height: $line-height-tight;
                transition: color 0.2s ease;
                @include text-ellipsis;
              }

              .assistant-meta {
                display: flex;
                align-items: center;
                gap: $spacing-xs;

                .category-tag {
                  font-size: $font-size-xs;
                  padding: 2px 8px;
                  border-radius: $border-radius-sm;
                  background: var(--theme-bg-tertiary);
                  border: 1px solid var(--theme-border-color);
                  color: var(--theme-text-secondary);
                  transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
                  font-weight: $font-weight-medium;
                }

                .status-dot {
                  width: 8px;
                  height: 8px;
                  border-radius: 50%;
                  background: $success-color;
                  box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
                }
              }
            }
          }

          .more-action-btn {
            color: var(--theme-text-tertiary);
            border: none;
            box-shadow: none;
            padding: 4px;
            transition: color 0.2s ease, background-color 0.2s ease;

            &:hover {
              color: var(--theme-text-secondary);
              background: var(--theme-bg-tertiary);
            }
          }
        }

        // 卡片内容
        .card-body {
          flex: 1;
          display: flex;
          flex-direction: column;

          .assistant-description {
            color: var(--theme-text-secondary);
            font-size: $font-size-sm;
            line-height: $line-height-base;
            transition: color 0.2s ease;
            margin-bottom: $spacing-md;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }

          .tags-area {
            margin-bottom: $spacing-md;
            display: flex;
            flex-wrap: wrap;
            gap: $spacing-xs;
            align-items: center;

            .feature-tag {
              font-size: $font-size-xs;
              padding: 2px 6px;
              border-radius: $border-radius-sm;
              background: rgba(24, 144, 255, 0.08);
              border: 1px solid rgba(24, 144, 255, 0.2);
              color: $primary-color;
              font-weight: $font-weight-medium;
            }

            .more-tags-indicator {
              font-size: $font-size-xs;
              color: var(--theme-text-tertiary);
              transition: color 0.2s ease;
              font-weight: $font-weight-medium;
            }
          }

          .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: $spacing-sm;
            margin-bottom: $spacing-md;

            .stat-cell {
              text-align: center;
              padding: $spacing-xs;
              border-radius: $border-radius-sm;
              background: var(--theme-bg-tertiary);
              transition: background-color 0.2s ease;

              .stat-number {
                font-size: $font-size-base;
                font-weight: $font-weight-semibold;
                color: var(--theme-text-primary);
                transition: color 0.2s ease;
                margin-bottom: 2px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2px;

                .rating-star {
                  font-size: $font-size-xs;
                  color: $warning-color;
                }
              }

              .stat-text {
                font-size: $font-size-xs;
                color: var(--theme-text-tertiary);
                transition: color 0.2s ease;
                font-weight: $font-weight-medium;
              }
            }
          }

          .knowledge-info {
            display: flex;
            align-items: center;
            gap: $spacing-xs;
            color: var(--theme-text-secondary);
            transition: color 0.2s ease;
            font-size: $font-size-xs;
            font-weight: $font-weight-medium;
            margin-bottom: $spacing-sm;

            .anticon {
              color: $primary-color;
            }
          }
        }

        // 卡片底部
        .card-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-top: $spacing-sm;
          border-top: 1px solid var(--theme-border-color-split);
          transition: border-color 0.2s ease;

          .create-time {
            font-size: $font-size-xs;
            color: var(--theme-text-tertiary);
            transition: color 0.2s ease;
            font-weight: $font-weight-medium;
          }

          .running-status {
            font-size: $font-size-xs;
            padding: 2px 8px;
            border-radius: $border-radius-sm;
            font-weight: $font-weight-medium;
          }
        }
      }
    }
  }

  // 分页器样式
  .pagination-wrapper {
    display: flex;
    justify-content: center;
    padding: 16px;
    border-top: 1px solid var(--theme-border-color-split);
    background: var(--theme-bg-tertiary);
    transition: background-color 0.2s ease, border-color 0.2s ease;

    .ant-pagination {
      .ant-pagination-total-text {
        color: var(--theme-text-secondary);
        transition: color 0.2s ease;
        font-size: $font-size-sm;
      }
    }
  }

  // ===== 动画效果已移除，使用统一的 PageLoadingAnimation =====

  // ===== 响应式设计 - 与Dashboard保持一致 =====
  @media (min-width: 1200px) {
    .assistant-management {
      gap: 32px;
    }
  }

  @media (max-width: 768px) {
    .assistant-management {
      padding: 0 8px;
      gap: 16px;
    }
  }

  @media (max-width: 480px) {
    .assistant-management {
      gap: 12px;
    }
    }
  }

  @media (max-width: $breakpoint-md) {
    .assistant-management {
      .assistant-grid {
      .assistant-card-wrapper .assistant-card-new {
        .card-header .header-left {
          flex-direction: column;
          align-items: flex-start;
          gap: $spacing-xs;

          .assistant-avatar {
            align-self: flex-start;
          }
        }

        .card-body .stats-grid {
          grid-template-columns: 1fr;
          gap: $spacing-xs;
        }
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    .assistant-management .main-content-section .main-content {
      .content-header {
        padding: $spacing-md;
      }

      .assistant-tabs {
        .ant-tabs-nav {
          padding: 0 $spacing-md;
        }

        .ant-tabs-content-holder .ant-tabs-content .ant-tabs-tabpane .tab-content {
          padding: $spacing-md;

          .toolbar {
            flex-direction: column;
            gap: $spacing-sm;

            .ant-space {
              width: 100%;
              flex-direction: column;

              .ant-input-search {
                width: 100% !important;
              }
            }
          }
        }
      }
    }
  }

}

// ===== 主题支持现在通过CSS变量自动处理 =====
