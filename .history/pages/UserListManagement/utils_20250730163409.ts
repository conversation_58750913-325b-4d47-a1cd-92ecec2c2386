import dayjs from 'dayjs';
import { UserData, STATUS_CONFIG } from './types';

// 生成模拟用户数据
export const generateMockUsers = (): UserData[] => {
  const users: UserData[] = [];
  const usernames = [
    'admin', 'john_doe', 'jane_smith', 'bob_wilson', 'alice_brown',
    'charlie_davis', 'diana_miller', 'edward_jones', 'fiona_garcia', 'george_martinez',
    'helen_rodriguez', 'ivan_lopez', 'julia_gonzalez', 'kevin_anderson', 'linda_taylor',
    'michael_thomas', 'nancy_jackson', 'oliver_white', 'patricia_harris', 'quincy_martin',
    'rachel_thompson', 'samuel_garcia', 'tina_martinez', 'ursula_robinson', 'victor_clark',
    'wendy_rodriguez', 'xavier_lewis', 'yvonne_lee', 'zachary_walker', 'amy_hall'
  ];

  // 中文昵称数据池 - 多样化风格
  const nicknames = [
    '超级管理员', '阳光小鹿', '温柔如水', '代码诗人', '梦想家',
    '星空漫步', '花开半夏', '清风徐来', '墨染青春', '时光旅人',
    '彩虹糖果', '月下独酌', '书香墨韵', '微风细雨', '暖阳如你',
    '青春无悔', '梦里花开', '静水流深', '云淡风轻', '岁月如歌',
    '温暖如初', '简单快乐', '追梦少年', '柠檬味的夏天', '小确幸',
    '向日葵的微笑', '蓝色妖姬', '甜甜圈', '咖啡时光', '小幸运'
  ];

  // 性别数据池
  const genders: ('male' | 'female' | 'unknown')[] = ['male', 'female', 'unknown'];

  // 手机号前缀（真实的中国大陆手机号段）
  const phonePrefix = [
    '138', '139', '150', '151', '152', '158', '159', '188', '189',
    '130', '131', '132', '155', '156', '185', '186', '176', '175',
    '133', '153', '180', '181', '189', '177', '173', '149', '199'
  ];

  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'company.com'];
  const usedPhones = new Set<string>(); // 确保手机号唯一性

  // 生成唯一手机号的辅助函数
  const generateUniquePhone = (): string => {
    let phone: string;
    do {
      const prefix = phonePrefix[Math.floor(Math.random() * phonePrefix.length)];
      const suffix = Math.floor(Math.random() * *********).toString().padStart(8, '0');
      phone = prefix + suffix;
    } while (usedPhones.has(phone));
    usedPhones.add(phone);
    return phone;
  };

  for (let i = 0; i < usernames.length; i++) {
    const username = usernames[i];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    const role = i === 0 ? 'admin' : Math.random() > 0.7 ? 'admin' : 'user';
    const status = Math.random() > 0.2 ? 'active' : 'inactive';
    
    // 生成随机的创建时间（过去30天内）
    const createdAt = dayjs().subtract(Math.floor(Math.random() * 30), 'day').toISOString();
    
    // 生成随机的最后登录时间（可能为空）
    const lastLoginAt = Math.random() > 0.3 
      ? dayjs().subtract(Math.floor(Math.random() * 7), 'day').toISOString()
      : null;

    users.push({
      id: i + 1,
      username,
      email: `${username}@${domain}`,
      role: role as 'admin' | 'user',
      status: status as 'active' | 'inactive',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${username}`,
      createdAt,
      lastLoginAt,
    });
  }

  return users;
};

// 格式化日期时间
export const formatDateTime = (dateString: string): string => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

// 获取用户状态文本
export const getUserStatusText = (status: 'active' | 'inactive'): string => {
  return STATUS_CONFIG[status].label;
};
