import dayjs from 'dayjs';
import { UserData, STATUS_CONFIG } from './types';

// 生成模拟用户数据
export const generateMockUsers = (): UserData[] => {
  const users: UserData[] = [];
  const usernames = [
    'admin', 'john_doe', 'jane_smith', 'bob_wilson', 'alice_brown',
    'charlie_davis', 'diana_miller', 'edward_jones', 'fiona_garcia', 'george_martinez',
    'helen_rodriguez', 'ivan_lopez', 'julia_gonzalez', 'kevin_anderson', 'linda_taylor',
    'michael_thomas', 'nancy_jackson', 'oliver_white', 'patricia_harris', 'quincy_martin',
    'rachel_thompson', 'samuel_garcia', 'tina_martinez', 'ursula_robinson', 'victor_clark',
    'wendy_rodriguez', 'xavier_lewis', 'yvonne_lee', 'zachary_walker', 'amy_hall'
  ];

  const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'company.com'];

  // 预定义的昵称列表
  const nicknames = [
    '超级管理员', '小约翰', '简妹妹', '鲍勃哥', '爱丽丝',
    '查理', '戴安娜', '爱德华', '菲奥娜', '乔治',
    '海伦', '伊万', '朱莉娅', '凯文', '琳达',
    '迈克尔', '南希', '奥利弗', '帕特里夏', '昆西',
    '瑞秋', '塞缪尔', '蒂娜', '乌苏拉', '维克多',
    '温迪', '泽维尔', '伊冯娜', '扎卡里', '艾米'
  ];

  // 性别选项
  const genders: ('male' | 'female' | 'unknown')[] = ['male', 'female', 'unknown'];

  // 生成随机手机号
  const generatePhoneNumber = (): string => {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139',
                     '150', '151', '152', '153', '155', '156', '157', '158', '159',
                     '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
    return prefix + suffix;
  };

  for (let i = 0; i < usernames.length; i++) {
    const username = usernames[i];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    const role = i === 0 ? 'admin' : Math.random() > 0.7 ? 'admin' : 'user';
    const status = Math.random() > 0.2 ? 'active' : 'inactive';

    // 生成随机的创建时间（过去30天内）
    const createdAt = dayjs().subtract(Math.floor(Math.random() * 30), 'day').toISOString();

    // 生成随机的最后登录时间（可能为空）
    const lastLoginAt = Math.random() > 0.3
      ? dayjs().subtract(Math.floor(Math.random() * 7), 'day').toISOString()
      : null;

    // 70% 的用户有昵称，30% 没有昵称
    const nickname = Math.random() > 0.3 ? nicknames[i] : undefined;

    // 随机分配性别，85% 有性别信息，15% 未知
    const gender = Math.random() > 0.15
      ? genders[Math.floor(Math.random() * 2)] // 只在 male 和 female 中选择
      : 'unknown';

    // 90% 的用户有手机号
    const phone = Math.random() > 0.1 ? generatePhoneNumber() : undefined;

    users.push({
      id: i + 1,
      username,
      nickname,
      email: `${username}@${domain}`,
      gender,
      phone,
      role: role as 'admin' | 'user',
      status: status as 'active' | 'inactive',
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${username}`,
      createdAt,
      lastLoginAt,
    });
  }

  return users;
};

// 格式化日期时间
export const formatDateTime = (dateString: string): string => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss');
};

// 获取用户状态文本
export const getUserStatusText = (status: 'active' | 'inactive'): string => {
  return STATUS_CONFIG[status].label;
};
