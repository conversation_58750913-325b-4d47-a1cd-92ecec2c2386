// 用户数据类型定义
export interface UserData {
  id: number;
  username: string;
  nickname?: string; // 昵称字段（可选）
  email: string;
  gender?: 'male' | 'female' | 'unknown'; // 性别字段（可选）
  phone?: string; // 手机号字段（可选）
  role: 'admin' | 'user';
  status: 'active' | 'inactive';
  avatar?: string;
  createdAt: string;
  lastLoginAt: string | null;
}

// 角色配置
export const ROLE_CONFIG = {
  admin: {
    label: '管理员',
    color: 'red',
  },
  user: {
    label: '普通用户',
    color: 'blue',
  },
} as const;

// 状态配置
export const STATUS_CONFIG = {
  active: {
    label: '启用',
    color: '#52c41a',
  },
  inactive: {
    label: '禁用',
    color: '#ff4d4f',
  },
} as const;
