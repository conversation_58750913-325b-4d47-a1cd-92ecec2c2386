@use '../../styles/variables' as *;

.user-management-refactored {
  padding: 0;
  background: var(--theme-bg-secondary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  transition: background-color 0.2s ease;

  @media (max-width: 1024px) {
    gap: $spacing-sm;
  }

  @media (max-width: 768px) {
    gap: $spacing-xs;
  }

  // 页面标题
  .page-header {
    margin-bottom: $spacing-md; // 电脑端：16px 间距
    padding-bottom: $spacing-md; // 电脑端：16px 内边距
    border-bottom: 1px solid var(--theme-border-color-split);
    transition: border-color 0.2s ease;

    // 平板端优化
    @media (max-width: 1024px) {
      margin-bottom: $spacing-sm; // 平板端：8px 间距
      padding-bottom: $spacing-sm; // 平板端：8px 内边距
    }

    // 手机端优化
    @media (max-width: 768px) {
      margin-bottom: $spacing-xs; // 手机端：4px 间距
      padding-bottom: $spacing-xs; // 手机端：4px 内边距
    }

    h1 {
      margin: 0 0 $spacing-xs 0; // 标题下方间距：4px
      font-size: 24px;
      font-weight: 600;
      color: var(--theme-text-primary);
      transition: color 0.2s ease;

      // 手机端字体大小优化
      @media (max-width: 768px) {
        font-size: 20px;
      }
    }

    p {
      margin: 0;
      color: var(--theme-text-secondary);
      font-size: 14px;
      transition: color 0.2s ease;
    }
  }

  // ProComponents QueryFilter 样式
  .ant-pro-query-filter {
    border: 1px solid var(--theme-border-color-split);
    border-radius: $border-radius-base;
    background: var(--theme-bg-primary);
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  // 表格容器 - 一体化设计（参考模型页面）
  .table-section {
    border: 1px solid var(--theme-border-color-split);
    border-radius: $border-radius-base;
    background: var(--theme-bg-primary);
    box-shadow: var(--theme-shadow-1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

    // 紧凑操作区域样式 - 作为表格顶部
    .compact-action-section {
      padding: $spacing-md; // 电脑端：16px 内边距
      background: var(--theme-bg-tertiary);
      border-bottom: 1px solid var(--theme-border-color-split);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-md; // 电脑端：16px 间距
      transition: background-color 0.2s ease, border-color 0.2s ease;

      // 平板端优化
      @media (max-width: 1024px) {
        padding: $spacing-sm; // 平板端：8px 内边距
        gap: $spacing-sm; // 平板端：8px 间距
      }

      // 手机端优化
      @media (max-width: 768px) {
        padding: $spacing-xs; // 手机端：4px 内边距
        gap: $spacing-xs; // 手机端：4px 间距
        flex-direction: column; // 手机端垂直布局
        align-items: stretch;
      }

    .action-buttons {
      flex-shrink: 0;
      display: flex;
      gap: $spacing-sm; // 按钮间距：8px

      // 手机端优化
      @media (max-width: 768px) {
        gap: $spacing-xs; // 手机端按钮间距：4px
        flex-wrap: wrap;
      }

      // 统一按钮样式 - 保持原生 Ant Design 动画效果
      .ant-btn {
        // 统一高度和基础样式
        height: 32px;
        border-radius: $border-radius-base;
        font-size: 14px;
        font-weight: 500;

        // 确保所有按钮变体都有统一高度
        &.ant-btn-sm {
          height: 32px;
        }

        &.ant-btn-lg {
          height: 32px;
        }

        // 保持原生动画，不添加自定义 transition 或 transform
        // 让 Ant Design 处理所有动画效果（包括波纹动画）
      }

    }
    }

    .stats-summary {
      flex-shrink: 0;

      .summary-items {
        display: flex;
        gap: $spacing-md; // 统计项间距：16px

        // 平板端优化
        @media (max-width: 1024px) {
          gap: $spacing-sm; // 平板端：8px 间距
        }

        // 手机端优化
        @media (max-width: 768px) {
          gap: $spacing-xs; // 手机端：4px 间距
          flex-direction: column;
        }

        .summary-item {
          display: flex;
          align-items: center;
          gap: $spacing-xs; // 图标与文字间距：4px
          font-size: 13px;
          color: var(--theme-text-secondary);
          transition: color 0.2s ease;

          .summary-icon {
            font-size: 14px;
            color: var(--theme-text-tertiary);
            transition: color 0.2s ease;

            &.enabled {
              color: $success-color;
            }
          }

          strong {
            color: var(--theme-text-primary);
            font-weight: 600;
            transition: color 0.2s ease;
          }
        }
      }
    }

    // 表格容器内的基本样式（保留有效的样式）
    // .tanstack-table {
    //   // 表格容器样式调整
    //   .table-container {
    //     background: transparent;
    //   }
    }
  }

  // 状态点样式
  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: $spacing-sm; // 状态点右边距：8px

    &.active {
      background: $success-color;
    }

    &.inactive {
      background: $error-color;
    }
  }




}

// ===== 统一的响应式间距系统 =====
// 注意：所有间距已在上面各个组件中使用统一的变量定义：
// - 电脑端 (>1024px): $spacing-md (16px)
// - 平板端 (768px-1024px): $spacing-sm (8px)
// - 手机端 (<768px): $spacing-xs (4px)

// 移动端页面边距优化
@media (max-width: 768px) {
  .user-management-refactored {
    padding: 0 $spacing-sm; // 手机端页面边距：8px
  }
}
