import React, { useMemo, useCallback, useEffect } from 'react';
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import {
  Button,
  Tag,
  Avatar,
  Space,
  Modal,
  message,
  Tooltip,
  Switch,
  Checkbox,
} from 'antd';
import {
  ManOutlined,
  WomanOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import {
  ProFormDateRangePicker,
  ProFormText,
  ProFormSelect,
  QueryFilter,
} from '@ant-design/pro-components';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  PlusOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import TanStackTable from '../../components/TanStackTable';
import { fuzzyFilter, dateRangeFilter } from '../../components/TanStackTable/utils';
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import UserCreateForm from '../../components/UserCreateForm';
import { 
  useUserManagementStore, 
  useFilteredUsers, 
  useUserStats, 
  useSelectionState,
  useFilteredSelectionActions 
} from '../../stores/pages/userManagementSlice';
import { UserData, ROLE_CONFIG, STATUS_CONFIG } from './types';
import {
  generateMockUsers,
  formatDateTime,
  getUserStatusText
} from './utils';
import { UserFormData } from '../../types/user';
import './style.scss';

// 创建列助手
const columnHelper = createColumnHelper<UserData>();

const UserManagementRefactored: React.FC = () => {

  // 使用 TanStack Table store (仅用于表格状态)
  const {
    loading: userManagementLoading,
    userManagementSorting: sorting,
    userManagementPagination: pagination,
    userManagementColumnFilters: columnFilters,
    userManagementGlobalFilter: globalFilter,
    setUserManagementSorting: setSorting,
    setUserManagementPagination: setPagination,
    setUserManagementColumnFilters: setColumnFilters,
    setUserManagementGlobalFilter: setGlobalFilter,
    resetUserManagementTableFilters: resetFilters,
  } = useUserManagementStore();

  // 使用用户管理专用 store
  const {
    loading: userLoading,
    showCreateForm,
    createFormLoading,
    dataInitialized,
    addUser,
    deleteUser,
    toggleUserStatus,
    setShowCreateForm,
    setCreateFormLoading,
    initializeData,
    batchDeleteUsers,
  } = useUserManagementStore();

  // 使用简化的选择器
  const filteredUsers = useFilteredUsers();
  const userStats = useUserStats();
  const { selectedUserIds, isAllSelected, isIndeterminate, selectedCount } = useSelectionState();
  
  // 使用筛选后的选择操作
  const { toggleUserSelection, toggleAllFilteredSelection, clearSelection } = useFilteredSelectionActions();

  // 获取筛选状态
  const roleFilter = useUserManagementStore(state => state.roleFilter);
  const statusFilter = useUserManagementStore(state => state.statusFilter);
  const dateRange = useUserManagementStore(state => state.dateRange);

  // 获取筛选操作函数
  const setRoleFilter = useUserManagementStore(state => state.setRoleFilter);
  const setStatusFilter = useUserManagementStore(state => state.setStatusFilter);
  const setDateRange = useUserManagementStore(state => state.setDateRange);
  const resetUserFilters = useUserManagementStore(state => state.resetFilters);

  // 合并加载状态
  const loading = userManagementLoading || userLoading;

  // 初始化数据
  useEffect(() => {
    const mockData = generateMockUsers();

    // 使用用户管理 store 初始化数据
    initializeData(mockData);
  }, [initializeData]); // 只在组件挂载时执行一次

  // 处理状态切换
  const handleStatusToggle = useCallback((userId: number, newStatus: boolean) => {
    toggleUserStatus(userId, newStatus);
    message.success(`用户状态已${newStatus ? '启用' : '禁用'}`);
  }, [toggleUserStatus]);

  // 处理添加用户
  const handleAddUser = useCallback(() => {
    setShowCreateForm(true);
  }, [setShowCreateForm]);

  // 处理用户创建表单提交
  const handleCreateUser = useCallback(async (values: UserFormData) => {
    try {
      setCreateFormLoading(true);


      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 将 UserFormData 转换为 UserData 格式
      const newUser: UserData = {
        id: Date.now(),
        username: values.username,
        email: values.email,
        role: values.role,
        status: values.status ? 'active' : 'inactive',
        createdAt: formatDateTime(new Date().toISOString()),
        lastLoginAt: null,
        avatar: values.avatar,
      };

      // 使用 store 添加用户
      addUser(newUser);

      message.success(`用户 "${values.username}" 创建成功！`);
      setShowCreateForm(false);

      // 重置分页到第一页
      setPagination({ ...pagination, pageIndex: 0 });

      // 清除所有筛选器
      resetUserFilters();



    } catch (error) {
      message.error('创建用户失败，请重试');
    } finally {
      setCreateFormLoading(false);
    }
  }, [addUser, setPagination, pagination, resetUserFilters, setCreateFormLoading, setShowCreateForm]);

  // 处理用户创建表单取消
  const handleCreateFormCancel = useCallback(() => {
    setShowCreateForm(false);
  }, [setShowCreateForm]);

  // 处理编辑用户
  const handleEditUser = useCallback((user: UserData) => {
    message.info(`编辑用户: ${user.username}`);
  }, []);

  // 处理删除用户
  const handleDeleteUser = useCallback((user: UserData) => {
    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除用户 "${user.username}" 吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        deleteUser(user.id);
        message.success('用户删除成功');
      },
    });
  }, [deleteUser]);

  // 处理批量删除
  const handleBatchDelete = useCallback(() => {
    if (selectedCount === 0) {
      message.warning('请先选择要删除的用户');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedCount} 个用户吗？此操作不可恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        batchDeleteUsers();
        message.success(`成功删除 ${selectedCount} 个用户`);
      },
    });
  }, [selectedCount, batchDeleteUsers]);

  // 处理行点击
  const handleRowClick = useCallback((_user: UserData) => {
    // 可以在这里添加行点击逻辑，比如显示详情
  }, []);

  // 获取用户名首字母或默认头像
  const getUserInitial = useCallback((username: string) => {
    return username ? username.charAt(0).toUpperCase() : 'U';
  }, []);

  // 使用 ref 跟踪上一次的筛选条件，避免无限循环
  const prevFiltersRef = React.useRef({ roleFilter, statusFilter, dateRange });
  
  // 当筛选条件变化时重置分页
  React.useEffect(() => {
    const prevFilters = prevFiltersRef.current;
    const filtersChanged = 
      prevFilters.roleFilter !== roleFilter ||
      prevFilters.statusFilter !== statusFilter ||
      JSON.stringify(prevFilters.dateRange) !== JSON.stringify(dateRange);
    
    if (filtersChanged) {
      setPagination({ pageIndex: 0, pageSize: pagination.pageSize });
      prevFiltersRef.current = { roleFilter, statusFilter, dateRange };
    }
  }, [roleFilter, statusFilter, dateRange, setPagination, pagination.pageSize]);

  // 列定义
  const columns = useMemo<ColumnDef<UserData, any>[]>(() => [
    // 批量选择列
    columnHelper.display({
      id: 'select',
      header: () => (
        <Checkbox
          checked={isAllSelected}
          indeterminate={isIndeterminate}
          onChange={toggleAllFilteredSelection}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={selectedUserIds.has(row.original.id)}
          onChange={() => toggleUserSelection(row.original.id)}
          onClick={(e) => e.stopPropagation()}
        />
      ),
      size: 50, // 选择列：50px
      enableSorting: false,
    }),
    // 头像列
    columnHelper.accessor('avatar', {
      id: 'avatar',
      header: '头像',
      size: 50, // 头像列：50px
      enableSorting: false,
      cell: ({ row }) => {
        const user = row.original;
        return (
          <div style={{ display: 'flex', justifyContent: 'left' }}>
            <Avatar
              size={32}
              icon={<UserOutlined />}
              src={user.avatar}
              style={{ 
                backgroundColor: user.avatar ? undefined : '#1890ff',
                color: '#fff'
              }}
            >
              {!user.avatar && getUserInitial(user.username)}
            </Avatar>
          </div>
        );
      },
    }),
    columnHelper.accessor('username', {
      id: 'username',
      header: '用户名',
      size: 145, // 用户名列：145px (增加10px显示长用户名)
      enableSorting: true,
      cell: ({ getValue }) => (
        <span style={{ fontWeight: 500 }}>{getValue()}</span>
      ),
    }),
    // 昵称列
    columnHelper.accessor('nickname', {
      id: 'nickname',
      header: '昵称',
      size: 112, // 昵称列：112px (增加7px显示中文昵称)
      enableSorting: false,
      cell: ({ getValue }) => (
        <span style={{ color: '#666' }}>{getValue() || '-'}</span>
      ),
    }),
    columnHelper.accessor('email', {
      id: 'email',
      header: '邮箱',
      size: 200, // 邮箱列：200px (增加空间显示完整邮箱)
      enableSorting: false,
      cell: ({ getValue }) => (
        <span style={{ color: '#666' }}>{getValue()}</span>
      ),
    }),
    // 性别列
    columnHelper.accessor('gender', {
      id: 'gender',
      header: '性别',
      size: 50, // 性别列：50px (图标显示更紧凑)
      enableSorting: true,
      cell: ({ getValue }) => {
        const gender = getValue();
        return (
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            {gender === 'male' && (
              <Tooltip title="男">
                <ManOutlined style={{ color: '#1890ff', fontSize: '16px' }} />
              </Tooltip>
            )}
            {gender === 'female' && (
              <Tooltip title="女">
                <WomanOutlined style={{ color: '#eb2f96', fontSize: '16px' }} />
              </Tooltip>
            )}
            {(!gender || gender === 'unknown') && (
              <Tooltip title="未知">
                <QuestionCircleOutlined style={{ color: '#999', fontSize: '16px' }} />
              </Tooltip>
            )}
          </div>
        );
      },
    }),
    // 手机号列
    columnHelper.accessor('phone', {
      id: 'phone',
      header: '手机号',
      size: 148, // 手机号列：148px (增加20px显示11位手机号)
      enableSorting: false,
      cell: ({ getValue }) => (
        <span style={{ color: '#666' }}>{getValue() || '-'}</span>
      ),
    }),
    columnHelper.accessor('role', {
      id: 'role',
      header: '角色',
      size: 60, // 角色列：60px (标签较短，减少15px)
      enableSorting: false,
      filterFn: 'equalsString',
      cell: ({ getValue }) => {
        const role = getValue() as 'admin' | 'user';
        const config = ROLE_CONFIG[role];
        return (
          <Tag color={config.color}>
            {config.label}
          </Tag>
        );
      },
    }),
    columnHelper.accessor('status', {
      id: 'status',
      header: '状态',
      size: 95, // 状态列：95px
      enableSorting: false,
      filterFn: 'equalsString',
      cell: ({ row }) => {
        const user = row.original;
        const isActive = user.status === 'active';
        return (
          <Space>
            <Switch
              size="small"
              checked={isActive}
              onChange={(checked) => handleStatusToggle(user.id, checked)}
            />
            <span style={{ color: isActive ? STATUS_CONFIG.active.color : STATUS_CONFIG.inactive.color }}>
              {getUserStatusText(user.status)}
            </span>
          </Space>
        );
      },
    }),
    columnHelper.accessor('createdAt', {
      id: 'createdAt',
      header: '注册时间',
      size: 140, // 注册时间列：140px (减少16px，仍能显示完整时间)
      enableSorting: true,
      filterFn: dateRangeFilter,
      cell: ({ getValue }) => formatDateTime(getValue()),
    }),
    columnHelper.accessor('lastLoginAt', {
      id: 'lastLoginAt',
      header: '最后登录',
      size: 140, // 最后登录列：140px (减少16px，仍能显示完整时间)
      enableSorting: false,
      cell: ({ getValue }) => {
        const lastLogin = getValue();
        return lastLogin ? (
          formatDateTime(lastLogin)
        ) : (
          <span style={{ color: '#999' }}>从未登录</span>
        );
      },
    }),
    columnHelper.display({
      id: 'actions',
      header: '操作',
      size: 80, // 操作列：80px (减少10px，两个图标按钮足够)
      cell: ({ row }) => {
        const user = row.original;
        return (
          <Space>
            <Tooltip title="编辑用户">
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditUser(user);
                }}
              />
            </Tooltip>
            <Tooltip title="删除用户">
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteUser(user);
                }}
              />
            </Tooltip>
          </Space>
        );
      },
    }),
  ], [
    selectedUserIds, 
    isAllSelected, 
    isIndeterminate, 
    toggleUserSelection, 
    toggleAllFilteredSelection, 
    handleStatusToggle, 
    handleEditUser, 
    handleDeleteUser, 
    getUserInitial
  ]);

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
    >
      <div className="user-management-refactored">
        {/* ProComponents QueryFilter 筛选区域 */}
        <QueryFilter
          defaultCollapsed
          split
          onFinish={(values) => {

            // 处理筛选逻辑
            if (values.username || values.email) {
              // 合并用户名和邮箱搜索
              const searchTerm = values.username || values.email || '';
              setGlobalFilter(searchTerm);
            }
            if (values.role) {
              setRoleFilter(values.role);
            }
            if (values.status) {
              setStatusFilter(values.status);
            }
            if (values.registerDateRange) {
              // 处理注册时间范围筛选 - 确保格式正确
              const [start, end] = values.registerDateRange;
              if (start && end) {
                setDateRange([dayjs(start), dayjs(end)]);
              } else {
                setDateRange(null);
              }

            }
          }}
          onReset={() => {
            setGlobalFilter('');
            resetUserFilters();
            resetFilters();
            clearSelection(); // 重置时清除选择
          }}
        >
          <ProFormText name="username" label="用户名" placeholder="请输入用户名" />
          <ProFormText name="email" label="邮箱" placeholder="请输入邮箱" />
          <ProFormSelect
            name="role"
            label="用户角色"
            placeholder="请选择角色"
            options={[
              { label: '管理员', value: 'admin' },
              { label: '普通用户', value: 'user' },
            ]}
          />
          <ProFormSelect
            name="status"
            label="用户状态"
            placeholder="请选择状态"
            options={[
              { label: '启用', value: 'active' },
              { label: '禁用', value: 'inactive' },
            ]}
          />
          <ProFormDateRangePicker
            name="registerDateRange"
            label="注册时间"
            placeholder={['开始日期', '结束日期']}
          />
        </QueryFilter>

        {/* 表格容器 - 包含操作区域的一体化设计 */}
        <div className="table-section">
          {/* 紧凑操作区域 - 作为表格的一部分 */}
          <div className="compact-action-section">
          {/* 左侧：操作按钮 */}
          <div className="action-buttons">
            <Space size={8}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddUser}
                size="small"
              >
                新建用户
              </Button>
              <Button
                icon={<DeleteOutlined />}
                size="small"
                disabled={selectedCount === 0}
                onClick={handleBatchDelete}
                danger
              >
                删除 {selectedCount > 0 && `(${selectedCount})`}
              </Button>
              <Button
                icon={<EditOutlined />}
                size="small"
                disabled
              >
                导出
              </Button>
            </Space>
          </div>

          {/* 右侧：统计信息 */}
          <div className="stats-summary">
            <Space size={16} className="summary-items">
              <span className="summary-item">
                <UserOutlined className="summary-icon" />
                总计: <strong>{userStats.total}</strong>
              </span>
              <span className="summary-item">
                <CheckCircleOutlined className="summary-icon enabled" />
                启用: <strong>{userStats.active}</strong>
              </span>
              <span className="summary-item">
                <ExclamationCircleOutlined className="summary-icon" />
                角色: <strong>{userStats.roles}种</strong>
              </span>
              {selectedCount > 0 && (
                <span className="summary-item">
                  <CheckCircleOutlined className="summary-icon" style={{ color: '#52c41a' }} />
                  已选择: <strong>{selectedCount}</strong>
                </span>
              )}
            </Space>
          </div>
          </div>

          {dataInitialized && (
            <TanStackTable
              data={filteredUsers}
              columns={columns}
              loading={loading}
              sorting={sorting}
              pagination={pagination}
              columnFilters={columnFilters}
              globalFilter={globalFilter}
              filterFns={{
                fuzzy: fuzzyFilter,
                dateRange: dateRangeFilter,
              }}
              config={{
                size: 'comfortable', // 使用 comfortable 预设 (表头55px, 行高56px)
                emptyStateHeight: 280,
                minRowsForDynamic: 10,
                pageSizeOptions: [10, 20, 50],
                defaultPageSize: 10,
                minTableWidth: 1215, // 列宽总和：50+50+135+105+215+60+128+75+95+156+156+90=1215
              }}
              events={{
                onSortingChange: setSorting,
                onPageChange: (pageIndex, pageSize) => {
                  setPagination({ pageIndex, pageSize });
                },
                onColumnFiltersChange: setColumnFilters,
                onGlobalFilterChange: setGlobalFilter,
                onRowClick: handleRowClick,
              }}
              showPagination={true}
              className="refactored-table"
              style={{
                border: 'none',
                borderRadius: '0',
                boxShadow: 'none',
              }}
            />
          )}
        </div>

        {/* 用户创建表单 */}
        <UserCreateForm
          visible={showCreateForm}
          loading={createFormLoading}
          onSubmit={handleCreateUser}
          onCancel={handleCreateFormCancel}
        />
      </div>
    </motion.div>
  );
};

export default React.memo(UserManagementRefactored);