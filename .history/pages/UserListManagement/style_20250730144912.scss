@use '../../styles/variables' as *;

.user-management-refactored {
  padding: 0;
  // background: var(--theme-bg-secondary);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: $spacing-md;
  transition: background-color 0.2s ease;

  // 页面标题
  .page-header {
    margin-bottom: $spacing-md; // 统一间距：16px
    padding-bottom: $spacing-md; // 统一间距：16px
    border-bottom: 1px solid var(--theme-border-color-split);
    transition: border-color 0.2s ease;

    h1 {
      margin: 0 0 $spacing-md 0; // 标题下方间距：16px
      font-size: 24px;
      font-weight: 600;
      color: var(--theme-text-primary);
      transition: color 0.2s ease;

      // 手机端字体大小优化
      @media (max-width: 768px) {
        font-size: 20px;
      }
    }

    p {
      margin: 0;
      color: var(--theme-text-secondary);
      font-size: 14px;
      transition: color 0.2s ease;
    }
  }

  // ProComponents QueryFilter 样式
  .ant-pro-query-filter {
    border: 1px solid var(--theme-border-color-split);
    border-radius: $border-radius-base;
    background: var(--theme-bg-primary);
    transition: background-color 0.2s ease, border-color 0.2s ease;
  }

  // 表格容器 - 一体化设计（参考模型页面）
  .table-section {
    border: 1px solid var(--theme-border-color-split);
    border-radius: 8px;
    background: var(--theme-bg-primary);
    box-shadow: var(--theme-shadow-1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;

    // 紧凑操作区域样式 - 作为表格顶部
    .compact-action-section {
      // padding: $spacing-md; // 统一内边距：16px
      // padding 上下边距 16px 左边16 右边20
      padding: $spacing-md  20px;
      background: var(--theme-bg-tertiary);
      border-bottom: 1px solid var(--theme-border-color-split);
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: $spacing-md; // 统一间距：16px
      transition: background-color 0.2s ease, border-color 0.2s ease;

      // 手机端布局优化
      @media (max-width: 768px) {
        flex-direction: column; // 手机端垂直布局
        align-items: stretch;
      }

    .action-buttons {
      flex-shrink: 0;
      display: flex;
      gap: $spacing-md; // 按钮间距：16px

      // 手机端优化
      @media (max-width: 768px) {
        flex-wrap: wrap;
      }

      // 统一按钮样式 - 保持原生 Ant Design 动画效果
      .ant-btn {
        // 统一高度和基础样式
        height: 32px;
        border-radius: $border-radius-base;
        font-size: 14px;
        font-weight: 500;

        // 确保所有按钮变体都有统一高度
        &.ant-btn-sm {
          height: 32px;
        }

        &.ant-btn-lg {
          height: 32px;
        }

        // 保持原生动画，不添加自定义 transition 或 transform
        // 让 Ant Design 处理所有动画效果（包括波纹动画）
      }

    }
    }

    .stats-summary {
      flex-shrink: 0;

      .summary-items {
        display: flex;
        gap: $spacing-md; // 统计项间距：16px

        // 手机端优化
        @media (max-width: 768px) {
          flex-direction: column;
        }

        .summary-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 13px;
          color: var(--theme-text-secondary);
          transition: color 0.2s ease;

          .summary-icon {
            font-size: 14px;
            color: var(--theme-text-tertiary);
            transition: color 0.2s ease;

            &.enabled {
              color: $success-color;
            }
          }

          strong {
            color: var(--theme-text-primary);
            font-weight: 600;
            transition: color 0.2s ease;
          }
        }
      }
    }

    // 表格容器内的基本样式（保留有效的样式）
    .tanstack-table {
      // 表格容器样式调整
      .table-container {
        background: transparent;
      }

      // 页面级 padding 覆盖 - 只影响当前页面
      .data-table {
        thead th {
          padding: 0 12px !important; // 覆盖全局 16px padding
        }

        tbody td {
          padding: 0 12px !important; // 覆盖全局 16px padding
        }
      }

      // 表格特定样式
      table {
        // 选择列样式
        td:first-child,
        th:first-child {
          text-align: center;
          padding-left: 8px;
          padding-right: 8px;
          width: 50px;
          min-width: 50px;
          max-width: 50px;
        }

        // 头像列样式
        td:nth-child(2),
        th:nth-child(2) {
          text-align: center;
          padding-left: 6px;
          padding-right: 6px;
          width: 50px;
          min-width: 50px;
          max-width: 50px;
        }

        // 用户名列样式
        td:nth-child(3),
        th:nth-child(3) {
          text-align: left;
          width: 135px;
          min-width: 135px;
          max-width: 135px;
        }

        // 昵称列样式
        td:nth-child(4),
        th:nth-child(4) {
          text-align: left;
          width: 105px;
          min-width: 105px;
          max-width: 105px;
        }

        // 邮箱列样式
        td:nth-child(5),
        th:nth-child(5) {
          text-align: left;
          width: 215px;
          min-width: 215px;
          max-width: 215px;
        }

        // 性别列样式（图标显示）
        td:nth-child(6),
        th:nth-child(6) {
          text-align: center;
          width: 60px;
          min-width: 60px;
          max-width: 60px;
        }

        // 手机号列样式
        td:nth-child(7),
        th:nth-child(7) {
          text-align: left;
          width: 128px;
          min-width: 128px;
          max-width: 128px;
        }

        // 角色列样式
        td:nth-child(8),
        th:nth-child(8) {
          text-align: center;
          width: 75px;
          min-width: 75px;
          max-width: 75px;
        }

        // 状态列样式
        td:nth-child(9),
        th:nth-child(9) {
          text-align: center;
          width: 95px;
          min-width: 95px;
          max-width: 95px;
        }

        // 注册时间列样式
        td:nth-child(10),
        th:nth-child(10) {
          text-align: center;
          width: 156px;
          min-width: 156px;
          max-width: 156px;
        }

        // 最后登录列样式
        td:nth-child(11),
        th:nth-child(11) {
          text-align: center;
          width: 156px;
          min-width: 156px;
          max-width: 156px;
        }

        // 操作列样式
        td:nth-child(12),
        th:nth-child(12) {
          text-align: center;
          width: 90px;
          min-width: 90px;
          max-width: 90px;
        }

        // // 添加表头居中样式 - 仅影响当前页面
        // thead th .header-content {
        //   justify-content: center !important;
        // }
       // 表头第一个需要居中
       thead th:first-child .header-content {
          justify-content: center !important;
        } 
        // 表头第二个需要居中
        // thead th:nth-child(2) .header-content {
        //   justify-content: center !important;
        // } 

        // 复选框样式调整
        .ant-checkbox-wrapper {
          display: flex;
          justify-content: center;
          margin: 0;
        }

        // 头像容器样式
        .avatar-container {
          display: flex;
          justify-content: left;
          align-items: center;
        }

        // 批量选择行高亮效果
        tr.selected {
          background-color: var(--theme-primary-color-bg) !important;
        }

        // 头像悬停效果
        .ant-avatar {
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.05);
          }
        }

        // 性别图标样式优化
        .anticon-man,
        .anticon-woman,
        .anticon-question-circle {
          transition: all 0.2s ease;
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
            opacity: 0.8;
          }
        }
      }
    }
  }



  // 移除响应式表格优化 - 使用固定列宽和水平滚动
}

// ===== 统一间距系统 =====
// 注意：所有间距使用统一的 $spacing-md (16px) 标准
// 简化设计系统，提高一致性，减少维护复杂度
