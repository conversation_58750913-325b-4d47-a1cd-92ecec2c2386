@use '../../styles/variables' as *;

// ===== 模型管理页面主样式 =====
.model-management-v2-pro {
  padding: 0;
  display: flex;
  flex-direction: column;

  // ===== 统一容器样式 =====
  .unified-container {
    // background: #fff;
    border-radius: $border-radius-base;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    overflow: visible;

    // ===== 现代标签页设计 =====
    .modern-tabs.ant-tabs {
      background: transparent;
      border: none;
      margin: 0;

      .ant-tabs-nav {
        background: transparent;
        border-bottom: 1px solid #e5e7eb;
        margin: 0;
        padding: 0 20px;

        .ant-tabs-nav-wrap {
          .ant-tabs-nav-list {
            .ant-tabs-tab {
              position: relative;
              padding: 0;
              margin: 0;
              border: none;
              background: transparent;

              .tab-trigger {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 16px 20px;
                font-weight: 500;
                color: #6b7280;
                transition: all 0.3s ease;
                border-radius: 0;
                position: relative;

                .anticon {
                  font-size: 16px;
                }
              }

              &:hover .tab-trigger {
                color: #1890ff;
                background: transparent;
              }

              &.ant-tabs-tab-active .tab-trigger {
                color: #1890ff;
                background: transparent;
                border-radius: 8px 8px 0 0;
                box-shadow: none;

                &::after {
                  content: '';
                  position: absolute;
                  bottom: -1px;
                  left: 0;
                  right: 0;
                  height: 2px;
                  background: #1890ff;
                }
              }
            }
          }
        }
      }

      .ant-tabs-content-holder {
        padding: 0;
        background: transparent;
        overflow: visible;
      }

      .ant-tabs-tabpane {
        padding: 0;
        background: transparent;
        overflow: visible;

        // ===== 标签页内容区域 =====
        .tab-content {
          display: flex;
          flex-direction: column;
          overflow: hidden;

          // ===== 紧凑操作区域设计 =====
          .compact-action-section {
            padding: 12px 20px;
            background: #fafbfc;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;

            .stats-summary {
              flex: 1;

              .summary-items {
                display: flex;
                gap: 24px;

                .summary-item {
                  display: flex;
                  align-items: center;
                  gap: 6px;
                  font-size: 13px;
                  color: #6b7280;

                  .summary-icon {
                    font-size: 14px;

                    &.enabled {
                      color: #10b981;
                    }
                  }

                  strong {
                    color: #374151;
                    font-weight: 600;
                  }
                }
              }
            }

            .action-controls {
              display: flex;
              align-items: center;
              gap: 8px;
              flex-shrink: 0;

              .compact-search {
                height: 32px;
                border-radius: $border-radius-base;
                border: 1px solid #d1d5db;
                background: #fff;
                transition: all 0.3s ease;

                &:hover {
                  border-color: #9ca3af;
                }

                &:focus-within {
                  border-color: #1890ff;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
                }

                .ant-input {
                  font-size: 13px;
                  color: #374151;

                  &::placeholder {
                    color: #9ca3af;
                  }
                }

                .anticon {
                  color: #9ca3af;
                  font-size: 12px;
                }
              }

              .ant-select {
                height: 32px;

                .ant-select-selector {
                  height: 32px !important;
                  border-radius: $border-radius-base;
                  border: 1px solid #d1d5db;
                  font-size: 13px;

                  .ant-select-selection-item {
                    line-height: 30px;
                  }

                  .ant-select-selection-placeholder {
                    line-height: 30px;
                    color: #9ca3af;
                  }
                }

                &:hover .ant-select-selector {
                  border-color: #9ca3af;
                }

                &.ant-select-focused .ant-select-selector {
                  border-color: #1890ff !important;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1) !important;
                }
              }

              .ant-btn {
                height: 32px;
                border-radius: $border-radius-base;
                font-size: 14px;
                font-weight: 500;

                &.ant-btn-sm {
                  height: 32px;
                }

                &.ant-btn-lg {
                  height: 32px;
                }

                &.ant-btn-icon-only {
                  width: 32px;
                  height: 32px;
                }
              }
            }
          }

          // ===== 表格区域统一设计 =====
          .table-section {
            background: transparent;
            overflow: visible;

            .tanstack-table {
              background: transparent !important;
              border: none !important;
              box-shadow: none !important;
              border-radius: 0;

              .pagination-container {
                background: transparent !important;
              }

              .table-container {
                overflow: visible !important;

                .table-wrapper {
                  overflow: visible !important;

                  .table-scroll-container {
                    overflow-x: auto !important;
                    overflow-y: auto !important;
                    min-width: 100% !important;
                    scroll-behavior: auto !important;
                    will-change: scroll-position;

                    .data-table {
                      min-width: 1200px !important;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // ===== 统一按钮样式 =====
  .ant-btn {
    height: 32px;
    border-radius: $border-radius-base;
    font-size: 14px;
    font-weight: 500;

    &.ant-btn-sm {
      height: 32px;
    }

    &.ant-btn-lg {
      height: 32px;
    }

    &.ant-btn-text {
      height: auto;
      padding: 4px 8px;
    }
  }
}

// ===== 表格特定样式增强 =====
.model-management-table,
.api-source-management-table {
  .table-container {
    overflow: visible !important;

    .table-wrapper {
      overflow: visible !important;

      .table-scroll-container {
        overflow-x: auto !important;
        overflow-y: auto !important;
        scroll-behavior: auto !important;
        will-change: scroll-position;

        &::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background: #a8a8a8;
          }
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 4px;
        }
      }
    }
  }
}