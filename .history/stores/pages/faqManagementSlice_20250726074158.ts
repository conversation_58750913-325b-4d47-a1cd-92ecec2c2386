/**
 * FAQ 管理状态管理 Slice
 * 管理 FAQ 建议的增删改查、状态管理等
 */

import { StateCreator } from 'zustand';
import type {
  FAQSuggestion,
  CreateFAQSuggestionRequest,
  FAQQueryParams,
  FAQPaginatedResponse,
  FAQStats,
} from '../../types/faq';

// FAQ 管理状态接口
export interface FAQManagementState {
  // FAQ 数据
  suggestions: FAQSuggestion[];
  currentSuggestion: FAQSuggestion | null;
  stats: FAQStats | null;
  
  // 分页信息
  faqPagination: {
    current: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 查询参数
  queryParams: FAQQueryParams;
  
  // UI 状态
  loading: boolean;
  saving: boolean;
  deleting: boolean;
  error: string | null;
  
  // 模态框状态
  modalVisible: boolean;
  editingItem: FAQSuggestion | null;
  
  // 选择状态
  selectedRowKeys: string[];
  
  // 缓存和持久化
  lastUpdated: number | null;
  isDirty: boolean;
  
  // FAQ 操作方法
  loadSuggestions: (params?: FAQQueryParams) => Promise<void>;
  loadSuggestionById: (id: string) => Promise<void>;
  createSuggestion: (data: CreateFAQSuggestionRequest) => Promise<void>;
  updateSuggestion: (id: string, data: Partial<CreateFAQSuggestionRequest>) => Promise<void>;
  deleteSuggestion: (id: string) => Promise<void>;
  batchDeleteSuggestions: (ids: string[]) => Promise<void>;
  updateSuggestionStatus: (id: string, status: 'active' | 'inactive' | 'draft') => Promise<void>;
  updateSuggestionOrder: (id: string, order: number) => Promise<void>;
  loadStats: () => Promise<void>;
  
  // UI 状态管理
  setLoading: (loading: boolean) => void;
  setSaving: (saving: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  setError: (error: string | null) => void;
  setModalVisible: (visible: boolean) => void;
  setEditingItem: (item: FAQSuggestion | null) => void;
  setSelectedRowKeys: (keys: string[]) => void;
  setQueryParams: (params: Partial<FAQQueryParams>) => void;
  setPagination: (pagination: Partial<FAQManagementState['faqPagination']>) => void;
  
  // 重置和清理
  resetFAQManagement: () => void;
  markDirty: (dirty: boolean) => void;
}

// 默认查询参数
const DEFAULT_QUERY_PARAMS: FAQQueryParams = {
  page: 1,
  pageSize: 10,
  sortBy: 'order',
  sortOrder: 'asc',
};

// 默认分页信息
const DEFAULT_PAGINATION = {
  current: 1,
  pageSize: 10,
  total: 0,
  totalPages: 0,
};

// 存储键
const STORAGE_KEY = 'v2-admin-faq-management';

/**
 * 从 localStorage 加载缓存
 */
const loadFromCache = (): { suggestions: FAQSuggestion[]; lastUpdated: number } | null => {
  try {
    const cached = localStorage.getItem(STORAGE_KEY);
    if (cached) {
      const { suggestions, lastUpdated } = JSON.parse(cached);
      // 检查缓存是否过期（1小时）
      if (Date.now() - lastUpdated < 60 * 60 * 1000) {
        return { suggestions, lastUpdated };
      }
    }
  } catch (error) {
    console.warn('Failed to load FAQ management data from cache:', error);
  }
  return null;
};

/**
 * 保存到 localStorage
 */
const saveToCache = (suggestions: FAQSuggestion[]) => {
  try {
    const cacheData = {
      suggestions,
      lastUpdated: Date.now(),
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(cacheData));
  } catch (error) {
    console.warn('Failed to save FAQ management data to cache:', error);
  }
};

/**
 * FAQ 管理 Slice 创建器
 */
export const createFAQManagementSlice: StateCreator<
  FAQManagementState,
  [],
  [],
  FAQManagementState
> = (set, get) => ({
  // 初始状态
  suggestions: [],
  currentSuggestion: null,
  stats: null,
  faqPagination: { ...DEFAULT_PAGINATION },
  queryParams: { ...DEFAULT_QUERY_PARAMS },
  loading: false,
  saving: false,
  deleting: false,
  error: null,
  modalVisible: false,
  editingItem: null,
  selectedRowKeys: [],
  lastUpdated: null,
  isDirty: false,

  /**
   * 加载 FAQ 建议列表
   */
  loadSuggestions: async (params?: FAQQueryParams) => {
    // 先尝试从缓存加载
    const cached = loadFromCache();
    if (cached && !params) {
      set({
        suggestions: cached.suggestions,
        lastUpdated: cached.lastUpdated,
        isDirty: false,
        error: null,
      });
      console.log('✅ FAQ suggestions loaded from cache');
      return;
    }

    try {
      set({ loading: true, error: null });

      const queryParams = { ...get().queryParams, ...params };
      set({ queryParams });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      const response: FAQPaginatedResponse = await faqService.faqService.getAllSuggestions(queryParams);

      set({
        suggestions: response.data,
        faqPagination: {
          current: response.page,
          pageSize: response.pageSize,
          total: response.total,
          totalPages: response.totalPages,
        },
        lastUpdated: Date.now(),
        isDirty: false,
        error: null,
      });

      // 保存到缓存
      saveToCache(response.data);
    } catch (error: any) {
      console.error('Failed to load FAQ suggestions:', error);
      set({ error: error.message || '加载 FAQ 建议失败' });
    } finally {
      set({ loading: false });
    }
  },

  /**
   * 根据 ID 加载 FAQ 建议
   */
  loadSuggestionById: async (id: string) => {
    try {
      set({ loading: true, error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      const suggestion = await faqService.faqService.getSuggestionById(id);

      set({
        currentSuggestion: suggestion,
        error: null,
      });
    } catch (error: any) {
      console.error('Failed to load FAQ suggestion by ID:', error);
      set({ error: error.message || '加载 FAQ 建议详情失败' });
    } finally {
      set({ loading: false });
    }
  },

  /**
   * 创建 FAQ 建议
   */
  createSuggestion: async (data: CreateFAQSuggestionRequest) => {
    try {
      set({ saving: true, error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.createSuggestion(data);

      set({
        saving: false,
        modalVisible: false,
        editingItem: null,
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('FAQ 建议创建成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to create FAQ suggestion:', error);
      set({
        saving: false,
        error: error.message || '创建 FAQ 建议失败',
      });
    }
  },

  /**
   * 更新 FAQ 建议
   */
  updateSuggestion: async (id: string, data: Partial<CreateFAQSuggestionRequest>) => {
    try {
      set({ saving: true, error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.updateSuggestion(id, data);

      set({
        saving: false,
        modalVisible: false,
        editingItem: null,
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('FAQ 建议更新成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion:', error);
      set({
        saving: false,
        error: error.message || '更新 FAQ 建议失败',
      });
    }
  },

  /**
   * 删除 FAQ 建议
   */
  deleteSuggestion: async (id: string) => {
    try {
      set({ deleting: true, error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.deleteSuggestion(id);

      set({
        deleting: false,
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('FAQ 建议删除成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to delete FAQ suggestion:', error);
      set({
        deleting: false,
        error: error.message || '删除 FAQ 建议失败',
      });
    }
  },

  /**
   * 批量删除 FAQ 建议
   */
  batchDeleteSuggestions: async (ids: string[]) => {
    try {
      set({ deleting: true, error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.batchDeleteSuggestions(ids);

      set({
        deleting: false,
        selectedRowKeys: [],
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success(`成功删除 ${ids.length} 个 FAQ 建议`);
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to batch delete FAQ suggestions:', error);
      set({
        deleting: false,
        error: error.message || '批量删除 FAQ 建议失败',
      });
    }
  },

  /**
   * 更新 FAQ 建议状态
   */
  updateSuggestionStatus: async (id: string, status: 'active' | 'inactive' | 'draft') => {
    try {
      set({ error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.updateSuggestionStatus(id, status);

      set({
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();

      // 显示成功消息
      try {
        const { message } = await import('antd');
        message.success('FAQ 建议状态更新成功');
      } catch (importError) {
        // 静默处理导入错误
      }
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion status:', error);
      set({ error: error.message || '更新 FAQ 建议状态失败' });
    }
  },

  /**
   * 更新 FAQ 建议排序
   */
  updateSuggestionOrder: async (id: string, order: number) => {
    try {
      set({ error: null });

      // 动态导入服务
      const faqService = await import('../../services/faqService');
      await faqService.faqService.updateSuggestionOrder(id, order);

      set({
        isDirty: true,
        error: null,
      });

      // 重新加载列表
      await get().loadSuggestions();
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion order:', error);
      set({ error: error.message || '更新 FAQ 建议排序失败' });
    }
  },

  /**
   * 加载统计信息
   */
  loadStats: async () => {
    try {
      // 动态导入服务
      const faqService = await import('../../services/faqService');
      const stats = await faqService.faqService.getStats();

      set({
        stats,
        error: null,
      });
    } catch (error: any) {
      console.error('Failed to load FAQ stats:', error);
      set({ error: error.message || '加载 FAQ 统计信息失败' });
    }
  },

  // UI 状态管理方法
  setLoading: (loading: boolean) => set({ loading }),
  setSaving: (saving: boolean) => set({ saving }),
  setDeleting: (deleting: boolean) => set({ deleting }),
  setError: (error: string | null) => set({ error }),
  setModalVisible: (modalVisible: boolean) => set({ modalVisible }),
  setEditingItem: (editingItem: FAQSuggestion | null) => set({ editingItem }),
  setSelectedRowKeys: (selectedRowKeys: string[]) => set({ selectedRowKeys }),
  setQueryParams: (params: Partial<FAQQueryParams>) => {
    const currentParams = get().queryParams;
    set({ queryParams: { ...currentParams, ...params } });
  },
  setPagination: (pagination: Partial<FAQManagementState['pagination']>) => {
    const currentPagination = get().pagination;
    set({ pagination: { ...currentPagination, ...pagination } });
  },

  /**
   * 标记是否有未保存的更改
   */
  markDirty: (isDirty: boolean) => set({ isDirty }),

  /**
   * 重置 FAQ 管理状态
   */
  resetFAQManagement: () => {
    set({
      suggestions: [],
      currentSuggestion: null,
      stats: null,
      pagination: { ...DEFAULT_PAGINATION },
      queryParams: { ...DEFAULT_QUERY_PARAMS },
      loading: false,
      saving: false,
      deleting: false,
      error: null,
      modalVisible: false,
      editingItem: null,
      selectedRowKeys: [],
      lastUpdated: null,
      isDirty: false,
    });

    // 清除缓存
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear FAQ management cache:', error);
    }
  },
});
