/**
 * Zustand Store 类型定义
 * 统一管理所有状态类型
 */

// 导入各个切片的类型
import type { SystemSettingsState } from './settings/systemSettingsSlice';
import type { CaptchaSettingsState } from './settings/captchaSettingsSlice';
import type { TitleGenerationSettingsState } from './settings/titleGenerationSettingsSlice';
import type { FileMiddlewareSettingsState } from './settings/fileMiddlewareSettingsSlice';
import type { FAQManagementState } from './pages/faqManagementSlice';
import type { UserCreateFormState, UserCreateFormActions } from './components/userCreateFormSlice';
import type { TanStackTableState, TanStackTableActions } from './components/tanStackTableSlice';
import type { LoginLogsState, LoginLogsActions } from './pages/loginLogsSlice';
import type { OperationLogsState, OperationLogsActions } from './pages/operationLogsSlice';
import type { SystemLogsState, SystemLogsActions } from './pages/systemLogsSlice';
import type { OnlineUsersState, OnlineUsersActions } from './pages/onlineUsersSlice';
import type { ModelsManagementState, ModelsManagementActions } from './pages/modelsManagementSlice';
import type { AssistantManagementState, AssistantManagementActions } from './pages/assistantManagementSlice';
import type { KnowledgeBaseManagementState, KnowledgeBaseManagementActions } from './pages/knowledgeBaseManagementSlice';
// ModelCreateFormState 和 ModelCreateFormActions 在此处定义，因为它们在 modelCreateFormSlice.ts 中没有导出

// ==================== 标签页相关类型 ====================

export interface TabRecord {
  id: string;
  path: string;
  title: string;
  icon?: string;
  description?: string;
  pinned: boolean;
  timestamp: number;
  loading?: boolean;
}

export interface TabsState {
  tabs: TabRecord[];
  isLoading: boolean;
  maxTabs: number;
  storageKey: string;
  excludePaths: string[];
  defaultPinnedPaths: string[];
}

export interface TabsActions {
  // 基础操作
  loadTabs: () => void;
  saveTabs: (tabs: TabRecord[]) => void;
  addTab: (path: string) => void;
  removeTab: (tabId: string) => void;
  
  // 标签页管理
  pinTab: (tabId: string, pinned: boolean) => void;
  refreshTab: (tabId: string, navigate: (path: string) => void, currentPath: string) => void;
  clearOtherTabs: (keepTabId: string, navigate?: (path: string) => void) => void;
  clearAllTabs: (navigate?: (path: string) => void) => void;
  
  // 工具方法
  getActiveTab: (currentPath: string) => TabRecord | undefined;
  getTabById: (tabId: string) => TabRecord | undefined;
  getTabByPath: (path: string) => TabRecord | undefined;
}

// ==================== 布局相关类型 ====================

export interface LayoutState {
  // 侧边栏状态
  sidebarCollapsed: boolean;
  sidebarWidth: number;
  sidebarCollapsedWidth: number;
  
  // 主题相关
  theme: 'light' | 'dark';
  
  // 响应式相关
  isMobile: boolean;
  currentBreakpoint: string;
  mobileDrawerOpen: boolean;
  
  // 布局配置
  headerHeight: number;
  contentPadding: number;
  fixedHeader: boolean;
  fixedSidebar: boolean;
}

export interface LayoutActions {
  // 侧边栏操作
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // 主题操作
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  
  // 移动端操作
  toggleMobileDrawer: () => void;
  setMobileDrawerOpen: (open: boolean) => void;
  
  // 响应式更新
  updateResponsive: (isMobile: boolean, breakpoint: string) => void;
  
  // 布局配置
  updateLayoutConfig: (config: Partial<LayoutState>) => void;
}

// ==================== 响应式相关类型 ====================

export interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  currentBreakpoint: string;
  screenWidth: number;
  screenHeight: number;
}

export interface ResponsiveActions {
  updateScreenSize: (width: number, height: number) => void;
  updateBreakpoint: (breakpoint: string) => void;
}

// ==================== 路由历史相关类型 ====================

export interface RouteHistoryRecord {
  path: string;
  title: string;
  timestamp: number;
  params?: Record<string, string>;
  query?: Record<string, string>;
}

export interface RouteHistoryState {
  history: RouteHistoryRecord[];
  maxHistory: number;
  currentPath: string;
}

export interface RouteHistoryActions {
  addRoute: (route: Omit<RouteHistoryRecord, 'timestamp'>) => void;
  removeRoute: (path: string) => void;
  clearHistory: () => void;
  setCurrentPath: (path: string) => void;
  getRecentRoutes: (limit?: number) => RouteHistoryRecord[];
}

// ==================== 最近访问相关类型 ====================

export interface RecentVisitRecord {
  id: string;
  path: string;
  title: string;
  icon?: string;
  timestamp: number;
  visitCount: number;
}

export interface RecentVisitsState {
  visits: RecentVisitRecord[];
  maxVisits: number;
  storageKey: string;
}

export interface RecentVisitsActions {
  addVisit: (path: string, title: string, icon?: string) => void;
  removeVisit: (id: string) => void;
  clearVisits: () => void;
  getRecentVisits: (limit?: number) => RecentVisitRecord[];
  getMostVisited: (limit?: number) => RecentVisitRecord[];
}

// ==================== 模型创建表单相关类型 ====================

export interface ModelCreateFormState {
  /**
   * 基础状态
   */
  visible: boolean;
  loading: boolean;
  saving: boolean;
  error: string | null;

  /**
   * 表单数据
   */
  parameters: Array<{ name: string; value: string }>;
  apiSources: any[]; // 使用 any[] 而不是 ApiSource[] 以避免循环依赖
  loadingApiSources: boolean;
  generatedId: string | null;

  /**
   * 缓存和优化
   */
  lastUpdated: number;
  isDirty: boolean;
}

export interface ModelCreateFormActions {
  // 模态框控制
  setVisible: (visible: boolean) => void;

  // 参数管理
  setParameters: (parameters: Array<{ name: string; value: string }>) => void;
  addParameter: () => void;
  removeParameter: (index: number) => void;
  updateParameter: (index: number, field: 'name' | 'value', value: string) => void;

  // API 源管理
  fetchApiSources: () => Promise<void>;
  setApiSources: (sources: any[]) => void;

  // ID 生成
  generateId: () => string;

  // 表单操作
  createModel: (modelData: any) => Promise<void>;
  resetForm: () => void;

  // 错误处理
  setError: (error: string | null) => void;
  clearError: () => void;

  // 状态管理
  markDirty: (dirty: boolean) => void;
  
  /**
   * 表单操作
   */
  setVisible: (visible: boolean) => void;
  setFormVisible: (visible: boolean) => void;
  resetForm: () => void;
  resetFormState: () => void;
  
  /**
   * 错误处理
   */
  setError: (error: string | null) => void;
  clearError: () => void;
  
  /**
   * 状态管理
   */
  markDirty: (dirty: boolean) => void;
}

// ==================== 组合类型 ====================

export interface AppState extends TabsState, LayoutState, ResponsiveState, RouteHistoryState, RecentVisitsState {}

export interface AppActions extends TabsActions, LayoutActions, ResponsiveActions, RouteHistoryActions, RecentVisitsActions {}

// Use intersection types to combine all slices and resolve conflicts
export type AppStore = 
  AppState & 
  AppActions & 
  SystemSettingsState & 
  CaptchaSettingsState & 
  TitleGenerationSettingsState & 
  FileMiddlewareSettingsState & 
  FAQManagementState & 
  UserCreateFormState & 
  UserCreateFormActions & 
  ModelCreateFormState & 
  ModelCreateFormActions & 
  TanStackTableState & 
  TanStackTableActions & 
  LoginLogsState & 
  LoginLogsActions & 
  OperationLogsState & 
  OperationLogsActions & 
  SystemLogsState & 
  SystemLogsActions & 
  OnlineUsersState & 
  OnlineUsersActions & 
  ModelsManagementState & 
  ModelsManagementActions & 
  AssistantManagementState & 
  AssistantManagementActions &
  KnowledgeBaseManagementState &
  KnowledgeBaseManagementActions;

// ==================== 配置类型 ====================

export interface StoreConfig {
  // 持久化配置
  enablePersistence: boolean;
  persistenceKeys: (keyof AppState)[];
  
  // 调试配置
  enableDevtools: boolean;
  devtoolsName: string;
}

// ==================== 工具类型 ====================

export type StateSlice<T> = (
  set: (partial: T | Partial<T> | ((state: T) => T | Partial<T>), replace?: boolean) => void,
  get: () => T
) => T;

export type StoreSlice<T, U> = (
  set: (partial: U | Partial<U> | ((state: U) => U | Partial<U>), replace?: boolean) => void,
  get: () => U
) => T;
