import { StateCreator } from 'zustand';
import { themeTransitionMonitor } from '../../utils/performanceMonitor';

// 主题类型定义 - 简化为只支持light和dark
export type ThemeMode = 'light' | 'dark';

// 主题状态接口
export interface ThemeState {
  // 当前主题模式
  theme: ThemeMode;
  // 是否正在切换主题（用于动画效果）
  isTransitioning: boolean;
  // 过渡阶段：'idle' | 'preparing' | 'animating' | 'completing'
  transitionPhase: 'idle' | 'preparing' | 'animating' | 'completing';
  // 过渡开始时间戳
  transitionStartTime: number;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题模式
  setTheme: (theme: ThemeMode) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 初始化主题
  initializeTheme: () => void;
  // 设置过渡阶段
  setTransitionPhase: (phase: 'idle' | 'preparing' | 'animating' | 'completing') => void;
  // 开始主题切换动画
  startThemeTransition: (newTheme: ThemeMode) => Promise<void>;
}

// 从 localStorage 获取保存的主题模式
const getSavedTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  const saved = localStorage.getItem('v2-admin-theme');
  return (saved === 'dark' || saved === 'light') ? saved : 'light';
};

// 应用主题到DOM
const applyThemeToDOM = (theme: ThemeMode) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
  }
};

// 默认状态
const defaultThemeState: ThemeState = {
  theme: getSavedTheme(),
  isTransitioning: false,
  transitionPhase: 'idle',
  transitionStartTime: 0,
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get, api) => ({
  ...defaultThemeState,

  setTheme: (theme: ThemeMode) => {
    // 直接设置主题，不触发动画（用于初始化等场景）
    set({ theme });

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('v2-admin-theme', theme);
    }

    // 应用到DOM
    applyThemeToDOM(theme);
  },

  setTransitionPhase: (phase: 'idle' | 'preparing' | 'animating' | 'completing') => {
    set({ transitionPhase: phase });
  },

  startThemeTransition: async (newTheme: ThemeMode) => {
    const startTime = Date.now();

    // 开始性能监控
    if (process.env.NODE_ENV === 'development') {
      themeTransitionMonitor.startMonitoring();
    }

    // 优化：减少状态更新次数，批量处理
    set({
      isTransitioning: true,
      transitionPhase: 'animating',
      transitionStartTime: startTime,
      theme: newTheme  // 同时更新主题
    });

    // 添加全局过渡类
    if (typeof document !== 'undefined') {
      document.documentElement.classList.add('theme-transitioning');
    }

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('v2-admin-theme', newTheme);
    }

    // 应用到DOM
    applyThemeToDOM(newTheme);

    // 等待过渡动画完成（400ms，优化后的时长）
    await new Promise(resolve => setTimeout(resolve, 400));

    // 批量清理：一次性完成所有清理工作
    if (typeof document !== 'undefined') {
      document.documentElement.classList.remove('theme-transitioning');
    }

    // 最终状态重置
    set({
      isTransitioning: false,
      transitionPhase: 'idle',
      transitionStartTime: 0
    });

    // 结束性能监控并生成报告
    if (process.env.NODE_ENV === 'development') {
      const metrics = themeTransitionMonitor.endMonitoring();
      if (metrics) {
        console.log('🔍 Theme Transition Performance Analysis:', {
          duration: `${metrics.duration.toFixed(2)}ms`,
          memoryDelta: `${(metrics.memoryUsage?.delta || 0) / 1024}KB`,
          transitioningElements: metrics.domMetrics?.transitioningElements
        });

        // 分析CSS过渡性能
        const cssAnalysis = themeTransitionMonitor.analyzeCSSTransitions();
        if (cssAnalysis.recommendations.length > 0) {
          console.warn('⚠️ Performance Recommendations:', cssAnalysis.recommendations);
        }
      }
    }
  },

  toggleTheme: () => {
    const { theme, isTransitioning } = get();

    // 如果正在过渡中，忽略新的切换请求
    if (isTransitioning) return;

    const newTheme = theme === 'light' ? 'dark' : 'light';
    get().startThemeTransition(newTheme);
  },

  initializeTheme: () => {
    const savedTheme = getSavedTheme();
    set({ theme: savedTheme });
    applyThemeToDOM(savedTheme);
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;
