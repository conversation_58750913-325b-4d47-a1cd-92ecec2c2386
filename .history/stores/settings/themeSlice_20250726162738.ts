import { StateCreator } from 'zustand';

// 主题类型定义 - 简化为只支持light和dark
export type ThemeMode = 'light' | 'dark';

// 主题状态接口
export interface ThemeState {
  // 当前主题模式
  theme: ThemeMode;
  // 是否正在切换主题（用于动画效果）
  isTransitioning: boolean;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题模式
  setThemeMode: (mode: ThemeMode) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 设置实际主题
  setActualTheme: (theme: 'light' | 'dark') => void;
  // 设置过渡状态
  setTransitioning: (isTransitioning: boolean) => void;
  // 更新系统主题
  updateSystemTheme: (systemTheme: 'light' | 'dark') => void;
  // 初始化主题（检测系统主题并应用）
  initializeTheme: () => void;
}

// 检测系统主题偏好
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 从 localStorage 获取保存的主题模式
const getSavedThemeMode = (): ThemeMode => {
  if (typeof window === 'undefined') return 'auto';
  const saved = localStorage.getItem('theme-mode');
  if (saved && ['light', 'dark', 'auto'].includes(saved)) {
    return saved as ThemeMode;
  }
  return 'auto';
};

// 计算实际应该应用的主题
const calculateActualTheme = (mode: ThemeMode, systemTheme: 'light' | 'dark'): 'light' | 'dark' => {
  if (mode === 'auto') {
    return systemTheme;
  }
  return mode;
};

// 默认状态
const defaultThemeState: ThemeState = {
  mode: getSavedThemeMode(),
  actualTheme: calculateActualTheme(getSavedThemeMode(), getSystemTheme()),
  isTransitioning: false,
  systemTheme: getSystemTheme(),
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get) => ({
  ...defaultThemeState,

  setThemeMode: (mode: ThemeMode) => {
    const { systemTheme } = get();
    const actualTheme = calculateActualTheme(mode, systemTheme);
    
    // 保存到 localStorage
    localStorage.setItem('theme-mode', mode);
    
    // 设置过渡状态
    set({ isTransitioning: true });
    
    // 更新状态
    set({ mode, actualTheme });
    
    // 短暂延迟后取消过渡状态
    setTimeout(() => {
      set({ isTransitioning: false });
    }, 200);
  },

  toggleTheme: () => {
    const { mode } = get();
    let newMode: ThemeMode;
    
    if (mode === 'auto') {
      // 如果当前是自动模式，切换到与系统主题相反的模式
      const { systemTheme } = get();
      newMode = systemTheme === 'dark' ? 'light' : 'dark';
    } else {
      // 在 light 和 dark 之间切换
      newMode = mode === 'light' ? 'dark' : 'light';
    }
    
    get().setThemeMode(newMode);
  },

  setActualTheme: (actualTheme: 'light' | 'dark') => {
    set({ actualTheme });
  },

  setTransitioning: (isTransitioning: boolean) => {
    set({ isTransitioning });
  },

  updateSystemTheme: (systemTheme: 'light' | 'dark') => {
    const { mode } = get();
    const actualTheme = calculateActualTheme(mode, systemTheme);
    set({ systemTheme, actualTheme });
  },

  initializeTheme: () => {
    const systemTheme = getSystemTheme();
    const mode = getSavedThemeMode();
    const actualTheme = calculateActualTheme(mode, systemTheme);
    
    set({ mode, actualTheme, systemTheme });
    
    // 监听系统主题变化
    if (typeof window !== 'undefined') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      const handleChange = (e: MediaQueryListEvent) => {
        const newSystemTheme = e.matches ? 'dark' : 'light';
        get().updateSystemTheme(newSystemTheme);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      
      // 返回清理函数（虽然在这个上下文中可能不会被调用）
      return () => {
        mediaQuery.removeEventListener('change', handleChange);
      };
    }
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;
