import { StateCreator } from 'zustand';

// 主题类型定义 - 简化为只支持light和dark
export type ThemeMode = 'light' | 'dark';

// 主题状态接口
export interface ThemeState {
  // 当前主题模式
  theme: ThemeMode;
  // 是否正在切换主题（用于动画效果）
  isTransitioning: boolean;
  // 过渡阶段：'idle' | 'preparing' | 'animating' | 'completing'
  transitionPhase: 'idle' | 'preparing' | 'animating' | 'completing';
  // 过渡开始时间戳
  transitionStartTime: number;
}

// 主题操作接口
export interface ThemeActions {
  // 设置主题模式
  setTheme: (theme: ThemeMode) => void;
  // 切换主题（在 light 和 dark 之间切换）
  toggleTheme: () => void;
  // 初始化主题
  initializeTheme: () => void;
  // 设置过渡阶段
  setTransitionPhase: (phase: 'idle' | 'preparing' | 'animating' | 'completing') => void;
  // 开始主题切换动画
  startThemeTransition: (newTheme: ThemeMode) => Promise<void>;
}

// 从 localStorage 获取保存的主题模式
const getSavedTheme = (): ThemeMode => {
  if (typeof window === 'undefined') return 'light';
  const saved = localStorage.getItem('v2-admin-theme');
  return (saved === 'dark' || saved === 'light') ? saved : 'light';
};

// 应用主题到DOM
const applyThemeToDOM = (theme: ThemeMode) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
  }
};

// 默认状态
const defaultThemeState: ThemeState = {
  theme: getSavedTheme(),
  isTransitioning: false,
};

// 创建主题 slice
export const createThemeSlice: StateCreator<
  ThemeState & ThemeActions,
  [],
  [],
  ThemeState & ThemeActions
> = (set, get, api) => ({
  ...defaultThemeState,

  setTheme: (theme: ThemeMode) => {
    // 设置过渡状态
    set({ isTransitioning: true });

    // 更新状态
    set({ theme });

    // 保存到 localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('v2-admin-theme', theme);
    }

    // 应用到DOM
    applyThemeToDOM(theme);

    // 短暂延迟后取消过渡状态
    setTimeout(() => {
      set({ isTransitioning: false });
    }, 300);
  },

  toggleTheme: () => {
    const { theme } = get();
    const newTheme = theme === 'light' ? 'dark' : 'light';
    get().setTheme(newTheme);
  },

  initializeTheme: () => {
    const savedTheme = getSavedTheme();
    set({ theme: savedTheme });
    applyThemeToDOM(savedTheme);
  },
});

// 导出类型
export type ThemeSlice = ThemeState & ThemeActions;
