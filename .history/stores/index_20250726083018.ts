/**
 * Zustand Store 主入口
 * 组合所有状态切片，创建统一的应用状态管理
 */

import React from 'react';
import { create } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';
import { createTabsSlice } from './global/tabsSlice';
import { createLayoutSlice } from './global/layoutSlice';
import { createResponsiveSlice } from './global/responsiveSlice';
import { createRouteHistorySlice } from './global/routeHistorySlice';
import { createRecentVisitsSlice } from './global/recentVisitsSlice';
import { createSystemSettingsSlice } from './settings/systemSettingsSlice';
import { createCaptchaSettingsSlice } from './settings/captchaSettingsSlice';
import { createTitleGenerationSettingsSlice } from './settings/titleGenerationSettingsSlice';
import { createFileMiddlewareSettingsSlice } from './settings/fileMiddlewareSettingsSlice';
import { createThemeSlice } from './settings/themeSlice';
import { createFAQManagementSlice } from './pages/faqManagementSlice';
import { createUserCreateFormSlice } from './components/userCreateFormSlice';
import { createModelCreateFormSlice } from './components/modelCreateFormSlice';
import { createTanStackTableSlice } from './components/tanStackTableSlice';
import { createLoginLogsSlice } from './pages/loginLogsSlice';
import { createOperationLogsSlice } from './pages/operationLogsSlice';
import { createSystemLogsSlice } from './pages/systemLogsSlice';
import { createOnlineUsersSlice } from './pages/onlineUsersSlice';
import { createModelsManagementSlice } from './pages/modelsManagementSlice';
import { createAssistantManagementSlice } from './pages/assistantManagementSlice';
import { createKnowledgeBaseManagementSlice } from './pages/knowledgeBaseManagementSlice';
import type { AppStore } from './types';

/**
 * 创建应用主 Store
 * 使用 Zustand 的中间件增强功能
 */
export const useAppStore = create<AppStore>()(
  devtools(
    persist(
      subscribeWithSelector((set, get, api) => ({
        // 标签页状态切片
        ...createTabsSlice(set as any, get),
        
        // 布局状态切片
        ...createLayoutSlice(set as any, get),
        
        // 响应式状态切片
        ...createResponsiveSlice(set as any, get),
        
        // 路由历史状态切片
        ...createRouteHistorySlice(set as any, get),
        
        // 最近访问状态切片
        ...createRecentVisitsSlice(set as any, get),

        // 系统设置状态切片
        ...createSystemSettingsSlice(set as any, get, api),

        // 验证码设置状态切片
        ...createCaptchaSettingsSlice(set as any, get, api),

        // 标题生成设置状态切片
        ...createTitleGenerationSettingsSlice(set as any, get, api),

        // 文件中间件设置状态切片
        ...createFileMiddlewareSettingsSlice(set as any, get, api),

        // 主题状态切片
        ...createThemeSlice(set as any, get, api),

        // FAQ 管理状态切片
        ...createFAQManagementSlice(set as any, get, api),
        
        // 用户创建表单状态切片
        ...createUserCreateFormSlice(set as any, get, api),
        
        // 模型创建表单状态切片
        ...createModelCreateFormSlice(set as any, get, api),

        // TanStack Table 状态切片
        ...createTanStackTableSlice(set as any, get, api),

        // 登录日志状态切片
        ...createLoginLogsSlice(set as any, get, api),

        // 操作日志状态切片
        ...createOperationLogsSlice(set as any, get, api),

        // 系统日志状态切片
        ...createSystemLogsSlice(set as any, get, api),

        // 在线用户状态切片
        ...createOnlineUsersSlice(set as any, get, api),

        // 模型管理状态切片
        ...createModelsManagementSlice(set as any, get, api),

        // 助手管理状态切片
        ...createAssistantManagementSlice(set as any, get, api),

        // 知识库管理状态切片
        ...createKnowledgeBaseManagementSlice(set as any, get, api),
      })),
      {
        name: 'v2-admin-store',
        // 只持久化需要的状态
        partialize: (state: AppStore) => ({
          // 标签页状态
          tabs: state.tabs,
          
          // 布局状态
          sidebarCollapsed: state.sidebarCollapsed,
          theme: state.theme,
          
          // 路由历史
          history: state.history,
          
          // 最近访问
          visits: state.visits,

          // 系统设置（缓存）
          settings: state.settings,
        }),
        // 版本控制，用于状态迁移
        version: 1,
      }
    ),
    {
      name: 'V2-Admin-Store',
      enabled: process.env.NODE_ENV === 'development',
    }
  )
);

// ==================== 选择器 Hooks ====================

/**
 * 标签页相关选择器
 */
export const useTabsStore = () => {
  const tabs = useAppStore((state) => state.tabs);
  const isLoading = useAppStore((state) => state.isLoading);
  const loadTabs = useAppStore((state) => state.loadTabs);
  const addTab = useAppStore((state) => state.addTab);
  const removeTab = useAppStore((state) => state.removeTab);
  const pinTab = useAppStore((state) => state.pinTab);
  const refreshTab = useAppStore((state) => state.refreshTab);
  const clearOtherTabs = useAppStore((state) => state.clearOtherTabs);
  const clearAllTabs = useAppStore((state) => state.clearAllTabs);
  const getActiveTab = useAppStore((state) => state.getActiveTab);
  const getTabById = useAppStore((state) => state.getTabById);
  const getTabByPath = useAppStore((state) => state.getTabByPath);

  return {
    tabs,
    isLoading,
    loadTabs,
    addTab,
    removeTab,
    pinTab,
    refreshTab,
    clearOtherTabs,
    clearAllTabs,
    getActiveTab,
    getTabById,
    getTabByPath,
  };
};

/**
 * 布局相关选择器
 */
export const useLayoutStore = () => {
  const sidebarCollapsed = useAppStore((state) => state.sidebarCollapsed);
  const sidebarWidth = useAppStore((state) => state.sidebarWidth);
  const theme = useAppStore((state) => state.theme);
  const isMobile = useAppStore((state) => state.isMobile);
  const mobileDrawerOpen = useAppStore((state) => state.mobileDrawerOpen);
  const headerHeight = useAppStore((state) => state.headerHeight);
  const contentPadding = useAppStore((state) => state.contentPadding);
  const toggleSidebar = useAppStore((state) => state.toggleSidebar);
  const setSidebarCollapsed = useAppStore((state) => state.setSidebarCollapsed);
  const setTheme = useAppStore((state) => state.setTheme);
  const toggleTheme = useAppStore((state) => state.toggleTheme);
  const toggleMobileDrawer = useAppStore((state) => state.toggleMobileDrawer);
  const updateResponsive = useAppStore((state) => state.updateResponsive);
  const updateLayoutConfig = useAppStore((state) => state.updateLayoutConfig);

  return {
    sidebarCollapsed,
    sidebarWidth,
    theme,
    isMobile,
    mobileDrawerOpen,
    headerHeight,
    contentPadding,
    toggleSidebar,
    setSidebarCollapsed,
    setTheme,
    toggleTheme,
    toggleMobileDrawer,
    updateResponsive,
    updateLayoutConfig,
  };
};

/**
 * 响应式相关选择器
 */
export const useResponsiveStore = () => {
  const isMobile = useAppStore((state) => state.isMobile);
  const isTablet = useAppStore((state) => state.isTablet);
  const isDesktop = useAppStore((state) => state.isDesktop);
  const currentBreakpoint = useAppStore((state) => state.currentBreakpoint);
  const screenWidth = useAppStore((state) => state.screenWidth);
  const screenHeight = useAppStore((state) => state.screenHeight);
  const updateScreenSize = useAppStore((state) => state.updateScreenSize);
  const updateBreakpoint = useAppStore((state) => state.updateBreakpoint);

  return {
    isMobile,
    isTablet,
    isDesktop,
    currentBreakpoint,
    screenWidth,
    screenHeight,
    updateScreenSize,
    updateBreakpoint,
  };
};

/**
 * 路由历史相关选择器
 */
export const useRouteHistoryStore = () => {
  const history = useAppStore((state) => state.history);
  const currentPath = useAppStore((state) => state.currentPath);
  const addRoute = useAppStore((state) => state.addRoute);
  const removeRoute = useAppStore((state) => state.removeRoute);
  const clearHistory = useAppStore((state) => state.clearHistory);
  const setCurrentPath = useAppStore((state) => state.setCurrentPath);
  const getRecentRoutes = useAppStore((state) => state.getRecentRoutes);

  return {
    history,
    currentPath,
    addRoute,
    removeRoute,
    clearHistory,
    setCurrentPath,
    getRecentRoutes,
  };
};

/**
 * 最近访问相关选择器
 */
export const useRecentVisitsStore = () => {
  const visits = useAppStore((state) => state.visits);
  const addVisit = useAppStore((state) => state.addVisit);
  const removeVisit = useAppStore((state) => state.removeVisit);
  const clearVisits = useAppStore((state) => state.clearVisits);
  const getRecentVisits = useAppStore((state) => state.getRecentVisits);
  const getMostVisited = useAppStore((state) => state.getMostVisited);

  return {
    visits,
    addVisit,
    removeVisit,
    clearVisits,
    getRecentVisits,
    getMostVisited,
  };
};

/**
 * 系统设置相关选择器
 */
export const useSystemSettingsStore = () => {
  const settings = useAppStore((state) => state.settings);
  const loading = useAppStore((state) => state.loading);
  const saving = useAppStore((state) => state.saving);
  const error = useAppStore((state) => state.error);
  const lastUpdated = useAppStore((state) => state.lastUpdated);
  const isDirty = useAppStore((state) => state.isDirty);
  const loadSettings = useAppStore((state) => state.loadSettings);
  const updateSettings = useAppStore((state) => state.updateSettings);
  const setSettings = useAppStore((state) => state.setSettings);
  const setLoading = useAppStore((state) => state.setLoading);
  const setSaving = useAppStore((state) => state.setSaving);
  const setError = useAppStore((state) => state.setError);
  const markDirty = useAppStore((state) => state.markDirty);
  const resetSettings = useAppStore((state) => state.resetSettings);
  const getDefaultSettings = useAppStore((state) => state.getDefaultSettings);
  const validateSettings = useAppStore((state) => state.validateSettings);

  return {
    settings,
    loading,
    saving,
    error,
    lastUpdated,
    isDirty,
    loadSettings,
    updateSettings,
    setSettings,
    setLoading,
    setSaving,
    setError,
    markDirty,
    resetSettings,
    getDefaultSettings,
    validateSettings,
  };
};

/**
 * 验证码设置相关选择器
 */
export const useCaptchaSettingsStore = () => {
  const captchaSettings = useAppStore((state) => state.captchaSettings);
  const captchaLoading = useAppStore((state) => state.captchaLoading);
  const captchaSaving = useAppStore((state) => state.captchaSaving);
  const captchaError = useAppStore((state) => state.captchaError);
  const captchaLastUpdated = useAppStore((state) => state.captchaLastUpdated);
  const captchaIsDirty = useAppStore((state) => state.captchaIsDirty);
  const testCaptcha = useAppStore((state) => state.testCaptcha);
  const testCaptchaLoading = useAppStore((state) => state.testCaptchaLoading);
  const loadCaptchaSettings = useAppStore((state) => state.loadCaptchaSettings);
  const updateCaptchaSettings = useAppStore((state) => state.updateCaptchaSettings);
  const setCaptchaSettings = useAppStore((state) => state.setCaptchaSettings);
  const setCaptchaLoading = useAppStore((state) => state.setCaptchaLoading);
  const setCaptchaSaving = useAppStore((state) => state.setCaptchaSaving);
  const setCaptchaError = useAppStore((state) => state.setCaptchaError);
  const markCaptchaDirty = useAppStore((state) => state.markCaptchaDirty);
  const resetCaptchaSettings = useAppStore((state) => state.resetCaptchaSettings);
  const getCaptchaDefaultSettings = useAppStore((state) => state.getCaptchaDefaultSettings);
  const generateTestCaptcha = useAppStore((state) => state.generateTestCaptcha);
  const validateCaptcha = useAppStore((state) => state.validateCaptcha);
  const refreshCaptcha = useAppStore((state) => state.refreshCaptcha);

  return {
    captchaSettings,
    captchaLoading,
    captchaSaving,
    captchaError,
    captchaLastUpdated,
    captchaIsDirty,
    testCaptcha,
    testCaptchaLoading,
    loadCaptchaSettings,
    updateCaptchaSettings,
    setCaptchaSettings,
    setCaptchaLoading,
    setCaptchaSaving,
    setCaptchaError,
    markCaptchaDirty,
    resetCaptchaSettings,
    getCaptchaDefaultSettings,
    generateTestCaptcha,
    validateCaptcha,
    refreshCaptcha,
  };
};

/**
 * 标题生成设置相关选择器
 */
export const useTitleGenerationSettingsStore = () => {
  const titleGenerationSettings = useAppStore((state) => state.titleGenerationSettings);
  const titleGenerationLoading = useAppStore((state) => state.titleGenerationLoading);
  const titleGenerationSaving = useAppStore((state) => state.titleGenerationSaving);
  const titleGenerationError = useAppStore((state) => state.titleGenerationError);
  const titleGenerationLastUpdated = useAppStore((state) => state.titleGenerationLastUpdated);
  const titleGenerationIsDirty = useAppStore((state) => state.titleGenerationIsDirty);
  const models = useAppStore((state) => state.models);
  const loadingModels = useAppStore((state) => state.loadingModels);
  const testResult = useAppStore((state) => state.testResult);
  const testing = useAppStore((state) => state.testing);
  const loadTitleGenerationSettings = useAppStore((state) => state.loadTitleGenerationSettings);
  const updateTitleGenerationSettings = useAppStore((state) => state.updateTitleGenerationSettings);
  const setTitleGenerationSettings = useAppStore((state) => state.setTitleGenerationSettings);
  const setTitleGenerationLoading = useAppStore((state) => state.setTitleGenerationLoading);
  const setTitleGenerationSaving = useAppStore((state) => state.setTitleGenerationSaving);
  const setTitleGenerationError = useAppStore((state) => state.setTitleGenerationError);
  const markTitleGenerationDirty = useAppStore((state) => state.markTitleGenerationDirty);
  const resetTitleGenerationSettings = useAppStore((state) => state.resetTitleGenerationSettings);
  const getTitleGenerationDefaultSettings = useAppStore((state) => state.getTitleGenerationDefaultSettings);
  const loadModels = useAppStore((state) => state.loadModels);
  const testTitleGeneration = useAppStore((state) => state.testTitleGeneration);

  return {
    titleGenerationSettings,
    titleGenerationLoading,
    titleGenerationSaving,
    titleGenerationError,
    titleGenerationLastUpdated,
    titleGenerationIsDirty,
    models,
    loadingModels,
    testResult,
    testing,
    loadTitleGenerationSettings,
    updateTitleGenerationSettings,
    setTitleGenerationSettings,
    setTitleGenerationLoading,
    setTitleGenerationSaving,
    setTitleGenerationError,
    markTitleGenerationDirty,
    resetTitleGenerationSettings,
    getTitleGenerationDefaultSettings,
    loadModels,
    testTitleGeneration,
  };
};

/**
 * 文件中间件设置相关选择器
 */
export const useFileMiddlewareSettingsStore = () => {
  const fileMiddlewareSettings = useAppStore((state) => state.fileMiddlewareSettings);
  const fileMiddlewareLoading = useAppStore((state) => state.fileMiddlewareLoading);
  const fileMiddlewareSaving = useAppStore((state) => state.fileMiddlewareSaving);
  const fileMiddlewareError = useAppStore((state) => state.fileMiddlewareError);
  const fileMiddlewareLastUpdated = useAppStore((state) => state.fileMiddlewareLastUpdated);
  const fileMiddlewareIsDirty = useAppStore((state) => state.fileMiddlewareIsDirty);
  const testResult = useAppStore((state) => state.testResult);
  const testing = useAppStore((state) => state.testing);
  const loadFileMiddlewareSettings = useAppStore((state) => state.loadFileMiddlewareSettings);
  const updateFileMiddlewareSettings = useAppStore((state) => state.updateFileMiddlewareSettings);
  const setFileMiddlewareSettings = useAppStore((state) => state.setFileMiddlewareSettings);
  const setFileMiddlewareLoading = useAppStore((state) => state.setFileMiddlewareLoading);
  const setFileMiddlewareSaving = useAppStore((state) => state.setFileMiddlewareSaving);
  const setFileMiddlewareError = useAppStore((state) => state.setFileMiddlewareError);
  const markFileMiddlewareDirty = useAppStore((state) => state.markFileMiddlewareDirty);
  const resetFileMiddlewareSettings = useAppStore((state) => state.resetFileMiddlewareSettings);
  const getFileMiddlewareDefaultSettings = useAppStore((state) => state.getFileMiddlewareDefaultSettings);
  const testFileMiddleware = useAppStore((state) => state.testFileMiddleware);

  return {
    fileMiddlewareSettings,
    fileMiddlewareLoading,
    fileMiddlewareSaving,
    fileMiddlewareError,
    fileMiddlewareLastUpdated,
    fileMiddlewareIsDirty,
    testResult,
    testing,
    loadFileMiddlewareSettings,
    updateFileMiddlewareSettings,
    setFileMiddlewareSettings,
    setFileMiddlewareLoading,
    setFileMiddlewareSaving,
    setFileMiddlewareError,
    markFileMiddlewareDirty,
    resetFileMiddlewareSettings,
    getFileMiddlewareDefaultSettings,
    testFileMiddleware,
  };
};

/**
 * FAQ 管理相关选择器
 */
export const useFAQManagementStore = () => {
  const suggestions = useAppStore((state) => state.suggestions);
  const currentSuggestion = useAppStore((state) => state.currentSuggestion);
  const stats = useAppStore((state) => state.stats);
  const pagination = useAppStore((state) => state.faqPagination);
  const queryParams = useAppStore((state) => state.queryParams);
  const loading = useAppStore((state) => state.loading);
  const saving = useAppStore((state) => state.saving);
  const deleting = useAppStore((state) => state.deleting);
  const error = useAppStore((state) => state.error);
  const modalVisible = useAppStore((state) => state.modalVisible);
  const editingItem = useAppStore((state) => state.editingItem);
  const selectedRowKeys = useAppStore((state) => state.selectedRowKeys);
  const lastUpdated = useAppStore((state) => state.lastUpdated);
  const isDirty = useAppStore((state) => state.isDirty);
  const loadSuggestions = useAppStore((state) => state.loadSuggestions);
  const loadSuggestionById = useAppStore((state) => state.loadSuggestionById);
  const createSuggestion = useAppStore((state) => state.createSuggestion);
  const updateSuggestion = useAppStore((state) => state.updateSuggestion);
  const deleteSuggestion = useAppStore((state) => state.deleteSuggestion);
  const batchDeleteSuggestions = useAppStore((state) => state.batchDeleteSuggestions);
  const updateSuggestionStatus = useAppStore((state) => state.updateSuggestionStatus);
  const updateSuggestionOrder = useAppStore((state) => state.updateSuggestionOrder);
  const loadStats = useAppStore((state) => state.loadStats);
  const setLoading = useAppStore((state) => state.setLoading);
  const setSaving = useAppStore((state) => state.setSaving);
  const setDeleting = useAppStore((state) => state.setDeleting);
  const setError = useAppStore((state) => state.setError);
  const setModalVisible = useAppStore((state) => state.setModalVisible);
  const setEditingItem = useAppStore((state) => state.setEditingItem);
  const setSelectedRowKeys = useAppStore((state) => state.setSelectedRowKeys);
  const setQueryParams = useAppStore((state) => state.setQueryParams);
  const setPagination = useAppStore((state) => state.setFaqPagination);
  const resetFAQManagement = useAppStore((state) => state.resetFAQManagement);
  const markDirty = useAppStore((state) => state.markDirty);

  return {
    suggestions,
    currentSuggestion,
    stats,
    pagination,
    queryParams,
    loading,
    saving,
    deleting,
    error,
    modalVisible,
    editingItem,
    selectedRowKeys,
    lastUpdated,
    isDirty,
    loadSuggestions,
    loadSuggestionById,
    createSuggestion,
    updateSuggestion,
    deleteSuggestion,
    batchDeleteSuggestions,
    updateSuggestionStatus,
    updateSuggestionOrder,
    loadStats,
    setLoading,
    setSaving,
    setDeleting,
    setError,
    setModalVisible,
    setEditingItem,
    setSelectedRowKeys,
    setQueryParams,
    setPagination,
    resetFAQManagement,
    markDirty,
  };
};

/**
 * 登录日志相关选择器
 */
export const useLoginLogsStore = () => {
  const data = useAppStore((state) => state.loginLogsData);
  const loading = useAppStore((state) => state.loginLogsLoading);
  const sorting = useAppStore((state) => state.loginLogsSorting);
  const pagination = useAppStore((state) => state.loginLogsPagination);
  const columnFilters = useAppStore((state) => state.loginLogsColumnFilters);
  const globalFilter = useAppStore((state) => state.loginLogsGlobalFilter);
  const searchParams = useAppStore((state) => state.loginLogsSearchParams);
  const setData = useAppStore((state) => state.setLoginLogsData);
  const setLoading = useAppStore((state) => state.setLoginLogsLoading);
  const setSorting = useAppStore((state) => state.setLoginLogsSorting);
  const setPagination = useAppStore((state) => state.setLoginLogsPagination);
  const setColumnFilters = useAppStore((state) => state.setLoginLogsColumnFilters);
  const setGlobalFilter = useAppStore((state) => state.setLoginLogsGlobalFilter);
  const setSearchParams = useAppStore((state) => state.setLoginLogsSearchParams);
  const resetSearchParams = useAppStore((state) => state.resetLoginLogsSearchParams);
  const loadData = useAppStore((state) => state.loadLoginLogsData);

  return {
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData,
  };
};





// ==================== 工具函数 ====================

/**
 * 用户创建表单相关选择器
 */
export const useUserCreateFormStore = () => {
  const visible = useAppStore((state) => state.visible);
  const loading = useAppStore((state) => state.loading);
  const saving = useAppStore((state) => state.saving);
  const avatarLoading = useAppStore((state) => state.avatarLoading);
  const error = useAppStore((state) => state.error);
  const editingUser = useAppStore((state) => state.editingUser);
  const avatarUrl = useAppStore((state) => state.avatarUrl);
  const avatarFile = useAppStore((state) => state.avatarFile);
  const isDirty = useAppStore((state) => state.isDirty);

  const setVisible = useAppStore((state) => state.setVisible);
  const setEditingUser = useAppStore((state) => state.setEditingUser);
  const setAvatarUrl = useAppStore((state) => state.setAvatarUrl);
  const setAvatarFile = useAppStore((state) => state.setAvatarFile);
  const uploadAvatar = useAppStore((state) => state.uploadAvatar);
  const createUser = useAppStore((state) => state.createUser);
  const updateUser = useAppStore((state) => state.updateUser);
  const resetForm = useAppStore((state) => state.resetForm);
  const setError = useAppStore((state) => state.setError);
  const clearError = useAppStore((state) => state.clearError);
  const markDirty = useAppStore((state) => state.markDirty);

  return {
    // 状态
    visible,
    loading,
    saving,
    avatarLoading,
    error,
    editingUser,
    avatarUrl,
    avatarFile,
    isDirty,

    // 操作
    setVisible,
    setEditingUser,
    setAvatarUrl,
    setAvatarFile,
    uploadAvatar,
    createUser,
    updateUser,
    resetForm,
    setError,
    clearError,
    markDirty,
  };
};

/**
 * 模型创建表单相关选择器
 */
export const useModelCreateFormStore = () => {
  const visible = useAppStore((state) => state.visible);
  const loading = useAppStore((state) => state.loading);
  const saving = useAppStore((state) => state.saving);
  const error = useAppStore((state) => state.error);
  const parameters = useAppStore((state) => state.parameters);
  const apiSources = useAppStore((state) => state.apiSources);
  const loadingApiSources = useAppStore((state) => state.loadingApiSources);
  const generatedId = useAppStore((state) => state.generatedId);
  const isDirty = useAppStore((state) => state.isDirty);

  const setVisible = useAppStore((state) => state.setVisible);
  const setParameters = useAppStore((state) => state.setParameters);
  const addParameter = useAppStore((state) => state.addParameter);
  const removeParameter = useAppStore((state) => state.removeParameter);
  const updateParameter = useAppStore((state) => state.updateParameter);
  const fetchApiSources = useAppStore((state) => state.fetchApiSources);
  const setApiSources = useAppStore((state) => state.setApiSources);
  const generateId = useAppStore((state) => state.generateId);
  const createModel = useAppStore((state) => state.createModel);
  const resetForm = useAppStore((state) => state.resetForm);
  const setError = useAppStore((state) => state.setError);
  const clearError = useAppStore((state) => state.clearError);
  const markDirty = useAppStore((state) => state.markDirty);

  return {
    // 状态
    visible,
    loading,
    saving,
    error,
    parameters,
    apiSources,
    loadingApiSources,
    generatedId,
    isDirty,

    // 操作
    setVisible,
    setParameters,
    addParameter,
    removeParameter,
    updateParameter,
    fetchApiSources,
    setApiSources,
    generateId,
    createModel,
    resetForm,
    setError,
    clearError,
    markDirty,
  };
};

/**
 * TanStack Table 相关选择器
 */
export const useTanStackTableStore = () => {
  const tableLoading = useAppStore((state) => state.tableLoading);
  const tableError = useAppStore((state) => state.tableError);
  const tableSorting = useAppStore((state) => state.tableSorting);
  const tablePagination = useAppStore((state) => state.tablePagination);
  const tableColumnFilters = useAppStore((state) => state.tableColumnFilters);
  const tableGlobalFilter = useAppStore((state) => state.tableGlobalFilter);
  const tableSelectedRows = useAppStore((state) => state.tableSelectedRows);
  const tableSelectedRowIds = useAppStore((state) => state.tableSelectedRowIds);
  const tableData = useAppStore((state) => state.tableData);
  const tableFilteredData = useAppStore((state) => state.tableFilteredData);
  const tableTotalCount = useAppStore((state) => state.tableTotalCount);

  const setTableLoading = useAppStore((state) => state.setTableLoading);
  const setTableError = useAppStore((state) => state.setTableError);
  const clearTableError = useAppStore((state) => state.clearTableError);
  const setTableSorting = useAppStore((state) => state.setTableSorting);
  const setTablePagination = useAppStore((state) => state.setTablePagination);
  const goToTablePage = useAppStore((state) => state.goToTablePage);
  const setTablePageSize = useAppStore((state) => state.setTablePageSize);
  const nextTablePage = useAppStore((state) => state.nextTablePage);
  const previousTablePage = useAppStore((state) => state.previousTablePage);
  const setTableColumnFilters = useAppStore((state) => state.setTableColumnFilters);
  const setTableGlobalFilter = useAppStore((state) => state.setTableGlobalFilter);
  const setTableSelectedRows = useAppStore((state) => state.setTableSelectedRows);
  const selectTableRow = useAppStore((state) => state.selectTableRow);
  const selectAllTableRows = useAppStore((state) => state.selectAllTableRows);
  const clearTableSelection = useAppStore((state) => state.clearTableSelection);
  const setTableData = useAppStore((state) => state.setTableData);
  const setTableFilteredData = useAppStore((state) => state.setTableFilteredData);
  const setTableTotalCount = useAppStore((state) => state.setTableTotalCount);
  const resetTableFilters = useAppStore((state) => state.resetTableFilters);
  const applyTableFilters = useAppStore((state) => state.applyTableFilters);
  const resetTable = useAppStore((state) => state.resetTable);

  return {
    // 状态 - 使用简化的属性名以便于使用
    loading: tableLoading,
    error: tableError,
    sorting: tableSorting,
    pagination: tablePagination,
    columnFilters: tableColumnFilters,
    globalFilter: tableGlobalFilter,
    selectedRows: tableSelectedRows,
    selectedRowIds: tableSelectedRowIds,
    data: tableData,
    filteredData: tableFilteredData,
    totalCount: tableTotalCount,

    // 操作 - 使用简化的方法名以便于使用
    setLoading: setTableLoading,
    setError: setTableError,
    clearError: clearTableError,
    setSorting: setTableSorting,
    setPagination: setTablePagination,
    goToPage: goToTablePage,
    setPageSize: setTablePageSize,
    nextPage: nextTablePage,
    previousPage: previousTablePage,
    setColumnFilters: setTableColumnFilters,
    setGlobalFilter: setTableGlobalFilter,
    setSelectedRows: setTableSelectedRows,
    selectRow: selectTableRow,
    selectAllRows: selectAllTableRows,
    clearSelection: clearTableSelection,
    setData: setTableData,
    setFilteredData: setTableFilteredData,
    setTotalCount: setTableTotalCount,
    resetFilters: resetTableFilters,
    applyFilters: applyTableFilters,
    resetTable,
  };
};

/**
 * 重置所有状态到初始值
 */
export const resetStore = () => {
  useAppStore.setState((state) => ({
    ...state,
    // 重置标签页
    tabs: [],
    isLoading: false,
    
    // 重置布局
    sidebarCollapsed: false,
    theme: 'light' as const,
    mobileDrawerOpen: false,
    
    // 重置历史
    history: [],
    currentPath: '',
    
    // 重置访问记录
    visits: [],
    
    // 重置用户创建表单
    avatarUrl: undefined,
    avatarLoading: false,
    avatarFile: null,
    isEditMode: false,
    formVisible: false,
    
    // 重置模型创建表单
    parameters: [{ name: "", value: "" }],
    apiSources: [],
    loadingApiSources: false,
    visible: false,
    
    // 重置登录日志
    data: [],
    loading: false,
    sorting: [],
    pagination: {
      pageIndex: 0,
      pageSize: 10,
    },
    columnFilters: [],
    globalFilter: '',
    searchParams: {
      username: '',
      ip: '',
      location: '',
      status: '',
      startTime: '',
      endTime: '',
    },
  }));
};

/**
 * 获取当前完整状态（调试用）
 */
export const getStoreSnapshot = () => useAppStore.getState();

/**
 * 在线用户相关选择器
 */
export const useOnlineUsersStore = () => {
  const onlineUsers = useAppStore((state) => state.onlineUsers);
  const loading = useAppStore((state) => state.loading);
  const error = useAppStore((state) => state.error);
  const statusFilter = useAppStore((state) => state.statusFilter);
  const osFilter = useAppStore((state) => state.osFilter);
  const locationFilter = useAppStore((state) => state.locationFilter);
  const searchText = useAppStore((state) => state.searchText);
  const dataInitialized = useAppStore((state) => state.dataInitialized);
  const autoRefreshEnabled = useAppStore((state) => state.autoRefreshEnabled);
  const refreshInterval = useAppStore((state) => state.refreshInterval);
  const lastUpdated = useAppStore((state) => state.lastUpdated);
  
  // TanStackTable 状态
  const sorting = useAppStore((state) => state.onlineUsersSorting);
  const pagination = useAppStore((state) => state.onlineUsersPagination);
  const columnFilters = useAppStore((state) => state.onlineUsersColumnFilters);
  const globalFilter = useAppStore((state) => state.onlineUsersGlobalFilter);

  // 操作方法
  const setOnlineUsers = useAppStore((state) => state.setOnlineUsers);
  const addOnlineUser = useAppStore((state) => state.addOnlineUser);
  const removeOnlineUser = useAppStore((state) => state.removeOnlineUser);
  const updateOnlineUser = useAppStore((state) => state.updateOnlineUser);
  const setLoading = useAppStore((state) => state.setLoading);
  const setError = useAppStore((state) => state.setError);
  const setStatusFilter = useAppStore((state) => state.setStatusFilter);
  const setOsFilter = useAppStore((state) => state.setOsFilter);
  const setLocationFilter = useAppStore((state) => state.setLocationFilter);
  const setSearchText = useAppStore((state) => state.setSearchText);
  const setFilters = useAppStore((state) => state.setFilters);
  const resetFilters = useAppStore((state) => state.resetFilters);
  const resetOnlineUsers = useAppStore((state) => state.resetOnlineUsers);
  const setDataInitialized = useAppStore((state) => state.setDataInitialized);
  const setAutoRefreshEnabled = useAppStore((state) => state.setAutoRefreshEnabled);
  const setRefreshInterval = useAppStore((state) => state.setRefreshInterval);
  const refreshOnlineUsers = useAppStore((state) => state.refreshOnlineUsers);
  
  // TanStackTable 操作方法
  const setSorting = useAppStore((state) => state.setOnlineUsersSorting);
  const setPagination = useAppStore((state) => state.setOnlineUsersPagination);
  const setColumnFilters = useAppStore((state) => state.setOnlineUsersColumnFilters);
  const setGlobalFilter = useAppStore((state) => state.setOnlineUsersGlobalFilter);
  const resetTableFilters = useAppStore((state) => state.resetOnlineUsersTableFilters);

  return {
    // 状态
    onlineUsers,
    loading,
    error,
    statusFilter,
    osFilter,
    locationFilter,
    searchText,
    dataInitialized,
    autoRefreshEnabled,
    refreshInterval,
    lastUpdated,
    
    // TanStackTable 状态
    sorting,
    pagination,
    columnFilters,
    globalFilter,

    // 操作
    setOnlineUsers,
    addOnlineUser,
    removeOnlineUser,
    updateOnlineUser,
    setLoading,
    setError,
    setStatusFilter,
    setOsFilter,
    setLocationFilter,
    setSearchText,
    setFilters,
    resetFilters,
    resetOnlineUsers,
    setDataInitialized,
    setAutoRefreshEnabled,
    setRefreshInterval,
    refreshOnlineUsers,
    
    // TanStackTable 操作
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    resetTableFilters,
  };
};

/**
 * 操作日志相关选择器
 */
export const useOperationLogsStore = () => {
  const data = useAppStore((state) => state.operationLogsData);
  const loading = useAppStore((state) => state.operationLogsLoading);
  const sorting = useAppStore((state) => state.operationLogsSorting);
  const pagination = useAppStore((state) => state.operationLogsPagination);
  const columnFilters = useAppStore((state) => state.operationLogsColumnFilters);
  const globalFilter = useAppStore((state) => state.operationLogsGlobalFilter);
  const searchParams = useAppStore((state) => state.operationLogsSearchParams);

  // 操作方法
  const setData = useAppStore((state) => state.setOperationLogsData);
  const setLoading = useAppStore((state) => state.setOperationLogsLoading);
  const setSorting = useAppStore((state) => state.setOperationLogsSorting);
  const setPagination = useAppStore((state) => state.setOperationLogsPagination);
  const setColumnFilters = useAppStore((state) => state.setOperationLogsColumnFilters);
  const setGlobalFilter = useAppStore((state) => state.setOperationLogsGlobalFilter);
  const setSearchParams = useAppStore((state) => state.setOperationLogsSearchParams);
  const resetSearchParams = useAppStore((state) => state.resetOperationLogsSearchParams);
  const loadData = useAppStore((state) => state.loadOperationLogsData);

  return {
    // 状态
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,

    // 操作
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData,
  };
};

/**
 * 系统日志相关选择器
 */
export const useSystemLogsStore = () => {
  const data = useAppStore((state) => state.systemLogsData);
  const loading = useAppStore((state) => state.systemLogsLoading);
  const sorting = useAppStore((state) => state.systemLogsSorting);
  const pagination = useAppStore((state) => state.systemLogsPagination);
  const columnFilters = useAppStore((state) => state.systemLogsColumnFilters);
  const globalFilter = useAppStore((state) => state.systemLogsGlobalFilter);
  const searchParams = useAppStore((state) => state.systemLogsSearchParams);

  // 操作方法
  const setData = useAppStore((state) => state.setSystemLogsData);
  const setLoading = useAppStore((state) => state.setSystemLogsLoading);
  const setSorting = useAppStore((state) => state.setSystemLogsSorting);
  const setPagination = useAppStore((state) => state.setSystemLogsPagination);
  const setColumnFilters = useAppStore((state) => state.setSystemLogsColumnFilters);
  const setGlobalFilter = useAppStore((state) => state.setSystemLogsGlobalFilter);
  const setSearchParams = useAppStore((state) => state.setSystemLogsSearchParams);
  const resetSearchParams = useAppStore((state) => state.resetSystemLogsSearchParams);
  const loadData = useAppStore((state) => state.loadSystemLogsData);

  return {
    // 状态
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,

    // 操作
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData,
  };
};

/**
 * 模型管理相关选择器
 */
export const useModelsManagementStore = () => {
  // 模型数据状态
  const modelsData = useAppStore((state) => state.modelsData);
  const modelsLoading = useAppStore((state) => state.modelsLoading);
  const modelsError = useAppStore((state) => state.modelsError);
  
  // API源数据状态
  const apiSourcesData = useAppStore((state) => state.apiSourcesData);
  const apiSourcesLoading = useAppStore((state) => state.apiSourcesLoading);
  const apiSourcesError = useAppStore((state) => state.apiSourcesError);
  
  // 当前活动标签
  const activeTab = useAppStore((state) => state.activeTab);
  
  // 模型筛选状态
  const modelsSearchText = useAppStore((state) => state.modelsSearchText);
  const modelsTypeFilter = useAppStore((state) => state.modelsTypeFilter);
  const modelsVendorFilter = useAppStore((state) => state.modelsVendorFilter);
  const modelsStatusFilter = useAppStore((state) => state.modelsStatusFilter);
  
  // API源筛选状态
  const apiSourcesSearchText = useAppStore((state) => state.apiSourcesSearchText);
  const apiSourcesTypeFilter = useAppStore((state) => state.apiSourcesTypeFilter);
  const apiSourcesStatusFilter = useAppStore((state) => state.apiSourcesStatusFilter);
  
  // 模型表格状态
  const modelsSorting = useAppStore((state) => state.modelsSorting);
  const modelsPagination = useAppStore((state) => state.modelsPagination);
  const modelsColumnFilters = useAppStore((state) => state.modelsColumnFilters);
  const modelsGlobalFilter = useAppStore((state) => state.modelsGlobalFilter);
  
  // API源表格状态
  const apiSourcesSorting = useAppStore((state) => state.apiSourcesSorting);
  const apiSourcesPagination = useAppStore((state) => state.apiSourcesPagination);
  const apiSourcesColumnFilters = useAppStore((state) => state.apiSourcesColumnFilters);
  const apiSourcesGlobalFilter = useAppStore((state) => state.apiSourcesGlobalFilter);
  
  // 模态框状态
  const isModelModalVisible = useAppStore((state) => state.isModelModalVisible);
  const isApiSourceModalVisible = useAppStore((state) => state.isApiSourceModalVisible);
  const selectedModel = useAppStore((state) => state.selectedModel);
  const selectedApiSource = useAppStore((state) => state.selectedApiSource);
  
  // 数据初始化状态
  const dataInitialized = useAppStore((state) => state.dataInitialized);
  const modelsLastUpdated = useAppStore((state) => state.modelsLastUpdated);
  
  // 模型数据操作
  const setModelsData = useAppStore((state) => state.setModelsData);
  const addModel = useAppStore((state) => state.addModel);
  const updateModel = useAppStore((state) => state.updateModel);
  const removeModel = useAppStore((state) => state.removeModel);
  const toggleModelStatus = useAppStore((state) => state.toggleModelStatus);
  
  // API源数据操作
  const setApiSourcesData = useAppStore((state) => state.setApiSourcesData);
  const addApiSource = useAppStore((state) => state.addApiSource);
  const updateApiSource = useAppStore((state) => state.updateApiSource);
  const removeApiSource = useAppStore((state) => state.removeApiSource);
  const toggleApiSourceStatus = useAppStore((state) => state.toggleApiSourceStatus);
  
  // 加载状态操作
  const setModelsLoading = useAppStore((state) => state.setModelsLoading);
  const setApiSourcesLoading = useAppStore((state) => state.setApiSourcesLoading);
  const setModelsError = useAppStore((state) => state.setModelsError);
  const setApiSourcesError = useAppStore((state) => state.setApiSourcesError);
  
  // 标签切换
  const setActiveTab = useAppStore((state) => state.setActiveTab);
  
  // 模型筛选操作
  const setModelsSearchText = useAppStore((state) => state.setModelsSearchText);
  const setModelsTypeFilter = useAppStore((state) => state.setModelsTypeFilter);
  const setModelsVendorFilter = useAppStore((state) => state.setModelsVendorFilter);
  const setModelsStatusFilter = useAppStore((state) => state.setModelsStatusFilter);
  const setModelsFilters = useAppStore((state) => state.setModelsFilters);
  
  // API源筛选操作
  const setApiSourcesSearchText = useAppStore((state) => state.setApiSourcesSearchText);
  const setApiSourcesTypeFilter = useAppStore((state) => state.setApiSourcesTypeFilter);
  const setApiSourcesStatusFilter = useAppStore((state) => state.setApiSourcesStatusFilter);
  const setApiSourcesFilters = useAppStore((state) => state.setApiSourcesFilters);
  
  // 表格状态操作
  const setModelsSorting = useAppStore((state) => state.setModelsSorting);
  const setModelsPagination = useAppStore((state) => state.setModelsPagination);
  const setModelsColumnFilters = useAppStore((state) => state.setModelsColumnFilters);
  const setModelsGlobalFilter = useAppStore((state) => state.setModelsGlobalFilter);
  
  const setApiSourcesSorting = useAppStore((state) => state.setApiSourcesSorting);
  const setApiSourcesPagination = useAppStore((state) => state.setApiSourcesPagination);
  const setApiSourcesColumnFilters = useAppStore((state) => state.setApiSourcesColumnFilters);
  const setApiSourcesGlobalFilter = useAppStore((state) => state.setApiSourcesGlobalFilter);
  
  // 模态框操作
  const setIsModelModalVisible = useAppStore((state) => state.setIsModelModalVisible);
  const setIsApiSourceModalVisible = useAppStore((state) => state.setIsApiSourceModalVisible);
  const setSelectedModel = useAppStore((state) => state.setSelectedModel);
  const setSelectedApiSource = useAppStore((state) => state.setSelectedApiSource);
  
  // 重置操作
  const resetModelsFilters = useAppStore((state) => state.resetModelsFilters);
  const resetApiSourcesFilters = useAppStore((state) => state.resetApiSourcesFilters);
  const resetModelsData = useAppStore((state) => state.resetModelsData);
  const resetApiSourcesData = useAppStore((state) => state.resetApiSourcesData);
  
  // 数据初始化
  const setDataInitialized = useAppStore((state) => state.setDataInitialized);
  
  // 刷新数据
  const refreshModelsData = useAppStore((state) => state.refreshModelsData);
  const refreshApiSourcesData = useAppStore((state) => state.refreshApiSourcesData);
  const refreshAllData = useAppStore((state) => state.refreshAllData);
  
  return {
    // 状态
    modelsData,
    modelsLoading,
    modelsError,
    apiSourcesData,
    apiSourcesLoading,
    apiSourcesError,
    activeTab,
    modelsSearchText,
    modelsTypeFilter,
    modelsVendorFilter,
    modelsStatusFilter,
    apiSourcesSearchText,
    apiSourcesTypeFilter,
    apiSourcesStatusFilter,
    modelsSorting,
    modelsPagination,
    modelsColumnFilters,
    modelsGlobalFilter,
    apiSourcesSorting,
    apiSourcesPagination,
    apiSourcesColumnFilters,
    apiSourcesGlobalFilter,
    isModelModalVisible,
    isApiSourceModalVisible,
    selectedModel,
    selectedApiSource,
    dataInitialized,
    modelsLastUpdated,
    
    // 操作
    setModelsData,
    addModel,
    updateModel,
    removeModel,
    toggleModelStatus,
    setApiSourcesData,
    addApiSource,
    updateApiSource,
    removeApiSource,
    toggleApiSourceStatus,
    setModelsLoading,
    setApiSourcesLoading,
    setModelsError,
    setApiSourcesError,
    setActiveTab,
    setModelsSearchText,
    setModelsTypeFilter,
    setModelsVendorFilter,
    setModelsStatusFilter,
    setModelsFilters,
    setApiSourcesSearchText,
    setApiSourcesTypeFilter,
    setApiSourcesStatusFilter,
    setApiSourcesFilters,
    setModelsSorting,
    setModelsPagination,
    setModelsColumnFilters,
    setModelsGlobalFilter,
    setApiSourcesSorting,
    setApiSourcesPagination,
    setApiSourcesColumnFilters,
    setApiSourcesGlobalFilter,
    setIsModelModalVisible,
    setIsApiSourceModalVisible,
    setSelectedModel,
    setSelectedApiSource,
    resetModelsFilters,
    resetApiSourcesFilters,
    resetModelsData,
    resetApiSourcesData,
    setDataInitialized,
    refreshModelsData,
    refreshApiSourcesData,
    refreshAllData,
  };
};

/**
 * 筛选后的模型列表选择器
 */
export const useFilteredModels = () => {
  const modelsData = useAppStore((state) => state.modelsData);
  const modelsSearchText = useAppStore((state) => state.modelsSearchText);
  const modelsTypeFilter = useAppStore((state) => state.modelsTypeFilter);
  const modelsVendorFilter = useAppStore((state) => state.modelsVendorFilter);
  const modelsStatusFilter = useAppStore((state) => state.modelsStatusFilter);
  
  return React.useMemo(() => {
    let filtered = modelsData;
    
    // 搜索文本筛选
    if (modelsSearchText && modelsSearchText.trim()) {
      const searchLower = modelsSearchText.toLowerCase().trim();
      filtered = filtered.filter(model =>
        model.name.toLowerCase().includes(searchLower) ||
        model.type.toLowerCase().includes(searchLower) ||
        model.vendor.toLowerCase().includes(searchLower)
      );
    }
    
    // 类型筛选
    if (modelsTypeFilter && modelsTypeFilter !== 'all') {
      filtered = filtered.filter(model => model.type === modelsTypeFilter);
    }
    
    // 供应商筛选
    if (modelsVendorFilter && modelsVendorFilter !== 'all') {
      filtered = filtered.filter(model => model.vendor === modelsVendorFilter);
    }
    
    // 状态筛选
    if (modelsStatusFilter && modelsStatusFilter !== 'all') {
      const isEnabled = modelsStatusFilter === 'enabled';
      filtered = filtered.filter(model => model.status === isEnabled);
    }
    
    return filtered;
  }, [modelsData, modelsSearchText, modelsTypeFilter, modelsVendorFilter, modelsStatusFilter]);
};

/**
 * 筛选后的API源列表选择器
 */
export const useFilteredApiSources = () => {
  const apiSourcesData = useAppStore((state) => state.apiSourcesData);
  const apiSourcesSearchText = useAppStore((state) => state.apiSourcesSearchText);
  const apiSourcesTypeFilter = useAppStore((state) => state.apiSourcesTypeFilter);
  const apiSourcesStatusFilter = useAppStore((state) => state.apiSourcesStatusFilter);
  
  return React.useMemo(() => {
    let filtered = apiSourcesData;
    
    // 搜索文本筛选
    if (apiSourcesSearchText && apiSourcesSearchText.trim()) {
      const searchLower = apiSourcesSearchText.toLowerCase().trim();
      filtered = filtered.filter(apiSource =>
        apiSource.name.toLowerCase().includes(searchLower) ||
        apiSource.type.toLowerCase().includes(searchLower) ||
        (apiSource.description && apiSource.description.toLowerCase().includes(searchLower))
      );
    }
    
    // 类型筛选
    if (apiSourcesTypeFilter && apiSourcesTypeFilter !== 'all') {
      filtered = filtered.filter(apiSource => apiSource.type === apiSourcesTypeFilter);
    }
    
    // 状态筛选
    if (apiSourcesStatusFilter && apiSourcesStatusFilter !== 'all') {
      filtered = filtered.filter(apiSource => apiSource.status === apiSourcesStatusFilter);
    }
    
    return filtered;
  }, [apiSourcesData, apiSourcesSearchText, apiSourcesTypeFilter, apiSourcesStatusFilter]);
};

/**
 * 模型统计信息选择器
 */
export const useModelsStats = () => {
  const modelsData = useAppStore((state) => state.modelsData);
  const filteredModels = useFilteredModels();
  
  return React.useMemo(() => {
    const total = modelsData.length;
    const filteredTotal = filteredModels.length;
    
    // 状态分布统计
    const enabledCount = modelsData.filter(model => model.status).length;
    const disabledCount = total - enabledCount;
    
    // 类型分布统计
    const typeStats = modelsData.reduce((acc, model) => {
      acc[model.type] = (acc[model.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 供应商分布统计
    const vendorStats = modelsData.reduce((acc, model) => {
      acc[model.vendor] = (acc[model.vendor] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 功能特性统计
    const featuresStats = {
      vision: modelsData.filter(model => model.enableVision).length,
      functionCalling: modelsData.filter(model => model.enableFunctionCalling).length,
      inference: modelsData.filter(model => model.enableInference).length,
      online: modelsData.filter(model => model.enableOnline).length,
    };
    
    return {
      total,
      filteredTotal,
      enabledCount,
      disabledCount,
      typeStats,
      vendorStats,
      featuresStats,
    };
  }, [modelsData, filteredModels]);
};

/**
 * API源统计信息选择器
 */
export const useApiSourcesStats = () => {
  const apiSourcesData = useAppStore((state) => state.apiSourcesData);
  const filteredApiSources = useFilteredApiSources();
  
  return React.useMemo(() => {
    const total = apiSourcesData.length;
    const filteredTotal = filteredApiSources.length;
    
    // 状态分布统计
    const statusStats = apiSourcesData.reduce((acc, apiSource) => {
      acc[apiSource.status] = (acc[apiSource.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 类型分布统计
    const typeStats = apiSourcesData.reduce((acc, apiSource) => {
      acc[apiSource.type] = (acc[apiSource.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return {
      total,
      filteredTotal,
      statusStats,
      typeStats,
    };
  }, [apiSourcesData, filteredApiSources]);
};

/**
 * 筛选后的在线用户列表选择器
 */
export const useFilteredOnlineUsers = () => {
  const onlineUsers = useAppStore((state) => state.onlineUsers);
  const statusFilter = useAppStore((state) => state.statusFilter);
  const osFilter = useAppStore((state) => state.osFilter);
  const locationFilter = useAppStore((state) => state.locationFilter);
  const searchText = useAppStore((state) => state.searchText);

  return React.useMemo(() => {
    let filtered = onlineUsers;

    // 状态筛选
    if (statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(user => user.状态 === statusFilter);
    }

    // 操作系统筛选
    if (osFilter && osFilter !== 'all') {
      filtered = filtered.filter(user => user.操作系统.toLowerCase().includes(osFilter.toLowerCase()));
    }

    // 登录地点筛选
    if (locationFilter && locationFilter !== 'all') {
      filtered = filtered.filter(user => user.登录地点.includes(locationFilter));
    }

    // 搜索文本筛选（用户名、IP、地点）
    if (searchText && searchText.trim()) {
      const searchLower = searchText.toLowerCase().trim();
      filtered = filtered.filter(user =>
        user.用户名.toLowerCase().includes(searchLower) ||
        user.登录IP.includes(searchLower) ||
        user.登录地点.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }, [onlineUsers, statusFilter, osFilter, locationFilter, searchText]);
};

/**
 * 在线用户统计信息选择器
 */
export const useOnlineUsersStats = () => {
  const onlineUsers = useAppStore((state) => state.onlineUsers);
  const filteredUsers = useFilteredOnlineUsers();

  return React.useMemo(() => {
    const total = onlineUsers.length;
    const filteredTotal = filteredUsers.length;

    // 状态分布统计
    const statusStats = onlineUsers.reduce((acc, user) => {
      acc[user.状态] = (acc[user.状态] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 操作系统分布统计
    const osStats = onlineUsers.reduce((acc, user) => {
      acc[user.操作系统] = (acc[user.操作系统] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // 浏览器分布统计
    const browserStats = onlineUsers.reduce((acc, user) => {
      acc[user.浏览器类型] = (acc[user.浏览器类型] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      filteredTotal,
      onlineCount: statusStats.online || 0,
      idleCount: statusStats.idle || 0,
      awayCount: statusStats.away || 0,
      statusStats,
      osStats,
      browserStats,
    };
  }, [onlineUsers, filteredUsers]);
};

/**
 * 助手管理相关选择器
 */
export const useAssistantManagementStore = () => {
  const assistants = useAppStore((state) => state.assistants);
  const categories = useAppStore((state) => state.categories);
  const currentAssistant = useAppStore((state) => state.currentAssistant);
  const assistantPagination = useAppStore((state) => state.assistantPagination);
  const assistantActiveTab = useAppStore((state) => state.assistantActiveTab);
  const assistantSearchKeyword = useAppStore((state) => state.assistantSearchKeyword);
  const assistantSelectedCategory = useAppStore((state) => state.assistantSelectedCategory);
  const assistantRagFilter = useAppStore((state) => state.assistantRagFilter);
  const assistantLoading = useAppStore((state) => state.assistantLoading);
  const assistantSaving = useAppStore((state) => state.assistantSaving);
  const assistantDeleting = useAppStore((state) => state.assistantDeleting);
  const assistantError = useAppStore((state) => state.assistantError);
  const formModalVisible = useAppStore((state) => state.formModalVisible);
  const detailModalVisible = useAppStore((state) => state.detailModalVisible);
  const categoryModalVisible = useAppStore((state) => state.categoryModalVisible);
  const editingAssistant = useAppStore((state) => state.editingAssistant);
  const selectedRowKeys = useAppStore((state) => state.selectedRowKeys);
  const assistantsLastUpdated = useAppStore((state) => state.assistantsLastUpdated);

  // Actions
  const loadAssistants = useAppStore((state) => state.loadAssistants);
  const loadAssistantById = useAppStore((state) => state.loadAssistantById);
  const createAssistant = useAppStore((state) => state.createAssistant);
  const updateAssistant = useAppStore((state) => state.updateAssistant);
  const deleteAssistant = useAppStore((state) => state.deleteAssistant);
  const duplicateAssistant = useAppStore((state) => state.duplicateAssistant);
  const loadCategories = useAppStore((state) => state.loadCategories);
  const createCategory = useAppStore((state) => state.createCategory);
  const updateCategory = useAppStore((state) => state.updateCategory);
  const deleteCategory = useAppStore((state) => state.deleteCategory);
  const setAssistantActiveTab = useAppStore((state) => state.setAssistantActiveTab);
  const setAssistantSearchKeyword = useAppStore((state) => state.setAssistantSearchKeyword);
  const setAssistantSelectedCategory = useAppStore((state) => state.setAssistantSelectedCategory);
  const setAssistantRagFilter = useAppStore((state) => state.setAssistantRagFilter);
  const setAssistantLoading = useAppStore((state) => state.setAssistantLoading);
  const setAssistantSaving = useAppStore((state) => state.setAssistantSaving);
  const setAssistantDeleting = useAppStore((state) => state.setAssistantDeleting);
  const setAssistantError = useAppStore((state) => state.setAssistantError);
  const setFormModalVisible = useAppStore((state) => state.setFormModalVisible);
  const setDetailModalVisible = useAppStore((state) => state.setDetailModalVisible);
  const setCategoryModalVisible = useAppStore((state) => state.setCategoryModalVisible);
  const setEditingAssistant = useAppStore((state) => state.setEditingAssistant);
  const setViewingAssistant = useAppStore((state) => state.setViewingAssistant);
  const setAssistantPagination = useAppStore((state) => state.setAssistantPagination);
  const setSelectedRowKeys = useAppStore((state) => state.setSelectedRowKeys);
  const resetAssistantManagement = useAppStore((state) => state.resetAssistantManagement);
  const markDirty = useAppStore((state) => state.markDirty);
  const refreshAssistantsData = useAppStore((state) => state.refreshAssistantsData);

  return {
    // State
    assistants,
    categories,
    currentAssistant,
    assistantPagination,
    assistantActiveTab,
    assistantSearchKeyword,
    assistantSelectedCategory,
    assistantRagFilter,
    assistantLoading,
    assistantSaving,
    assistantDeleting,
    assistantError,
    formModalVisible,
    detailModalVisible,
    categoryModalVisible,
    editingAssistant,
    selectedRowKeys,
    assistantsLastUpdated,
    // Actions
    loadAssistants,
    loadAssistantById,
    createAssistant,
    updateAssistant,
    deleteAssistant,
    duplicateAssistant,
    loadCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    setAssistantActiveTab,
    setAssistantSearchKeyword,
    setAssistantSelectedCategory,
    setAssistantRagFilter,
    setAssistantLoading,
    setAssistantSaving,
    setAssistantDeleting,
    setAssistantError,
    setFormModalVisible,
    setDetailModalVisible,
    setCategoryModalVisible,
    setEditingAssistant,
    setViewingAssistant,
    setAssistantPagination,
    setSelectedRowKeys,
    resetAssistantManagement,
    markDirty,
    refreshAssistantsData,
  };
};

/**
 * 知识库管理相关选择器
 */
export const useKnowledgeBaseManagementStore = () => {
  const knowledgeBases = useAppStore((state) => state.knowledgeBases);
  const loading = useAppStore((state) => state.loading);
  const saving = useAppStore((state) => state.saving);
  const deleting = useAppStore((state) => state.deleting);
  const ragTestLoading = useAppStore((state) => state.ragTestLoading);
  const error = useAppStore((state) => state.error);
  const searchKeyword = useAppStore((state) => state.searchKeyword);
  const selectedType = useAppStore((state) => state.selectedType);
  const selectedStatus = useAppStore((state) => state.selectedStatus);
  const formModalVisible = useAppStore((state) => state.formModalVisible);
  const detailModalVisible = useAppStore((state) => state.detailModalVisible);
  const documentModalVisible = useAppStore((state) => state.documentModalVisible);
  const editingKnowledgeBase = useAppStore((state) => state.editingKnowledgeBase);
  const viewingKnowledgeBase = useAppStore((state) => state.viewingKnowledgeBase);
  const kbActiveTab = useAppStore((state) => state.kbActiveTab);
  const selectedKnowledgeBase = useAppStore((state) => state.selectedKnowledgeBase);
  const testQuestion = useAppStore((state) => state.testQuestion);
  const ragTestResult = useAppStore((state) => state.ragTestResult);
  const ragTestResults = useAppStore((state) => state.ragTestResults);
  const ragTestStats = useAppStore((state) => state.ragTestStats);
  const kbPagination = useAppStore((state) => state.kbPagination);
  const kbLastUpdated = useAppStore((state) => state.kbLastUpdated);

  // Actions
  const loadKnowledgeBases = useAppStore((state) => state.loadKnowledgeBases);
  const refreshKnowledgeBases = useAppStore((state) => state.refreshKnowledgeBases);
  const createKnowledgeBase = useAppStore((state) => state.createKnowledgeBase);
  const updateKnowledgeBase = useAppStore((state) => state.updateKnowledgeBase);
  const deleteKnowledgeBase = useAppStore((state) => state.deleteKnowledgeBase);
  const batchDeleteKnowledgeBases = useAppStore((state) => state.batchDeleteKnowledgeBases);
  const performRAGTest = useAppStore((state) => state.performRAGTest);
  const setLoading = useAppStore((state) => state.setLoading);
  const setSaving = useAppStore((state) => state.setSaving);
  const setDeleting = useAppStore((state) => state.setDeleting);
  const setRagTestLoading = useAppStore((state) => state.setRagTestLoading);
  const setError = useAppStore((state) => state.setError);
  const setSearchKeyword = useAppStore((state) => state.setSearchKeyword);
  const setSelectedType = useAppStore((state) => state.setSelectedType);
  const setSelectedStatus = useAppStore((state) => state.setSelectedStatus);
  const setFormModalVisible = useAppStore((state) => state.setFormModalVisible);
  const setDetailModalVisible = useAppStore((state) => state.setDetailModalVisible);
  const setDocumentModalVisible = useAppStore((state) => state.setDocumentModalVisible);
  const setEditingKnowledgeBase = useAppStore((state) => state.setEditingKnowledgeBase);
  const setViewingKnowledgeBase = useAppStore((state) => state.setViewingKnowledgeBase);
  const setKbActiveTab = useAppStore((state) => state.setKbActiveTab);
  const setSelectedKnowledgeBase = useAppStore((state) => state.setSelectedKnowledgeBase);
  const setTestQuestion = useAppStore((state) => state.setTestQuestion);
  const setRagTestResult = useAppStore((state) => state.setRagTestResult);
  const setRagTestResults = useAppStore((state) => state.setRagTestResults);
  const setRagTestStats = useAppStore((state) => state.setRagTestStats);
  const setKbPagination = useAppStore((state) => state.setKbPagination);
  const resetKnowledgeBaseManagement = useAppStore((state) => state.resetKnowledgeBaseManagement);

  return {
    // State
    knowledgeBases,
    loading,
    saving,
    deleting,
    ragTestLoading,
    error,
    searchKeyword,
    selectedType,
    selectedStatus,
    formModalVisible,
    detailModalVisible,
    documentModalVisible,
    editingKnowledgeBase,
    viewingKnowledgeBase,
    kbActiveTab,
    selectedKnowledgeBase,
    testQuestion,
    ragTestResult,
    ragTestResults,
    ragTestStats,
    kbPagination,
    kbLastUpdated,
    // Actions
    loadKnowledgeBases,
    refreshKnowledgeBases,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase,
    batchDeleteKnowledgeBases,
    performRAGTest,
    setLoading,
    setSaving,
    setDeleting,
    setRagTestLoading,
    setError,
    setSearchKeyword,
    setSelectedType,
    setSelectedStatus,
    setFormModalVisible,
    setDetailModalVisible,
    setDocumentModalVisible,
    setEditingKnowledgeBase,
    setViewingKnowledgeBase,
    setKbActiveTab,
    setSelectedKnowledgeBase,
    setTestQuestion,
    setRagTestResult,
    setRagTestResults,
    setRagTestStats,
    setKbPagination,
    resetKnowledgeBaseManagement,
  };
};

/**
 * 主题管理相关 Hook
 */
export const useThemeStore = () => {
  const mode = useAppStore((state) => state.mode);
  const actualTheme = useAppStore((state) => state.actualTheme);
  const isTransitioning = useAppStore((state) => state.isTransitioning);
  const systemTheme = useAppStore((state) => state.systemTheme);
  const setThemeMode = useAppStore((state) => state.setThemeMode);
  const toggleTheme = useAppStore((state) => state.toggleTheme);
  const setActualTheme = useAppStore((state) => state.setActualTheme);
  const setTransitioning = useAppStore((state) => state.setTransitioning);
  const updateSystemTheme = useAppStore((state) => state.updateSystemTheme);
  const initializeTheme = useAppStore((state) => state.initializeTheme);

  return {
    mode,
    actualTheme,
    isTransitioning,
    systemTheme,
    setThemeMode,
    toggleTheme,
    setActualTheme,
    setTransitioning,
    updateSystemTheme,
    initializeTheme,
  };
};

/**
 * 订阅状态变化（调试用）
 */
export const subscribeToStore = (callback: (state: AppStore) => void) => {
  return useAppStore.subscribe(callback);
};

// 导出主 Store
export default useAppStore;
