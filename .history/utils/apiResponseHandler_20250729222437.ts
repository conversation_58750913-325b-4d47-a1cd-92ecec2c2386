// import { message } from 'antd'; // 移除未使用的导入

/**
 * API响应处理工具
 * 统一处理API响应格式和错误处理
 */

export interface ApiError {
  message: string;
  code?: number;
  requestId?: string;
}

export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

/**
 * 处理API响应，统一格式
 */
export function handleApiResponse<T = any>(response: any): ApiResult<T> {
  try {
    // 如果响应为空或无效
    if (!response) {
      return {
        success: false,
        error: { message: '服务器响应为空' }
      };
    }

    // 检查是否已经是处理过的响应格式
    if (response.success !== undefined) {
      return response;
    }

    // 标准化响应处理
    // 1. 新的响应格式 { success: true, code: 200, data: {...} }
    if (response.success === true && response.code === 200) {
      return {
        success: true,
        data: response.data
      };
    }

    // 2. 新的错误响应格式 { success: false, code: 401, message: "认证失败", data: { errors: [...] } }
    if (response.success === false && response.code !== 200) {
      return {
        success: false,
        error: {
          message: response.message || '请求失败',
          code: response.code
        }
      };
    }

    // 3. 直接成功响应（code: 200 或 code: 0）
    if (response.code === 200 || response.code === 0) {
      return {
        success: true,
        data: response.data
      };
    }

    // 4. 包装格式响应 ({ data: { code, message, data } })
    if (response.data && typeof response.data === 'object') {
      const innerResponse = response.data;
      if (innerResponse.code === 200 || innerResponse.code === 0) {
        return {
          success: true,
          data: innerResponse.data
        };
      }
      
      // 错误响应
      return {
        success: false,
        error: {
          message: innerResponse.message || '请求失败',
          code: innerResponse.code
        }
      };
    }

    // 5. 错误响应（code !== 200 && code !== 0）
    if (response.code && response.code !== 200 && response.code !== 0) {
      return {
        success: false,
        error: {
          message: response.message || '请求失败',
          code: response.code
        }
      };
    }

    // 6. 直接数据响应（没有code字段，直接返回数据）
    return {
      success: true,
      data: response
    };

  } catch (error) {
    console.error('处理API响应时出错:', error);
    return {
      success: false,
      error: { message: '响应处理失败' }
    };
  }
}

/**
 * 处理API错误
 */
export function handleApiError(error: any): void {
  console.error('API请求失败:', error);
  
  if (error.response) {
    // 服务器响应了错误状态码
    const status = error.response.status;
    const data = error.response.data;
    
    console.error('错误状态码:', status);
    console.error('错误响应数据:', data);
    
    // 根据状态码处理不同的错误
    switch (status) {
      case 401:
        console.error('未授权访问');
        break;
      case 403:
        console.error('权限不足');
        break;
      case 404:
        console.error('资源不存在');
        break;
      case 500:
        console.error('服务器内部错误');
        break;
      default:
        console.error('未知错误');
    }
  } else if (error.request) {
    // 请求发送了但没有收到响应
    console.error('网络错误，请检查网络连接');
  } else {
    // 其他错误
    console.error('请求设置错误:', error.message);
  }
}