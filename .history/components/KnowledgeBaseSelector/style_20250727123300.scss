@use '../../styles/variables' as *;

/**
 * 知识库选择器组件样式
 */
.knowledge-base-selector {
  .selector-container {
    .ant-select {
      width: 100%;

      .ant-select-selector {
        border-radius: $border-radius-base;
      }
    }

    .ant-select-multiple {
      .ant-select-selection-item {
        background-color: $primary-color;
        border-color: $primary-color;
        color: var(--theme-bg-primary);
        border-radius: $border-radius-sm;
      }
    }
  }

  .selected-items {
    margin-top: $spacing-md;

    .selected-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-md $spacing-md;
      background-color: var(--theme-bg-tertiary);
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;
      transition: background-color 0.2s ease, border-color 0.2s ease;

      .item-info {
        flex: 1;

        .item-name {
          font-weight: $font-weight-medium;
          color: var(--theme-text-primary);
          margin-bottom: $spacing-md;
          transition: color 0.2s ease;
        }

        .item-description {
          color: var(--theme-text-secondary);
          font-size: $font-size-sm;
          transition: color 0.2s ease;
        }
      }

      .item-actions {
        .ant-btn {
          border: none;
          box-shadow: none;
        }
      }
    }
  }

  .loading-state {
    text-align: center;
    padding: 16px;
    color: var(--theme-text-tertiary);
    transition: color 0.2s ease;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: var(--theme-text-tertiary);
    transition: color 0.2s ease;

    .empty-icon {
      font-size: 24px;
      margin-bottom: $spacing-md;
    }
  }
}
