@use '../../styles/variables' as *;

/**
 * 知识库选择器组件样式
 */
.knowledge-base-selector {
  .selector-container {
    .ant-select {
      width: 100%;

      .ant-select-selector {
        border-radius: $border-radius-base;
      }
    }

    .ant-select-multiple {
      .ant-select-selection-item {
        background-color: $primary-color;
        border-color: $primary-color;
        color: var(--theme-bg-primary);
        border-radius: $border-radius-sm;
      }
    }
  }

  .selected-items {
    margin-top: $spacing-md;

    .selected-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: $spacing-sm $spacing-md;
      background-color: $background-color-light;
      border: 1px solid $border-color-split;
      border-radius: $border-radius-base;
      margin-bottom: $spacing-sm;

      .item-info {
        flex: 1;

        .item-name {
          font-weight: $font-weight-medium;
          color: $text-color;
          margin-bottom: $spacing-xs;
        }

        .item-description {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }

      .item-actions {
        .ant-btn {
          border: none;
          box-shadow: none;
        }
      }
    }
  }

  .loading-state {
    text-align: center;
    padding: 16px;
    color: $text-color-disabled;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: $text-color-disabled;

    .empty-icon {
      font-size: 24px;
      margin-bottom: $spacing-sm;
    }
  }
}
