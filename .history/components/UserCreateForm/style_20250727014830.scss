/**
 * V2 Admin 用户创建表单 - 现代简洁风格
 */
@use '../../styles/variables' as *;

// 全局强制覆盖 Ant Design Modal 的 padding
.ant-modal-root,
body {
  .ant-modal-content {
    padding: 0 !important;
    
    .ant-modal-header {
      padding: 16px 28px !important;
    }
    
    .ant-modal-body {
      padding: 20px 28px !important;
    }
    
    .ant-modal-footer {
      padding: 16px 28px !important;
    }
  }
}

.user-create-modal {
  // 全局重置
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  // 最高优先级覆盖 Modal 内边距
  &.ant-modal-root .ant-modal-wrap .ant-modal .ant-modal-content,
  .ant-modal-content,
  .ant-modal-content.ant-modal-content {
    padding: 0 !important;
  }

  // 模态框基础样式
  .ant-modal {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--theme-shadow-2);
  }

  .ant-modal-content {
    border-radius: 12px;
    border: none;
    overflow: hidden;
    padding: 0 !important;
    background: var(--theme-bg-primary);
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
  }

  // 模态框头部
  .ant-modal-header {
    background: var(--theme-bg-primary);
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px 28px !important;
    margin: 0 !important;
    transition: background-color 0.2s ease, border-color 0.2s ease;

    .ant-modal-title {
      color: var(--theme-text-primary);
      font-size: 16px;
      font-weight: 600;
      line-height: 1.4;
      letter-spacing: -0.01em;
      transition: color 0.2s ease;
    }
  }

  // 模态框主体
  .ant-modal-body {
    padding: 20px 28px !important;
    margin: 0 !important;
    background: var(--theme-bg-primary);
    max-height: 650px;
    overflow-y: auto;
    transition: background-color 0.2s ease;

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--theme-text-tertiary);
      border-radius: 6px;

      &:hover {
        background: var(--theme-text-secondary);
      }
    }
  }

  // 模态框底部
  .ant-modal-footer {
    background: var(--theme-bg-primary);
    border-top: 1px solid var(--theme-border-color-split);
    padding: 16px 28px !important;
    margin: 0 !important;
    text-align: right;
    transition: background-color 0.2s ease, border-color 0.2s ease;

    .ant-btn {
      height: 36px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 8px;
      padding: 0 18px;
      transition: all 0.2s ease;

      &:not(:last-child) {
        margin-right: 12px;
      }

      &.ant-btn-default {
        border-color: var(--theme-border-color);
        color: var(--theme-text-secondary);

        &:hover {
          border-color: var(--theme-text-tertiary);
          color: var(--theme-text-primary);
          background: var(--theme-bg-secondary);
        }
      }
      
      &.ant-btn-primary {
        background: #1677ff;
        border-color: #1677ff;
        box-shadow: 0 2px 0 rgba(5, 145, 255, 0.1);
        
        &:hover {
          background: #4096ff;
          border-color: #4096ff;
        }
      }
    }
  }

  // 表单容器
  .user-form {
    background: var(--theme-bg-primary);
    border-radius: 8px;
    transition: background-color 0.2s ease;

    // 头像上传区域
    .avatar-form-item {
      margin-bottom: 28px;
      text-align: center;

      .ant-form-item-label {
        text-align: center;
        margin-bottom: 16px;
        
        label {
          font-size: 15px;
          color: var(--theme-text-primary);
          font-weight: 500;
          transition: color 0.2s ease;

          .ant-form-item-optional {
            font-size: 13px;
            color: var(--theme-text-tertiary);
            font-weight: normal;
            margin-left: 4px;
            transition: color 0.2s ease;
          }
        }
      }

      .avatar-upload-wrapper {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 240px;
        margin: 0 auto;
  
        .avatar-uploader {
          width: 100%;
          margin-bottom: 10px;
          text-align: center;
    
          .ant-upload {
            display: flex;
            justify-content: center;
            width: 100%;
          }
  
          .avatar-upload-area {
            width: 100px;
            height: 100px;
            border: 2px dashed var(--theme-border-color);
            border-radius: 50%;
            background: var(--theme-bg-tertiary);
            transition: border-color 0.3s ease, background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            margin: 0 auto;

            &:hover {
              border-color: $primary-color;
              background: rgba(24, 144, 255, 0.05);
              transform: translateY(-2px);
              box-shadow: 0 6px 16px rgba(22, 119, 255, 0.12);
            }
            
            &:active {
              transform: translateY(0);
              transition: transform 0.1s;
            }

            .ant-avatar {
              width: 96px;
              height: 96px;
              border-radius: 50%;
              object-fit: cover;
            }

            .avatar-placeholder {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              width: 100%;
              height: 100%;
              padding: 0 8px;

              .anticon {
                font-size: 24px;
                color: var(--theme-text-tertiary);
                margin-bottom: 6px;
                transition: all 0.3s ease;
              }

              .upload-text {
                font-size: 12px;
                color: var(--theme-text-secondary);
                transition: all 0.3s ease;
                text-align: center;
                line-height: 1.3;
              }
            }

            &:hover .avatar-placeholder {
              .anticon {
                color: #1677ff;
                transform: scale(1.1);
              }
              
              .upload-text {
                color: #1677ff;
              }
            }
            
            &::after {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: rgba(0, 0, 0, 0);
              transition: background 0.3s ease;
              border-radius: 50%;
              pointer-events: none;
            }
            
            &:hover::after {
              background: rgba(0, 0, 0, 0.02);
            }
          }
        }

        .upload-hint {
          font-size: 12px;
          color: #8c8c8c;
          margin-top: 8px;
          text-align: center;
          max-width: 200px;
          line-height: 1.4;
          background: #f9f9f9;
          padding: 6px 10px;
          border-radius: 6px;
          border: 1px solid #f0f0f0;
          
          &:before {
            content: "📝 ";
            font-size: 12px;
          }
        }
      }
    }

    // 表单项通用样式
    .ant-form-item {
      margin-bottom: 20px;

      .ant-form-item-label {
        margin-bottom: 6px;
        
        label {
          font-size: 14px;
          color: #262626;
          font-weight: 500;
          line-height: 1.4;
        }
      }
    }

    // 行布局
    .ant-row {
      margin-left: -8px;
      margin-right: -8px;
      
      .ant-col {
        padding-left: 8px;
        padding-right: 8px;
      }
    }

    // 输入框样式
    .ant-input,
    .ant-input-password {
      height: 38px;
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 0 14px;
      font-size: 14px;
      transition: all 0.3s ease;
      background: #fafafa;

      &:hover {
        border-color: #4096ff;
        background: #f5f5f5;
      }

      &:focus {
        border-color: #1677ff;
        box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        background: #fff;
      }
    }

    // 带图标输入框
    .ant-input-affix-wrapper {
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      padding: 0 14px;
      background: #fafafa;
      transition: all 0.3s ease;

      &:hover {
        border-color: #4096ff;
        background: #f5f5f5;
      }

      &:focus,
      &.ant-input-affix-wrapper-focused {
        border-color: #1677ff;
        box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        background: #fff;
      }

      .ant-input {
        border: none;
        box-shadow: none;
        padding: 0;
        height: 36px;
        background: transparent;
      }

      .ant-input-prefix {
        margin-right: 8px;
        
        .anticon {
          color: #bfbfbf;
          font-size: 15px;
          transition: color 0.3s ease;
        }
      }
      
      &:hover .ant-input-prefix .anticon,
      &:focus .ant-input-prefix .anticon {
        color: #1677ff;
      }
    }

    // 选择器样式
    .ant-select {
      .ant-select-selector {
        height: 38px;
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        padding: 0 14px;
        background: #fafafa;
        transition: all 0.3s ease;

        .ant-select-selection-item {
          line-height: 36px;
          font-size: 14px;
        }

        .ant-select-selection-placeholder {
          line-height: 36px;
          color: #bfbfbf;
          font-size: 14px;
        }
      }

      &:hover .ant-select-selector {
        border-color: #4096ff;
        background: #f5f5f5;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #1677ff;
        box-shadow: 0 0 0 2px rgba(22, 119, 255, 0.1);
        background: #fff;
      }
      
      .ant-select-arrow {
        color: #bfbfbf;
        transition: color 0.3s ease;
      }
      
      &:hover .ant-select-arrow,
      &.ant-select-focused .ant-select-arrow {
        color: #1677ff;
      }
    }

    // 开关样式
    .ant-switch {
      background: #d9d9d9;
      height: 20px;
      min-width: 40px;
      border-radius: 10px;
      
      .ant-switch-handle {
        width: 16px;
        height: 16px;
        top: 2px;
        left: 2px;
      }
      
      &.ant-switch-checked {
        background: #1677ff;
        
        .ant-switch-handle {
          left: calc(100% - 16px - 2px);
        }
      }
    }

    // 状态提示
    .status-hint {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 4px;
    }

    // 错误状态
    .ant-form-item-has-error {
      .ant-input,
      .ant-input-affix-wrapper,
      .ant-select .ant-select-selector {
        border-color: #ff4d4f;
        background: #fff2f0;
      }
    }

    .ant-form-item-explain-error {
      color: #ff4d4f;
      font-size: 12px;
      margin-top: 4px;
    }
  }

  // 主题样式现在通过CSS变量自动处理



}

// 下拉菜单样式
.ant-select-dropdown {
  border-radius: 8px;
  border: none;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  padding: 6px;

  .ant-select-item {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 6px;
    transition: all 0.2s ease;

    &:hover {
      background: #f5f5f5;
    }

    &.ant-select-item-option-selected {
      background: #e6f4ff;
      color: #1677ff;
      font-weight: 500;
    }
  }
}

// 下拉菜单主题现在通过CSS变量自动处理

// 响应式设计
@media (max-width: 768px) {
  .user-create-modal {
    .ant-modal {
      margin: 16px;
      max-width: calc(100vw - 32px);
    }

    .ant-modal-header,
    .ant-modal-body,
    .ant-modal-footer {
      padding: 16px !important;
    }

    .user-form {
      .ant-row .ant-col {
        width: 100%;
        padding: 0;
        margin-bottom: 6px;
      }
      
      .ant-form-item {
        margin-bottom: 16px;
      }
      
      .avatar-form-item {
        margin-bottom: 20px;
      }
      
      .ant-input,
      .ant-input-affix-wrapper,
      .ant-select .ant-select-selector {
        height: 36px;
        padding: 0 12px;
      }
      
      .ant-select .ant-select-selection-item,
      .ant-select .ant-select-selection-placeholder {
        line-height: 34px;
      }
      
      .ant-input-affix-wrapper .ant-input {
        height: 34px;
      }
    }
  }
}
