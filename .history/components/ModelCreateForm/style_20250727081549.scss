@use '../../styles/variables' as *;

// 模型创建表单样式
.model-create-form-modal {
  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: $spacing-md 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: var(--theme-text-primary);
    transition: color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .ant-modal-footer {
    border-top: 1px solid var(--theme-border-color-split);
    padding: $spacing-md 16px;
    text-align: right;
    transition: border-color 0.2s ease;
  }
}

.model-create-form {
  // 表单分组卡片
  .form-section {
    margin-bottom: $spacing-md;
    border-radius: $border-radius-base;
    box-shadow: $box-shadow-light;

    &:last-child {
      margin-bottom: 0;
    }

    .ant-card-head {
      border-bottom: 1px solid var(--theme-border-color-split);
      padding: 0 $spacing-md;
      min-height: 40px;
      transition: border-color 0.2s ease;

      .ant-card-head-title {
        font-size: $font-size-base;
        font-weight: $font-weight-medium;
        color: var(--theme-text-primary);
        padding: $spacing-sm 0;
        transition: color 0.2s ease;
      }

      .ant-card-extra {
        padding: $spacing-sm 0;
      }
    }

    .ant-card-body {
      padding: $spacing-md;
    }
  }

  // 表单项优化
  .ant-form-item {
    margin-bottom: $spacing-md;

    .ant-form-item-label {
      padding-bottom: $spacing-xs;

      > label {
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        color: $text-color-primary;
        height: auto;

        &.ant-form-item-required {
          &::before {
            color: $error-color;
          }
        }
      }
    }

    .ant-form-item-control {
      .ant-input,
      .ant-select-selector,
      .ant-input-number,
      .ant-select-single .ant-select-selector {
        border-radius: $border-radius-sm;
        border-color: $border-color-base;
        transition: $transition-base;

        &:hover {
          border-color: $primary-color;
        }

        &:focus,
        &.ant-select-focused .ant-select-selector {
          border-color: $primary-color;
          box-shadow: 0 0 0 2px rgba($primary-color, 0.1);
        }
      }

      .ant-input-group-addon {
        border-color: $border-color-base;
        background: $background-color-light;

        .ant-btn {
          border: none;
          background: transparent;
          color: $primary-color;
          font-size: $font-size-xs;

          &:hover {
            color: $primary-color-hover;
          }
        }
      }
    }

    .ant-form-item-extra {
      font-size: $font-size-xs;
      color: $text-color-tertiary;
      margin-top: $spacing-xs;
    }
  }

  // 能力选择区域
  .capability-group {
    .capability-card {
      border: 1px solid $border-color-light;
      border-radius: $border-radius-sm;
      transition: $transition-base;
      cursor: pointer;

      &:hover {
        border-color: $primary-color;
        box-shadow: 0 2px 8px rgba($primary-color, 0.15);
      }

      .ant-checkbox-wrapper {
        display: flex;
        align-items: flex-start;
        width: 100%;

        .ant-checkbox {
          margin-right: $spacing-sm;
          margin-top: 2px;
        }

        .capability-title {
          font-size: $font-size-sm;
          font-weight: $font-weight-medium;
          color: $text-color-primary;
          margin-bottom: 2px;
          display: flex;
          align-items: center;
          gap: $spacing-xs;
        }

        .capability-desc {
          font-size: $font-size-xs;
          color: $text-color-tertiary;
          line-height: $line-height-base;
        }
      }

      // 选中状态
      &:has(.ant-checkbox-checked) {
        border-color: $primary-color;
        background: rgba($primary-color, 0.02);
      }
    }
  }

  // 参数设置区域
  .parameter-row {
    margin-bottom: $spacing-sm;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    .ant-input {
      font-size: $font-size-sm;
    }

    .ant-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      width: 32px;
      padding: 0;
      border-radius: $border-radius-sm;

      &:hover {
        background: rgba($error-color, 0.1);
        border-color: $error-color;
      }
    }
  }

  .empty-parameters {
    text-align: center;
    padding: 16px 0;
    color: $text-color-tertiary;
    font-size: $font-size-sm;
    border: 1px dashed $border-color-light;
    border-radius: $border-radius-sm;
    background: $background-color-light;
  }

  // 开关组件
  .ant-switch {
    background: rgba($text-color-tertiary, 0.3);

    &.ant-switch-checked {
      background: $primary-color;
    }

    &:hover:not(.ant-switch-disabled) {
      background: rgba($primary-color, 0.8);
    }
  }

  // 文本域
  .ant-input {
    &.ant-input-textarea {
      .ant-input {
        resize: vertical;
        min-height: 80px;
      }
    }
  }

  // 选择器下拉
  .ant-select-dropdown {
    border-radius: $border-radius-base;
    box-shadow: $box-shadow-large;

    .ant-select-item {
      border-radius: $border-radius-sm;
      margin: 2px $spacing-xs;

      &:hover {
        background: rgba($primary-color, 0.08);
      }

      &.ant-select-item-option-selected {
        background: rgba($primary-color, 0.12);
        color: $primary-color;
        font-weight: $font-weight-medium;
      }
    }
  }

  // 工具提示
  .anticon-info-circle {
    cursor: help;
    transition: $transition-base;

    &:hover {
      color: $primary-color-hover;
    }
  }

  // 标签样式
  .ant-tag {
    border-radius: $border-radius-xs;
    font-size: $font-size-xs;
    padding: 2px 6px;
    margin: 0;
    border: none;
    background: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  // 折叠面板（如果使用）
  .ant-collapse {
    border: 1px solid $border-color-light;
    border-radius: $border-radius-base;
    background: transparent;

    .ant-collapse-header {
      padding: $spacing-sm $spacing-md !important;
      border-radius: $border-radius-sm !important;
      font-weight: $font-weight-medium;
      color: $text-color-primary;

      .ant-collapse-arrow {
        color: $text-color-secondary;
      }
    }

    .ant-collapse-content {
      border-top: 1px solid $border-color-split;

      .ant-collapse-content-box {
        padding: $spacing-md;
      }
    }

    .ant-collapse-item {
      border-bottom: none;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-sm) {
  .model-create-form-modal {
    .ant-modal {
      margin: $spacing-md;
      max-width: calc(100vw - 16px);
    }

    .ant-modal-body {
      padding: $spacing-md;
      max-height: 60vh;
    }
  }

  .model-create-form {
    .form-section {
      .ant-card-body {
        padding: $spacing-sm;
      }
    }

    .capability-group {
      .ant-col {
        span: 24 !important; // 移动端全宽显示
      }
    }

    .parameter-row {
      .ant-col:nth-child(1) {
        margin-bottom: $spacing-xs;
      }
      .ant-col:nth-child(2) {
        margin-bottom: $spacing-xs;
      }
    }
  }
}

// ===== 主题支持现在通过CSS变量自动处理 =====
