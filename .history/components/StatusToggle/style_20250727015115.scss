/**
 * V2 Admin 状态切换组件样式 - 简洁风格
 * 去掉圆角、阴影、渐变等装饰效果
 */
@use '../../styles/variables' as *;

.status-toggle {
  transition: all 0.2s ease;

  // 启用状态样式 - 简洁风格
  &.status-enabled {
    background: $success-color;
    border: none;

    &:hover {
      opacity: 0.8;
    }

    &:focus {
      outline: none;
    }

    // 开关手柄样式
    .ant-switch-handle::before {
      background: var(--theme-bg-primary);
    }
  }

  // 禁用状态样式 - 简洁风格
  &.status-disabled {
    background: $error-color;
    border: none;

    &:hover {
      opacity: 0.8;
    }

    &:focus {
      outline: none;
    }

    // 开关手柄样式
    .ant-switch-handle::before {
      background: var(--theme-bg-primary);
    }
  }

  // 禁用状态 - 简洁风格
  &.ant-switch-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: var(--theme-border-color) !important;

    .ant-switch-handle::before {
      background: var(--theme-text-tertiary);
    }
  }

  // 加载状态 - 简洁风格
  &.ant-switch-loading {
    opacity: 0.8;
    pointer-events: none;

    .ant-switch-loading-icon {
      color: var(--theme-bg-primary);
    }
  }

  // 暗色主题适配 - 简洁风格
  .v2-admin-app.dark & {
    &.status-enabled {
      background: #73d13d;
    }

    &.status-disabled {
      background: #ff7875;
    }

    &.ant-switch-disabled {
      background: #434343 !important;

      .ant-switch-handle::before {
        background: #595959;
      }
    }
  }
}
