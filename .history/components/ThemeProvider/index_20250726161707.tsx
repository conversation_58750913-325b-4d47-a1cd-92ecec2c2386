import React, { useMemo } from 'react';
import { ConfigProvider, theme } from 'antd';
import { useTheme } from '../../hooks/useTheme';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * 主题提供者组件
 * 使用Ant Design的ConfigProvider来应用主题
 */
const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { isDark } = useTheme();

  // Ant Design 主题配置
  const antdTheme = useMemo(() => {
    return {
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: {
        // 基础色彩
        colorPrimary: '#1677ff',
        borderRadius: 6,

        // 字体
        fontSize: 14,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',

        // 间距
        padding: 16,
        margin: 16,

        // 阴影
        boxShadow: isDark
          ? '0 2px 8px rgba(0, 0, 0, 0.45)'
          : '0 2px 8px rgba(0, 0, 0, 0.15)',
      },
      components: {
        Layout: {
          bodyBg: isDark ? '#141414' : '#f5f5f5',
          headerBg: isDark ? '#1f1f1f' : '#ffffff',
          siderBg: isDark ? '#1f1f1f' : '#ffffff',
        },
        Menu: {
          itemBg: 'transparent',
          subMenuItemBg: 'transparent',
          itemSelectedBg: isDark ? '#1677ff33' : '#e6f7ff',
          itemHoverBg: isDark ? '#ffffff0f' : '#f5f5f5',
        },
        Card: {
          headerBg: 'transparent',
          bodyPadding: 24,
        },
        Button: {
          borderRadius: 6,
        },
        Input: {
          borderRadius: 6,
        },
        Table: {
          headerBg: isDark ? '#262626' : '#fafafa',
        },
      },
    };
  }, [isDark]);

  return (
    <ConfigProvider theme={antdTheme}>
      {children}
    </ConfigProvider>
  );
};

export default ThemeProvider;
          bodyBg: isDark ? '#141414' : '#f5f5f5',
        },
        
        // 菜单组件
        Menu: {
          itemBg: 'transparent',
          itemSelectedBg: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
          itemHoverBg: isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)',
        },
        
        // 按钮组件
        Button: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },
        
        // 输入框组件
        Input: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },
        
        // 表格组件
        Table: {
          headerBg: isDark ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
          rowHoverBg: isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)',
        },
        
        // 卡片组件
        Card: {
          headerBg: 'transparent',
          bodyPadding: 24,
        },
        
        // 抽屉组件
        Drawer: {
          colorBgElevated: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 模态框组件
        Modal: {
          contentBg: isDark ? '#1f1f1f' : '#ffffff',
          headerBg: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 下拉菜单组件
        Dropdown: {
          colorBgElevated: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 工具提示组件
        Tooltip: {
          colorBgSpotlight: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        },
      },
    };
  }, [actualTheme]);

  return (
    <ConfigProvider theme={antdTheme}>
      <div className={`app-theme-wrapper ${isTransitioning ? 'theme-transitioning' : ''}`}>
        {children}
      </div>
    </ConfigProvider>
  );
};

export default ThemeProvider;
