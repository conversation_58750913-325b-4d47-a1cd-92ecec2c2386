import React, { useMemo, useEffect } from 'react';
import { ConfigProvider, theme } from 'antd';
import { useThemeStore } from '../../stores';

interface ThemeProviderProps {
  children: React.ReactNode;
}

/**
 * 主题提供者组件
 * 使用Ant Design的ConfigProvider来应用主题
 */
const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { isDark } = useTheme();

  // Ant Design 主题配置
  const antdTheme = useMemo(() => {
    return {
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: {
        // 基础色彩
        colorPrimary: '#1677ff',
        borderRadius: 6,

        // 字体
        fontSize: 14,
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',

        // 间距
        padding: 16,
        margin: 16,

        // 阴影
        boxShadow: isDark
          ? '0 2px 8px rgba(0, 0, 0, 0.45)'
          : '0 2px 8px rgba(0, 0, 0, 0.15)',
      },
      components: {
        Layout: {
          bodyBg: isDark ? '#141414' : '#f5f5f5',
          headerBg: isDark ? '#1f1f1f' : '#ffffff',
          siderBg: isDark ? '#1f1f1f' : '#ffffff',
        },
        Menu: {
          itemBg: 'transparent',
          subMenuItemBg: 'transparent',
          itemSelectedBg: isDark ? '#1677ff33' : '#e6f7ff',
          itemHoverBg: isDark ? '#ffffff0f' : '#f5f5f5',
        },
        Card: {
          headerBg: 'transparent',
          bodyPadding: 24,
        },
        Button: {
          borderRadius: 6,
        },
        Input: {
          borderRadius: 6,
        },
        Table: {
          headerBg: isDark ? '#262626' : '#fafafa',
        },
      },
    };
  }, [isDark]);

  return (
    <ConfigProvider theme={antdTheme}>
      {children}
    </ConfigProvider>
  );
};

export default ThemeProvider;
