import React, { useEffect, useMemo } from 'react';
import { ConfigProvider, theme } from 'antd';
import { useThemeStore } from '../../stores';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { actualTheme, isTransitioning, initializeTheme } = useThemeStore();

  // 初始化主题
  useEffect(() => {
    initializeTheme();
  }, [initializeTheme]);

  // 设置 HTML 根元素的 data-theme 属性
  useEffect(() => {
    const root = document.documentElement;
    root.setAttribute('data-theme', actualTheme);
    
    // 添加过渡类名
    if (isTransitioning) {
      root.classList.add('theme-transition');
      const timer = setTimeout(() => {
        root.classList.remove('theme-transition');
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [actualTheme, isTransitioning]);

  // Ant Design 主题配置
  const antdTheme = useMemo(() => {
    const isDark = actualTheme === 'dark';
    
    return {
      algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
      token: {
        // 基础色彩
        colorPrimary: '#1677ff',
        colorSuccess: '#52c41a',
        colorWarning: '#faad14',
        colorError: '#ff4d4f',
        colorInfo: '#1677ff',
        
        // 字体配置
        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif',
        fontSize: 14,
        fontSizeHeading1: 38,
        fontSizeHeading2: 30,
        fontSizeHeading3: 24,
        fontSizeHeading4: 20,
        fontSizeHeading5: 16,
        
        // 圆角配置
        borderRadius: 6,
        borderRadiusLG: 8,
        borderRadiusSM: 4,
        
        // 间距配置
        padding: 16,
        paddingLG: 24,
        paddingSM: 12,
        paddingXS: 8,
        
        // 阴影配置
        boxShadow: isDark 
          ? '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)'
          : '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
        
        // 动画配置
        motionDurationFast: '0.1s',
        motionDurationMid: '0.2s',
        motionDurationSlow: '0.3s',
      },
      components: {
        // 布局组件
        Layout: {
          headerBg: isDark ? '#001529' : '#ffffff',
          siderBg: isDark ? '#001529' : '#ffffff',
          bodyBg: isDark ? '#141414' : '#f5f5f5',
        },
        
        // 菜单组件
        Menu: {
          itemBg: 'transparent',
          itemSelectedBg: isDark ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
          itemHoverBg: isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)',
        },
        
        // 按钮组件
        Button: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },
        
        // 输入框组件
        Input: {
          borderRadius: 6,
          controlHeight: 32,
          controlHeightLG: 40,
          controlHeightSM: 24,
        },
        
        // 表格组件
        Table: {
          headerBg: isDark ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.02)',
          rowHoverBg: isDark ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)',
        },
        
        // 卡片组件
        Card: {
          headerBg: 'transparent',
          bodyPadding: 24,
        },
        
        // 抽屉组件
        Drawer: {
          colorBgElevated: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 模态框组件
        Modal: {
          contentBg: isDark ? '#1f1f1f' : '#ffffff',
          headerBg: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 下拉菜单组件
        Dropdown: {
          colorBgElevated: isDark ? '#1f1f1f' : '#ffffff',
        },
        
        // 工具提示组件
        Tooltip: {
          colorBgSpotlight: isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)',
        },
      },
    };
  }, [actualTheme]);

  return (
    <ConfigProvider theme={antdTheme}>
      <div className={`app-theme-wrapper ${isTransitioning ? 'theme-transitioning' : ''}`}>
        {children}
      </div>
    </ConfigProvider>
  );
};

export default ThemeProvider;
