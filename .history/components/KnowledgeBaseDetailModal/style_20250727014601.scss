@use '../../styles/variables' as *;

/**
 * 知识库详情模态框样式
 */
.knowledge-base-detail {
  // 模态框内容区域间距
  .ant-card {
    margin-bottom: $spacing-md; // 卡片之间的间距

    &:last-child {
      margin-bottom: 0; // 最后一个卡片不需要底部间距
    }
  }

  // 分隔线间距
  .ant-divider {
    margin: $spacing-md 0; // 分隔线上下间距
  }
  .ant-descriptions-item-label {
    font-weight: 500;
    color: var(--theme-text-secondary);
    transition: color 0.2s ease;
  }

  .ant-descriptions-item-content {
    color: var(--theme-text-primary);
    transition: color 0.2s ease;
  }

  .ant-card {
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-base;

    .ant-card-head {
      border-bottom: 1px solid var(--theme-border-color-split);
      transition: border-color 0.2s ease;

      .ant-card-head-title {
        font-weight: $font-weight-semibold;
        color: var(--theme-text-primary);
        transition: color 0.2s ease;
      }
    }
  }

  .ant-statistic {
    .ant-statistic-title {
      font-size: $font-size-sm;
      color: var(--theme-text-secondary);
      margin-bottom: $spacing-xs;
      transition: color 0.2s ease;
    }

    .ant-statistic-content {
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
    }
  }

  .ant-progress {
    .ant-progress-text {
      font-weight: $font-weight-medium;
    }
  }

  .ant-tag {
    border-radius: $border-radius-sm;
    font-weight: $font-weight-medium;
    
    &.ant-tag-blue {
      background-color: #e6f7ff;
      border-color: #91d5ff;
      color: #1890ff;
    }
    
    &.ant-tag-green {
      background-color: #f6ffed;
      border-color: #b7eb8f;
      color: #52c41a;
    }
    
    &.ant-tag-orange {
      background-color: #fff7e6;
      border-color: #ffd591;
      color: #fa8c16;
    }
    
    &.ant-tag-purple {
      background-color: #f9f0ff;
      border-color: #d3adf7;
      color: #722ed1;
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .knowledge-base-detail {
    .ant-descriptions {
      .ant-descriptions-item {
        padding-bottom: $spacing-md;
      }
    }

    .ant-statistic {
      text-align: center;
      margin-bottom: 16px;
    }
  }
}

// 暗色主题支持
.dark .knowledge-base-detail {
  .ant-card {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;
  }

  .ant-descriptions-item-label {
    color: $dark-text-color-secondary;
  }

  .ant-descriptions-item-content {
    color: $dark-text-color;
  }
}

// 动画效果
.knowledge-base-detail {
  .ant-card {
    transition: all 0.3s ease;

    &:hover {
      box-shadow: $box-shadow-hover;
    }
  }
}
