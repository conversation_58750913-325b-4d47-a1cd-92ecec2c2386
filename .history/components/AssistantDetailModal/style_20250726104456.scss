@use '../../styles/variables' as *;

/**
 * 助手详情模态框样式
 */
.assistant-detail-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid $border-color-split;
    padding: 16px;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .detail-section {
    margin-bottom: 16px;

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: $text-color;
      margin-bottom: $spacing-md;
    }

    .section-content {
      color: $text-color-secondary;
      line-height: $line-height-base;
    }
  }

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .knowledge-base-list {
    .ant-list-item {
      padding: $spacing-sm 0;
      border-bottom: 1px solid $border-color-split;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
