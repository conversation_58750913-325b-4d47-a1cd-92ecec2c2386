@use '../../styles/variables' as *;

/**
 * 助手详情模态框样式
 */
.assistant-detail-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .detail-section {
    margin-bottom: 16px;

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: var(--theme-text-primary);
      margin-bottom: $spacing-md;
      transition: color 0.2s ease;
    }

    .section-content {
      color: var(--theme-text-secondary);
      line-height: $line-height-base;
      transition: color 0.2s ease;
    }
  }

  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: $spacing-sm;
  }

  .knowledge-base-list {
    .ant-list-item {
      padding: $spacing-sm 0;
      border-bottom: 1px solid var(--theme-border-color-split);
      transition: border-color 0.2s ease;

      &:last-child {
        border-bottom: none;
      }
    }
  }
}
