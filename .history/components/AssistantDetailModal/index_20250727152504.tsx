import React from "react";
import {
  Modal,
  Descriptions,
  Tag,
  Button,
  Space,
  Statistic,
  Row,
  Col,
  Card,
} from "antd";
import {
  EditOutlined,
  CopyOutlined,
  DeleteOutlined,
  DatabaseOutlined,
} from "@ant-design/icons";
import type { Assistant, AssistantCategory } from "../../types/assistant";
import "./style.scss";

interface AssistantDetailModalProps {
  visible: boolean;
  assistant?: Assistant | null;
  categories: AssistantCategory[];
  onCancel: () => void;
  onEdit: (assistant: Assistant) => void;
  onDuplicate: (assistant: Assistant) => void;
  onDelete: (assistant: Assistant) => void;
}

const AssistantDetailModal: React.FC<AssistantDetailModalProps> = ({
  visible,
  assistant,
  categories,
  onCancel,
  onEdit,
  onDuplicate,
  onDelete,
}) => {
  if (!assistant) return null;

  const getCategoryLabel = (categoryKey: string) => {
    const category = categories.find((c) => c.key === categoryKey);
    return category ? category.label : categoryKey;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "k";
    }
    return num.toString();
  };

  return (
    <Modal
      title={
        <div style={{ display: "flex", alignItems: "center", gap: 12 }}>
          <span>{assistant.name}</span>
          <div>
            {assistant.is_system && (
              <Tag color="purple">系统预置</Tag>
            )}
            {assistant.is_public && (
              <Tag color="blue">公开</Tag>
            )}
            {assistant.rag_enabled && (
              <Tag color="green">
                <DatabaseOutlined /> RAG启用
              </Tag>
            )}
            <Tag color={assistant.is_active ? "green" : "red"}>
              {assistant.is_active ? "启用" : "禁用"}
            </Tag>
          </div>
        </div>
      }
      open={visible}
      onCancel={onCancel}
      width={900}
      footer={
        <Space>
          <Button onClick={onCancel}>关闭</Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => onEdit(assistant)}
          >
            编辑
          </Button>
          <Button
            icon={<CopyOutlined />}
            onClick={() => onDuplicate(assistant)}
          >
            复制
          </Button>
          <Button
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDelete(assistant)}
          >
            删除
          </Button>
        </Space>
      }
    >
      <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
        {/* 基础信息 */}
        <Card title="基础信息" style={{ marginBottom: 16 }}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="名称">
              {assistant.name}
            </Descriptions.Item>
            <Descriptions.Item label="分类">
              <Tag color="geekblue">
                {getCategoryLabel(assistant.category)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="模型">
              {assistant.model_id}
            </Descriptions.Item>
            <Descriptions.Item label="创建者">
              {assistant.created_by}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {formatDate(assistant.created_at)}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {formatDate(assistant.updated_at)}
            </Descriptions.Item>
          </Descriptions>
          <Descriptions column={1} size="small" style={{ marginTop: 16 }}>
            <Descriptions.Item label="描述">
              {assistant.description}
            </Descriptions.Item>
          </Descriptions>

          {/* 标签 */}
          {assistant.tags.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <strong>标签：</strong>
              <div style={{ marginTop: 8 }}>
                {assistant.tags.map((tag) => (
                  <Tag key={tag} color="blue">
                    {tag}
                  </Tag>
                ))}
              </div>
            </div>
          )}
        </Card>

        {/* 使用统计 */}
        <Card title="使用统计" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="使用次数"
                value={assistant.usage_count}
                formatter={(value) => formatNumber(Number(value))}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="消息数量"
                value={assistant.message_count}
                formatter={(value) => formatNumber(Number(value))}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="平均评分"
                value={assistant.average_rating}
                precision={1}
                suffix="/ 5.0"
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="评分次数"
                value={assistant.rating_count}
                formatter={(value) => formatNumber(Number(value))}
              />
            </Col>
          </Row>

          <Row gutter={16} style={{ marginTop: 16 }}>
            <Col span={6}>
              <Statistic
                title="总Token数"
                value={assistant.total_tokens}
                formatter={(value) => formatNumber(Number(value))}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="RAG查询次数"
                value={assistant.rag_query_count}
                formatter={(value) => formatNumber(Number(value))}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title="最后使用时间"
                value={assistant.last_used_at ? formatDate(assistant.last_used_at) : "从未使用"}
                valueStyle={{ fontSize: 14 }}
              />
            </Col>
          </Row>
        </Card>

        {/* 模型配置 */}
        <Card title="模型配置" style={{ marginBottom: 16 }}>
          <Descriptions column={2} size="small">
            <Descriptions.Item label="Temperature">
              {assistant.config.temperature}
            </Descriptions.Item>
            <Descriptions.Item label="Max Tokens">
              {assistant.config.max_tokens}
            </Descriptions.Item>
            <Descriptions.Item label="Top-P">
              {assistant.config.top_p || "未设置"}
            </Descriptions.Item>
            <Descriptions.Item label="频率惩罚">
              {assistant.config.frequency_penalty || "未设置"}
            </Descriptions.Item>
          </Descriptions>
          <Descriptions column={1} size="small" style={{ marginTop: 16 }}>
            <Descriptions.Item label="系统提示词">
              <div
                style={{
                  maxHeight: 200,
                  overflowY: "auto",
                  padding: 8,
                  backgroundColor: "#f5f5f5",
                  borderRadius: 4,
                  whiteSpace: "pre-wrap",
                }}
              >
                {assistant.config.system_prompt}
              </div>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 知识库信息 */}
        {assistant.rag_enabled && (
          <Card title="知识库配置" style={{ marginBottom: 16 }}>
            <Descriptions column={2} size="small">
              <Descriptions.Item label="RAG状态">
                <Tag color="green">已启用</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="绑定知识库数量">
                {assistant.knowledge_base_ids.length}
              </Descriptions.Item>
            </Descriptions>

            {assistant.config.rag_config && (
              <>
                <Descriptions column={2} size="small" style={{ marginTop: 16 }}>
                  <Descriptions.Item label="检索策略">
                    {assistant.config.rag_config.retrieval_strategy}
                  </Descriptions.Item>
                  <Descriptions.Item label="相似度阈值">
                    {assistant.config.rag_config.similarity_threshold}
                  </Descriptions.Item>
                  <Descriptions.Item label="检索数量">
                    {assistant.config.rag_config.top_k}
                  </Descriptions.Item>
                  <Descriptions.Item label="上下文长度">
                    {assistant.config.rag_config.max_context_length}
                  </Descriptions.Item>
                  <Descriptions.Item label="重排序">
                    <Tag color={assistant.config.rag_config.rerank_enabled ? "green" : "red"}>
                      {assistant.config.rag_config.rerank_enabled ? "启用" : "禁用"}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="引用来源">
                    <Tag color={assistant.config.rag_config.citation_enabled ? "green" : "red"}>
                      {assistant.config.rag_config.citation_enabled ? "显示" : "隐藏"}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              </>
            )}

            {assistant.knowledge_base_ids.length > 0 && (
              <div style={{ marginTop: 16 }}>
                <strong>绑定的知识库：</strong>
                <div style={{ marginTop: 8 }}>
                  {assistant.knowledge_base_ids.map((kbId) => (
                    <Tag key={kbId} color="blue" icon={<DatabaseOutlined />}>
                      {kbId}
                    </Tag>
                  ))}
                </div>
              </div>
            )}
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default AssistantDetailModal;
