import React, { useRef, useCallback, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';
import './style.scss';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 带有圆圈展开动画的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, setTheme, isDark, isTransitioning } = useThemeStore();
  const buttonRef = useRef<HTMLButtonElement>(null);

  /**
   * 创建真实的主题切换动画
   * 使用页面快照和遮罩层技术显示两个主题的实际内容差异
   */
  const createThemeTransitionAnimation = useCallback(async (event: React.MouseEvent, newTheme: 'light' | 'dark') => {
    if (!buttonRef.current) return;

    // 检查用户是否偏好减少动画
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setTheme(newTheme);
      return;
    }

    // 获取按钮位置和尺寸
    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算需要覆盖整个屏幕的圆圈半径
    const maxRadius = Math.sqrt(
      Math.pow(Math.max(centerX, window.innerWidth - centerX), 2) +
      Math.pow(Math.max(centerY, window.innerHeight - centerY), 2)
    ) + 50;

    try {
      // 先切换到新主题，让页面背景显示新主题
      setTheme(newTheme);

      // 等待一帧让新主题样式生效
      await new Promise(resolve => requestAnimationFrame(resolve));

      // 创建当前主题（旧主题）的预览层
      const previewLayer = document.createElement('div');
      previewLayer.className = 'theme-transition-preview';
      previewLayer.setAttribute('data-theme', isDark ? 'dark' : 'light'); // 使用旧主题

      // 临时切换回旧主题以获取旧主题的样式
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light');

      // 等待一帧让旧主题样式生效
      await new Promise(resolve => requestAnimationFrame(resolve));

      // 创建旧主题的页面快照
      const bodyClone = document.body.cloneNode(true) as HTMLElement;

      // 切换回新主题
      document.documentElement.setAttribute('data-theme', newTheme);

      // 设置预览层样式 - 初始时圆圈覆盖整个屏幕（显示旧主题）
      previewLayer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        pointer-events: none;
        overflow: hidden;
        clip-path: circle(${maxRadius}px at ${centerX}px ${centerY}px);
        transition: clip-path 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: clip-path;
        backface-visibility: hidden;
      `;

      // 设置克隆内容的样式
      bodyClone.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        overflow: hidden;
        transform: translateZ(0);
      `;

      // 添加克隆内容到预览层
      previewLayer.appendChild(bodyClone);

      // 添加到DOM
      document.body.appendChild(previewLayer);

      // 触发动画 - 圆圈收缩到按钮位置（逐渐显示新主题）
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          previewLayer.style.clipPath = `circle(0px at ${centerX}px ${centerY}px)`;
        });
      });

      // 动画完成后清理
      const cleanupTimer = setTimeout(() => {
        if (previewLayer.parentNode) {
          previewLayer.parentNode.removeChild(previewLayer);
        }
      }, 650);

      // 如果组件卸载，清理定时器
      return () => {
        clearTimeout(themeChangeTimer);
        clearTimeout(cleanupTimer);
        if (previewLayer.parentNode) {
          previewLayer.parentNode.removeChild(previewLayer);
        }
      };
    } catch (error) {
      console.warn('Theme transition animation failed, falling back to simple transition:', error);
      // 降级到简单的主题切换
      setTheme(newTheme);
    }
  }, [setTheme]);

  /**
   * 添加触觉反馈（如果支持）
   */
  const addHapticFeedback = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50); // 轻微震动50ms
    }
  }, []);

  /**
   * 处理主题切换
   */
  const handleToggleTheme = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    if (isTransitioning) return;

    // 添加触觉反馈
    addHapticFeedback();

    const newTheme = isDark ? 'light' : 'dark';

    // 对于键盘事件，创建一个模拟的鼠标事件
    if ('key' in event) {
      const rect = buttonRef.current?.getBoundingClientRect();
      if (rect) {
        const mockEvent = {
          clientX: rect.left + rect.width / 2,
          clientY: rect.top + rect.height / 2,
        } as React.MouseEvent;
        createThemeTransitionAnimation(mockEvent, newTheme);
      } else {
        setTheme(newTheme);
      }
    } else {
      createThemeTransitionAnimation(event, newTheme);
    }
  }, [isDark, isTransitioning, createThemeTransitionAnimation, addHapticFeedback, setTheme]);

  /**
   * 键盘事件处理
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggleTheme(event);
    }
  }, [handleToggleTheme]);

  /**
   * 全局键盘快捷键支持 (Ctrl/Cmd + Shift + T)
   */
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        if (!isTransitioning) {
          const rect = buttonRef.current?.getBoundingClientRect();
          if (rect) {
            const mockEvent = {
              clientX: rect.left + rect.width / 2,
              clientY: rect.top + rect.height / 2,
            } as React.MouseEvent;
            handleToggleTheme(mockEvent);
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleToggleTheme, isTransitioning]);

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题 (Ctrl+Shift+T)`}>
      <Button
        ref={buttonRef}
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={handleToggleTheme}
        onKeyDown={handleKeyDown}
        className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题，快捷键 Ctrl+Shift+T`}
        disabled={isTransitioning}
        tabIndex={0}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
