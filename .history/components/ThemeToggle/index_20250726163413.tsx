import React from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 简单的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, toggleTheme, isDark } = useThemeStore();

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题`}>
      <Button
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={toggleTheme}
        className={className}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题`}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
