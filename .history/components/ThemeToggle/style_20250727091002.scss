// 主题切换组件样式
.theme-toggle-button,
.theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--ant-color-fill-tertiary);
    transform: scale(1.05);
  }

  &.transitioning {
    .anticon {
      animation: themeIconSpin 0.6s ease-in-out;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .anticon {
    transition: transform 0.2s ease-in-out;
    position: relative;
    z-index: 1;
  }
}

.theme-toggle-dropdown {
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

// 主题选项样式
.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .theme-option-icon {
    display: flex;
    align-items: center;
    width: 16px;
    color: var(--ant-color-text-secondary);
  }

  .theme-option-label {
    flex: 1;
    color: var(--ant-color-text);
  }

  .theme-option-check {
    color: var(--ant-color-primary);
    font-size: 12px;
  }
}

// 主题切换动画
@keyframes themeIconSpin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(0.9);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
  }
  75% {
    transform: rotate(270deg) scale(0.9);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// ===== 脉冲动画已移除 =====

// ===== 圆圈动画相关样式已移除 =====
// 保留统一的过渡控制系统，但移除复杂的视觉动画效果

// ===== 旧的圆圈动画样式已移除 =====

// 主题过渡动画
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 页面级别的主题过渡
html {
  transition: background-color 0.3s ease;
}

body {
  transition: background-color 0.3s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    padding: 4px 8px;

    .ant-btn-icon {
      font-size: 14px;
    }
  }

  // 移动端优化圆圈动画
  .theme-transition-circle {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 减少动画的用户偏好设置
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      transform: none;
    }

    .anticon {
      animation: none;
    }
  }

  .theme-transition-circle {
    transition: none;
  }

  .theme-transition {
    transition: none;
  }
}

// 深色主题特定样式
[data-theme='dark'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // ===== 圆圈颜色样式已移除 =====
}

// 浅色主题特定样式
[data-theme='light'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // 亮色主题下的圆圈颜色
  .theme-transition-circle {
    &.to-light {
      background-color: #ffffff;
    }
    &.to-dark {
      background-color: #1f1f1f;
    }
  }
}
