// 主题切换组件样式 - 简化版
.theme-toggle-button,
.theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  border-radius: 6px;

  &:hover {
    background-color: var(--ant-color-fill-tertiary);
  }

  .anticon {
    transition: transform 0.2s ease-in-out;
  }
}

.theme-toggle-dropdown {
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

// 主题选项样式
.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .theme-option-icon {
    display: flex;
    align-items: center;
    width: 16px;
    color: var(--ant-color-text-secondary);
  }

  .theme-option-label {
    flex: 1;
    color: var(--ant-color-text);
  }

  .theme-option-check {
    color: var(--ant-color-primary);
    font-size: 12px;
  }
}

// ===== 所有主题切换动画已移除 =====
// 现在使用直接的主题切换，无动画效果

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    padding: 4px 8px;

    .ant-btn-icon {
      font-size: 14px;
    }
  }
}

// 深色主题特定样式
[data-theme='dark'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // ===== 圆圈颜色样式已移除 =====
}

// 浅色主题特定样式
[data-theme='light'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // ===== 圆圈颜色样式已移除 =====
}
