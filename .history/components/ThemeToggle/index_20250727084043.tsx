import React, { useRef, useCallback } from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';
import './style.scss';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 带有圆圈展开动画的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, setTheme, isDark, isTransitioning } = useThemeStore();
  const buttonRef = useRef<HTMLButtonElement>(null);

  /**
   * 创建圆圈展开动画
   */
  const createCircleAnimation = useCallback((event: React.MouseEvent, newTheme: 'light' | 'dark') => {
    if (!buttonRef.current) return;

    // 获取按钮位置和尺寸
    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算需要覆盖整个屏幕的圆圈半径
    const maxRadius = Math.sqrt(
      Math.pow(Math.max(centerX, window.innerWidth - centerX), 2) +
      Math.pow(Math.max(centerY, window.innerHeight - centerY), 2)
    );

    // 创建动画容器
    const animationContainer = document.createElement('div');
    animationContainer.className = 'theme-transition-container';

    // 创建圆圈元素
    const circle = document.createElement('div');
    circle.className = 'theme-transition-circle';

    // 设置圆圈的初始样式
    circle.style.cssText = `
      position: fixed;
      top: ${centerY}px;
      left: ${centerX}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background-color: ${newTheme === 'light' ? '#ffffff' : '#1f1f1f'};
      transform: translate(-50%, -50%);
      z-index: 9999;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    `;

    // 设置容器样式
    animationContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9998;
      pointer-events: none;
    `;

    // 添加到DOM
    animationContainer.appendChild(circle);
    document.body.appendChild(animationContainer);

    // 触发动画
    requestAnimationFrame(() => {
      circle.style.width = `${maxRadius * 2}px`;
      circle.style.height = `${maxRadius * 2}px`;
    });

    // 在动画进行到一半时切换主题
    setTimeout(() => {
      setTheme(newTheme);
    }, 300);

    // 动画完成后清理
    setTimeout(() => {
      if (animationContainer.parentNode) {
        animationContainer.parentNode.removeChild(animationContainer);
      }
    }, 600);
  }, [setTheme]);

  /**
   * 处理主题切换
   */
  const handleToggleTheme = useCallback((event: React.MouseEvent) => {
    if (isTransitioning) return;

    const newTheme = isDark ? 'light' : 'dark';
    createCircleAnimation(event, newTheme);
  }, [isDark, isTransitioning, createCircleAnimation]);

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题`}>
      <Button
        ref={buttonRef}
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={handleToggleTheme}
        className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题`}
        disabled={isTransitioning}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
