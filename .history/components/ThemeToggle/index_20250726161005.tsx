import React from 'react';
import { But<PERSON>, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useTheme } from '../../hooks/useTheme';

interface ThemeToggleProps {
  // 显示模式：button（按钮）或 dropdown（下拉菜单）
  mode?: 'button' | 'dropdown';
  // 按钮大小
  size?: 'small' | 'middle' | 'large';
  // 是否显示文本
  showText?: boolean;
  // 自定义类名
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  mode = 'dropdown',
  size = 'middle',
  showText = false,
  className = '',
}) => {
  const {
    mode: themeMode,
    actualTheme,
    isTransitioning,
    setThemeMode,
    toggleTheme,
    initializeTheme,
  } = useThemeStore();

  // 初始化主题
  useEffect(() => {
    initializeTheme();
  }, [initializeTheme]);

  // 主题选项配置
  const themeOptions = [
    {
      key: 'light' as ThemeMode,
      label: '浅色主题',
      icon: <SunOutlined />,
    },
    {
      key: 'dark' as ThemeMode,
      label: '深色主题',
      icon: <MoonOutlined />,
    },
    {
      key: 'auto' as ThemeMode,
      label: '跟随系统',
      icon: <DesktopOutlined />,
    },
  ];

  // 获取当前主题图标
  const getCurrentIcon = () => {
    if (themeMode === 'auto') {
      return <DesktopOutlined />;
    }
    return actualTheme === 'dark' ? <MoonOutlined /> : <SunOutlined />;
  };

  // 获取当前主题文本
  const getCurrentText = () => {
    const option = themeOptions.find(opt => opt.key === themeMode);
    return option?.label || '主题';
  };

  // 处理主题切换
  const handleThemeChange = (newMode: ThemeMode) => {
    setThemeMode(newMode);
  };

  // 下拉菜单项
  const dropdownItems = themeOptions.map(option => ({
    key: option.key,
    label: (
      <div className="theme-option">
        <span className="theme-option-icon">{option.icon}</span>
        <span className="theme-option-label">{option.label}</span>
        {themeMode === option.key && (
          <CheckOutlined className="theme-option-check" />
        )}
      </div>
    ),
    onClick: () => handleThemeChange(option.key),
  }));

  // 按钮模式：简单切换
  if (mode === 'button') {
    return (
      <Tooltip title={`切换到${actualTheme === 'dark' ? '浅色' : '深色'}主题`}>
        <Button
          type="text"
          size={size}
          icon={getCurrentIcon()}
          onClick={toggleTheme}
          loading={isTransitioning}
          className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        >
          {showText && getCurrentText()}
        </Button>
      </Tooltip>
    );
  }

  // 下拉菜单模式：完整选项
  return (
    <Dropdown
      menu={{ items: dropdownItems }}
      trigger={['click']}
      placement="bottomRight"
      className={`theme-toggle-dropdown ${className}`}
    >
      <Button
        type="text"
        size={size}
        icon={getCurrentIcon()}
        loading={isTransitioning}
        className={`theme-toggle-trigger ${isTransitioning ? 'transitioning' : ''}`}
      >
        {showText && getCurrentText()}
      </Button>
    </Dropdown>
  );
};

export default ThemeToggle;
