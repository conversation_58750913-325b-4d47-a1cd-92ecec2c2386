import React, { useRef, useCallback, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';
import './style.scss';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 带有圆圈展开动画的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, setTheme, isDark, isTransitioning } = useThemeStore();
  const buttonRef = useRef<HTMLButtonElement>(null);

  /**
   * 创建圆圈展开动画
   */
  const createCircleAnimation = useCallback((event: React.MouseEvent, newTheme: 'light' | 'dark') => {
    if (!buttonRef.current) return;

    // 检查用户是否偏好减少动画
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setTheme(newTheme);
      return;
    }

    // 获取按钮位置和尺寸
    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算需要覆盖整个屏幕的圆圈半径（添加一些余量确保完全覆盖）
    const maxRadius = Math.sqrt(
      Math.pow(Math.max(centerX, window.innerWidth - centerX), 2) +
      Math.pow(Math.max(centerY, window.innerHeight - centerY), 2)
    ) + 50;

    // 创建动画容器
    const animationContainer = document.createElement('div');
    animationContainer.className = 'theme-transition-container';

    // 创建圆圈元素
    const circle = document.createElement('div');
    circle.className = `theme-transition-circle to-${newTheme}`;

    // 设置圆圈的初始样式
    const circleColor = newTheme === 'light' ? '#ffffff' : '#1f1f1f';
    circle.style.cssText = `
      position: fixed;
      top: ${centerY}px;
      left: ${centerX}px;
      width: 0;
      height: 0;
      border-radius: 50%;
      background-color: ${circleColor};
      transform: translate(-50%, -50%);
      z-index: 9999;
      pointer-events: none;
      transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      will-change: width, height;
      backface-visibility: hidden;
    `;

    // 设置容器样式
    animationContainer.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 9998;
      pointer-events: none;
      overflow: hidden;
    `;

    // 添加到DOM
    animationContainer.appendChild(circle);
    document.body.appendChild(animationContainer);

    // 使用双重 requestAnimationFrame 确保样式已应用
    requestAnimationFrame(() => {
      requestAnimationFrame(() => {
        circle.style.width = `${maxRadius * 2}px`;
        circle.style.height = `${maxRadius * 2}px`;
      });
    });

    // 在动画进行到一半时切换主题
    const themeChangeTimer = setTimeout(() => {
      setTheme(newTheme);
    }, 300);

    // 动画完成后清理
    const cleanupTimer = setTimeout(() => {
      if (animationContainer.parentNode) {
        animationContainer.parentNode.removeChild(animationContainer);
      }
    }, 650);

    // 如果组件卸载，清理定时器
    return () => {
      clearTimeout(themeChangeTimer);
      clearTimeout(cleanupTimer);
      if (animationContainer.parentNode) {
        animationContainer.parentNode.removeChild(animationContainer);
      }
    };
  }, [setTheme]);

  /**
   * 添加触觉反馈（如果支持）
   */
  const addHapticFeedback = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50); // 轻微震动50ms
    }
  }, []);

  /**
   * 处理主题切换
   */
  const handleToggleTheme = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    if (isTransitioning) return;

    // 添加触觉反馈
    addHapticFeedback();

    const newTheme = isDark ? 'light' : 'dark';

    // 对于键盘事件，创建一个模拟的鼠标事件
    if ('key' in event) {
      const rect = buttonRef.current?.getBoundingClientRect();
      if (rect) {
        const mockEvent = {
          clientX: rect.left + rect.width / 2,
          clientY: rect.top + rect.height / 2,
        } as React.MouseEvent;
        createCircleAnimation(mockEvent, newTheme);
      } else {
        setTheme(newTheme);
      }
    } else {
      createCircleAnimation(event, newTheme);
    }
  }, [isDark, isTransitioning, createCircleAnimation, addHapticFeedback, setTheme]);

  /**
   * 键盘事件处理
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggleTheme(event);
    }
  }, [handleToggleTheme]);

  /**
   * 全局键盘快捷键支持 (Ctrl/Cmd + Shift + T)
   */
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        if (!isTransitioning) {
          const rect = buttonRef.current?.getBoundingClientRect();
          if (rect) {
            const mockEvent = {
              clientX: rect.left + rect.width / 2,
              clientY: rect.top + rect.height / 2,
            } as React.MouseEvent;
            handleToggleTheme(mockEvent);
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleToggleTheme, isTransitioning]);

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题`}>
      <Button
        ref={buttonRef}
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={handleToggleTheme}
        className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题`}
        disabled={isTransitioning}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
