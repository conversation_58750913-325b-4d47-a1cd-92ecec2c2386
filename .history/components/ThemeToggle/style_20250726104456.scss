// 主题切换组件样式
.theme-toggle-button,
.theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;

  &:hover {
    background-color: var(--ant-color-fill-tertiary);
  }

  &.transitioning {
    .anticon {
      animation: themeIconSpin 0.3s ease-in-out;
    }
  }

  .anticon {
    transition: transform 0.2s ease-in-out;
  }
}

.theme-toggle-dropdown {
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

// 主题选项样式
.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .theme-option-icon {
    display: flex;
    align-items: center;
    width: 16px;
    color: var(--ant-color-text-secondary);
  }

  .theme-option-label {
    flex: 1;
    color: var(--ant-color-text);
  }

  .theme-option-check {
    color: var(--ant-color-primary);
    font-size: 12px;
  }
}

// 主题切换动画
@keyframes themeIconSpin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// 主题过渡动画
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    padding: 4px 8px;
    
    .ant-btn-icon {
      font-size: 14px;
    }
  }
}

// 深色主题特定样式
[data-theme='dark'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: rgba(255, 255, 255, 0.65);
    }

    .theme-option-label {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

// 浅色主题特定样式
[data-theme='light'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: rgba(0, 0, 0, 0.45);
    }

    .theme-option-label {
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
