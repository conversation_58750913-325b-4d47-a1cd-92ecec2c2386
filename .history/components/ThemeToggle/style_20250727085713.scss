// 主题切换组件样式
.theme-toggle-button,
.theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  position: relative;
  overflow: hidden;

  &:hover {
    background-color: var(--ant-color-fill-tertiary);
    transform: scale(1.05);
  }

  &.transitioning {
    .anticon {
      animation: themeIconSpin 0.6s ease-in-out;
    }
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .anticon {
    transition: transform 0.2s ease-in-out;
    position: relative;
    z-index: 1;
  }
}

.theme-toggle-dropdown {
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    gap: 6px;
  }
}

// 主题选项样式
.theme-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  min-width: 120px;

  .theme-option-icon {
    display: flex;
    align-items: center;
    width: 16px;
    color: var(--ant-color-text-secondary);
  }

  .theme-option-label {
    flex: 1;
    color: var(--ant-color-text);
  }

  .theme-option-check {
    color: var(--ant-color-primary);
    font-size: 12px;
  }
}

// 主题切换动画
@keyframes themeIconSpin {
  0% {
    transform: rotate(0deg) scale(1);
  }
  25% {
    transform: rotate(90deg) scale(0.9);
  }
  50% {
    transform: rotate(180deg) scale(0.8);
  }
  75% {
    transform: rotate(270deg) scale(0.9);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 0.8;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.4;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

// 主题切换预览层
.theme-transition-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  will-change: clip-path;

  // 使用更流畅的缓动函数
  transition: clip-path 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 硬件加速
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);

  // 确保预览层在最顶层
  overflow: hidden;

  // 预览层内容样式
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    transform: translateZ(0);

    // 禁用所有交互
    pointer-events: none;
    user-select: none;

    // 确保文字和图像清晰
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

// 主题切换遮罩层（备用方案）
.theme-transition-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  pointer-events: none;
  will-change: clip-path;

  transition: clip-path 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backface-visibility: hidden;
  perspective: 1000px;
  overflow: hidden;
}

// 兼容旧的圆圈动画（保留向后兼容性）
.theme-transition-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9998;
  pointer-events: none;
}

.theme-transition-circle {
  position: fixed;
  border-radius: 50%;
  pointer-events: none;
  will-change: width, height, transform;

  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform: translate(-50%, -50%);
  backface-visibility: hidden;
  perspective: 1000px;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: inherit;
    transform: translate(-50%, -50%);
    animation: pulse 0.3s ease-out;
    opacity: 0.6;
  }
}

// 主题过渡动画
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

// 页面级别的主题过渡
html {
  transition: background-color 0.3s ease;
}

body {
  transition: background-color 0.3s ease;
}

// 响应式设计
@media (max-width: 768px) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    padding: 4px 8px;

    .ant-btn-icon {
      font-size: 14px;
    }
  }

  // 移动端优化圆圈动画
  .theme-transition-circle {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 减少动画的用户偏好设置
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      transform: none;
    }

    .anticon {
      animation: none;
    }
  }

  .theme-transition-circle {
    transition: none;
  }

  .theme-transition {
    transition: none;
  }
}

// 深色主题特定样式
[data-theme='dark'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // 暗色主题下的圆圈颜色
  .theme-transition-circle {
    &.to-light {
      background-color: #ffffff;
    }
    &.to-dark {
      background-color: #1f1f1f;
    }
  }
}

// 浅色主题特定样式
[data-theme='light'] {
  .theme-toggle-button,
  .theme-toggle-trigger {
    &:hover {
      background-color: var(--theme-bg-tertiary);
    }
  }

  .theme-option {
    .theme-option-icon {
      color: var(--theme-text-secondary);
    }

    .theme-option-label {
      color: var(--theme-text-primary);
    }
  }

  // 亮色主题下的圆圈颜色
  .theme-transition-circle {
    &.to-light {
      background-color: #ffffff;
    }
    &.to-dark {
      background-color: #1f1f1f;
    }
  }
}
