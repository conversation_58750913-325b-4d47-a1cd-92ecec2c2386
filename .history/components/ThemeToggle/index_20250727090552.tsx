import React, { useRef, useCallback, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useThemeStore } from '../../stores';
import './style.scss';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 带有圆圈展开动画的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, setTheme, isDark, isTransitioning, startThemeTransition } = useThemeStore();
  const buttonRef = useRef<HTMLButtonElement>(null);

  /**
   * 创建统一的主题切换动画
   * 解决组件样式切换不同步的问题
   */
  const createUnifiedThemeTransition = useCallback(async (event: React.MouseEvent, newTheme: 'light' | 'dark') => {
    if (!buttonRef.current) return;

    // 检查用户是否偏好减少动画
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setTheme(newTheme);
      return;
    }

    // 获取按钮位置和尺寸
    const button = buttonRef.current;
    const rect = button.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算需要覆盖整个屏幕的圆圈半径
    const maxRadius = Math.sqrt(
      Math.pow(Math.max(centerX, window.innerWidth - centerX), 2) +
      Math.pow(Math.max(centerY, window.innerHeight - centerY), 2)
    ) + 50;

    try {
      // 创建遮罩层，提供视觉缓冲
      const maskLayer = document.createElement('div');
      maskLayer.className = 'theme-transition-mask';

      // 设置遮罩层样式
      maskLayer.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: 9999;
        pointer-events: none;
        background: ${newTheme === 'light' ? '#ffffff' : '#1f1f1f'};
        clip-path: circle(0px at ${centerX}px ${centerY}px);
        transition: clip-path 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        will-change: clip-path;
        backface-visibility: hidden;
      `;

      // 添加到DOM
      document.body.appendChild(maskLayer);

      // 开始统一的主题切换过程
      const transitionPromise = startThemeTransition(newTheme);

      // 同时触发遮罩动画
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          maskLayer.style.clipPath = `circle(${maxRadius}px at ${centerX}px ${centerY}px)`;
        });
      });

      // 等待主题切换完成
      await transitionPromise;

      // 清理遮罩层
      setTimeout(() => {
        if (maskLayer.parentNode) {
          maskLayer.parentNode.removeChild(maskLayer);
        }
      }, 100);

    } catch (error) {
      console.warn('Unified theme transition failed, falling back to simple transition:', error);
      // 降级到简单的主题切换
      setTheme(newTheme);
    }
  }, [setTheme, startThemeTransition]);

  /**
   * 添加触觉反馈（如果支持）
   */
  const addHapticFeedback = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50); // 轻微震动50ms
    }
  }, []);

  /**
   * 处理主题切换
   */
  const handleToggleTheme = useCallback((event: React.MouseEvent | React.KeyboardEvent) => {
    if (isTransitioning) return;

    // 添加触觉反馈
    addHapticFeedback();

    const newTheme = isDark ? 'light' : 'dark';

    // 对于键盘事件，创建一个模拟的鼠标事件
    if ('key' in event) {
      const rect = buttonRef.current?.getBoundingClientRect();
      if (rect) {
        const mockEvent = {
          clientX: rect.left + rect.width / 2,
          clientY: rect.top + rect.height / 2,
        } as React.MouseEvent;
        createUnifiedThemeTransition(mockEvent, newTheme);
      } else {
        setTheme(newTheme);
      }
    } else {
      createUnifiedThemeTransition(event, newTheme);
    }
  }, [isDark, isTransitioning, createThemeTransitionAnimation, addHapticFeedback, setTheme]);

  /**
   * 键盘事件处理
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggleTheme(event);
    }
  }, [handleToggleTheme]);

  /**
   * 全局键盘快捷键支持 (Ctrl/Cmd + Shift + T)
   */
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        if (!isTransitioning) {
          const rect = buttonRef.current?.getBoundingClientRect();
          if (rect) {
            const mockEvent = {
              clientX: rect.left + rect.width / 2,
              clientY: rect.top + rect.height / 2,
            } as React.MouseEvent;
            createUnifiedThemeTransition(mockEvent, isDark ? 'light' : 'dark');
          }
        }
      }
    };

    document.addEventListener('keydown', handleGlobalKeyDown);
    return () => {
      document.removeEventListener('keydown', handleGlobalKeyDown);
    };
  }, [handleToggleTheme, isTransitioning]);

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题 (Ctrl+Shift+T)`}>
      <Button
        ref={buttonRef}
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={handleToggleTheme}
        onKeyDown={handleKeyDown}
        className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题，快捷键 Ctrl+Shift+T`}
        disabled={isTransitioning}
        tabIndex={0}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
