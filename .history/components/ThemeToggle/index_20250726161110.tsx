import React from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useTheme } from '../../hooks/useTheme';

interface ThemeToggleProps {
  className?: string;
  size?: 'small' | 'middle' | 'large';
}

/**
 * 主题切换按钮组件
 * 简单的亮色/暗色主题切换
 */
const ThemeToggle: React.FC<ThemeToggleProps> = ({
  className = '',
  size = 'middle'
}) => {
  const { theme, toggleTheme, isDark } = useTheme();

  return (
    <Tooltip title={`切换到${isDark ? '亮色' : '暗色'}主题`}>
      <Button
        type="text"
        size={size}
        icon={isDark ? <SunOutlined /> : <MoonOutlined />}
        onClick={toggleTheme}
        className={className}
        aria-label={`切换到${isDark ? '亮色' : '暗色'}主题`}
      />
    </Tooltip>
  );
  };

  // 获取当前主题文本
  const getCurrentText = () => {
    const option = themeOptions.find(opt => opt.key === themeMode);
    return option?.label || '主题';
  };

  // 处理主题切换
  const handleThemeChange = (newMode: ThemeMode) => {
    setThemeMode(newMode);
  };

  // 下拉菜单项
  const dropdownItems = themeOptions.map(option => ({
    key: option.key,
    label: (
      <div className="theme-option">
        <span className="theme-option-icon">{option.icon}</span>
        <span className="theme-option-label">{option.label}</span>
        {themeMode === option.key && (
          <CheckOutlined className="theme-option-check" />
        )}
      </div>
    ),
    onClick: () => handleThemeChange(option.key),
  }));

  // 按钮模式：简单切换
  if (mode === 'button') {
    return (
      <Tooltip title={`切换到${actualTheme === 'dark' ? '浅色' : '深色'}主题`}>
        <Button
          type="text"
          size={size}
          icon={getCurrentIcon()}
          onClick={toggleTheme}
          loading={isTransitioning}
          className={`theme-toggle-button ${className} ${isTransitioning ? 'transitioning' : ''}`}
        >
          {showText && getCurrentText()}
        </Button>
      </Tooltip>
    );
  }

  // 下拉菜单模式：完整选项
  return (
    <Dropdown
      menu={{ items: dropdownItems }}
      trigger={['click']}
      placement="bottomRight"
      className={`theme-toggle-dropdown ${className}`}
    >
      <Button
        type="text"
        size={size}
        icon={getCurrentIcon()}
        loading={isTransitioning}
        className={`theme-toggle-trigger ${isTransitioning ? 'transitioning' : ''}`}
      >
        {showText && getCurrentText()}
      </Button>
    </Dropdown>
  );
};

export default ThemeToggle;
