
@use '../../styles/variables' as *;

.user-avatar-wrapper {
  display: flex;
  align-items: center;
  height: 32px; // 固定高度48px，与demo完全一致
  padding: 0 3px; 
  cursor: pointer;
  transition: all 0.3s ease; // 使用0.3s过渡，与demo的transition-all-300一致
  border-radius: $border-radius-md;

  &:hover {
    background: var(--theme-bg-tertiary);
  }

  .ant-avatar {
    margin-right: 5px; // 头像右边距8px，与demo的mr-8px一致
    flex-shrink: 0; // 防止头像被压缩

    .anticon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .anticon {
    // 用户名样式，使用demo中的anticon类名
    font-size: 15px;
    color: var(--theme-text-primary);
    font-weight: 500;
    max-width: 100px;
    transition: color 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
  }
}


// ===== 主题支持现在通过CSS变量自动处理 =====
