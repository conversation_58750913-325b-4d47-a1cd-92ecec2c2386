
@use '../../styles/variables' as *;

.user-avatar-wrapper {
  display: flex;
  align-items: center;
  height: 32px; // 固定高度48px，与demo完全一致
  padding: 0 3px; 
  cursor: pointer;
  transition: all 0.3s ease; // 使用0.3s过渡，与demo的transition-all-300一致
  border-radius: $border-radius-md;

  &:hover {
    background: var(--hover-color, rgba(0, 0, 0, 0.04)); // 使用CSS变量，与demo完全一致
  }

  .ant-avatar {
    margin-right: 5px; // 头像右边距8px，与demo的mr-8px一致
    flex-shrink: 0; // 防止头像被压缩

    .anticon {
      font-size: 16px;
      width: 16px;
      height: 16px;
    }
  }

  .anticon {
    // 用户名样式，使用demo中的anticon类名
    font-size: 15px;
    color: inherit;
    font-weight: 500;
    max-width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1;
  }
}


// 暗色主题适配
.dark {
  .user-avatar-wrapper {
    color: rgba(255, 255, 255, 0.85);

    &:hover {
      background: var(--hover-color, rgba(255, 255, 255, 0.08)); // 暗色主题悬停背景
    }

    .anticon {
      color: rgba(255, 255, 255, 0.85); // 暗色主题下的用户名颜色
    }
  }
}

// CSS变量定义，支持主题切换
:root {
  --hover-color: rgba(0, 0, 0, 0.04);
}

// 暗色主题CSS变量
.dark {
  --hover-color: rgba(255, 255, 255, 0.08);
}
