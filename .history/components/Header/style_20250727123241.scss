@use '../../styles/variables' as *;

// V2 Header容器样式 - 固定顶部布局
.v2-header-container {
  position: relative;
  z-index: $z-index-sticky;
  background: $header-bg-light;
  border-bottom: 1px solid rgba(5, 5, 5, 0.06);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.2s ease;
  // 确保Header固定，不参与滚动
  flex-shrink: 0;

  // 移动端特殊处理：降低z-index，确保不遮挡Drawer
  @media (max-width: 768px) {
    z-index: 999;
  }

  // V2 顶部导航栏样式 - 使用父级限定提升优先级
  .v2-header {
    height: $header-height;
    background: transparent;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 0 10px; // 左边10px，右边16px
    position: relative;

    // 左侧区域
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
      min-width: 0;

      .collapse-button {
        width: 32px;
        height: 32px;
        border-radius: $border-radius-base;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;

        &:hover {
          background: $background-color-light;
          color: $primary-color;
          transform: translateY(-1px);
        }

        .anticon {
          font-size: 16px;
        }
      }

      .header-content {
        flex: 1;
        min-width: 0;
      }

      // 面包屑容器
      .breadcrumb-container {
        display: flex;
        flex-direction: column;
        gap: $spacing-md;

        .recent-visits-container {
          display: flex;
          align-items: center;
        }
      }

      .header-title {
        font-size: $font-size-lg;
        font-weight: $font-weight-semibold;
        color: $text-color;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        transition: all 0.2s ease;
      }
    }

    // 右侧区域
    .header-right {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      gap: 0; // 移除默认间距，由子元素自己控制
    }

    // 面包屑容器样式
    .breadcrumb-container {
      width: 100%;
    }
  }
}

// 独立的标签页历史记录区域样式
.tabs-history-area {
  background: $background-color-white;
  border-bottom: 1px solid $border-color-split;
  padding: 0;
  position: relative;

  // 确保标签页历史记录在所有屏幕尺寸下都可见
  display: block;
  width: 100%;

  // 与顶部导航栏的分隔线
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(5, 5, 5, 0.03);
  }

  // 面包屑样式
  .header-breadcrumb {
    margin-bottom: 0;

    .ant-breadcrumb-link {
      color: $text-color-secondary;
      // 移除所有交互动画和悬停效果
    }

    .ant-breadcrumb-separator {
      color: $text-color-disabled;
      // 移除过渡动画
    }

    .breadcrumb-icon {
      margin-right: $spacing-xs;
    }
  }

  // 操作按钮区域
  .header-actions {
    display: flex;
    align-items: center;
    gap: 4px; // 减小按钮间距，使布局更紧凑

    .action-button {
      width: 32px;
      height: 32px;
      border-radius: $border-radius-base;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      color: $text-color-secondary;

      &:hover {
        background: $background-color-light;
        color: $primary-color;
        transform: translateY(-1px);
      }

      &:active {
        transform: translateY(0) scale(0.95);
      }

      .anticon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }
    }

    // 通知徽章样式优化 - 小红点
    .ant-badge {
      .ant-badge-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: $error-color;
        border: 1px solid $background-color-white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        top: 2px;
        right: 2px;
      }

      // 保留原有数字徽章样式（备用）
      .ant-badge-count {
        font-size: $font-size-xs;
        height: 16px;
        min-width: 16px;
        line-height: 16px;
        padding: 0 4px;
        border-radius: $border-radius-lg;
      }
    }
  }


}

// 移动端适配
@media (max-width: $breakpoint-sm) {
  .v2-header {
    height: $header-height-mobile;
    padding: 0 $spacing-md;

    .header-left {
      gap: $spacing-md;

      .header-title {
        font-size: $font-size-base;
      }

      .collapse-button {
        width: 28px;
        height: 28px;

        .anticon {
          font-size: 14px;
        }
      }
    }

    .header-actions {
      gap: 2px; // 移动端进一步减小间距

      .action-button {
        width: 28px;
        height: 28px;

        .anticon {
          font-size: 14px;
        }
      }

      // 移动端通知徽章优化
      .ant-badge {
        .ant-badge-dot {
          width: 5px;
          height: 5px;
          top: 1px;
          right: 1px;
        }
      }
    }

    .user-info {
      padding: $spacing-xs;

      .user-name {
        display: none;
      }
    }
  }
}

// 平板端适配
@media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md) {
  .v2-header {
    .header-breadcrumb {
      .ant-breadcrumb-link {
        max-width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
      }
    }

    .user-info {
      .user-name {
        max-width: 80px;
      }
    }
  }
}

// 暗色主题
.v2-header.dark {
  background: $header-bg-dark;
  border-bottom-color: rgba(255, 255, 255, 0.1);

  .header-left {
    .collapse-button {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;
      }
    }

    .header-title {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .header-breadcrumb {
    .ant-breadcrumb-link {
      color: rgba(255, 255, 255, 0.65);
      // 移除悬停效果
    }

    .ant-breadcrumb-separator {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  .header-actions {
    .action-button {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;
      }
    }
  }

  .user-info {
    &:hover {
      background: rgba(255, 255, 255, 0.08);
    }

    .user-name {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

// 高分辨率适配
@media (min-width: $breakpoint-xl) {
  .v2-header {
    padding: 0 16px;

    .header-left {
      gap: 16px;
    }

    .collapse-button,
    .action-button {
      width: 36px;
      height: 36px;

      .anticon {
        font-size: 18px;
      }
    }

    .user-info {
      padding: $spacing-md $spacing-md;

      .user-name {
        font-size: $font-size-base;
        max-width: 120px;
      }
    }

    // 移动端调整布局
    .breadcrumb-container {
      gap: $spacing-xs;
    }
  }
}

// 动画效果
.v2-header {
  .collapse-button,
  .action-button,
  .user-info {
    transform-origin: center;

    &:active {
      transition: transform 0.1s ease;
    }
  }
}

// 暗色主题适配
.v2-basic-layout.dark {
  .v2-header-container {
    background: $header-bg-dark;
    border-bottom-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.2);
  }

  .v2-header {
    background: transparent;

    .collapse-button {
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        color: $primary-color;
        background: rgba(255, 255, 255, 0.1);
      }
    }

    .header-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .header-breadcrumb {
      .ant-breadcrumb-link {
        color: rgba(255, 255, 255, 0.45);
        // 移除悬停效果
      }

      .ant-breadcrumb-separator {
        color: rgba(255, 255, 255, 0.25);
      }
    }

    .action-button {
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        color: $primary-color;
        background: rgba(255, 255, 255, 0.1);
      }
    }

    // 暗色主题下的通知徽章优化
    .ant-badge {
      .ant-badge-dot {
        background: $error-color;
        border: 1px solid $header-bg-dark;
        box-shadow: 0 0 0 1px $header-bg-dark;
      }
    }

    .user-info {
      .user-name {
        color: rgba(255, 255, 255, 0.85);
      }

      .ant-avatar {
        border-color: rgba(255, 255, 255, 0.15);
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  // 独立标签页历史记录区域的暗色主题
  .tabs-history-area {
    background: $header-bg-dark;
    border-bottom-color: rgba(255, 255, 255, 0.1);

    &::before {
      background: rgba(255, 255, 255, 0.05);
    }
  }
}
