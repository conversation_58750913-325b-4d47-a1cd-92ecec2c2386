@use '../../styles/variables' as *;

/**
 * 文档管理模态框样式
 */
.document-management-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .stats-section {
    margin-bottom: 16px;

    .ant-statistic-card {
      text-align: center;
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-base;
      transition: border-color 0.2s ease;
    }
  }

  .upload-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--theme-bg-tertiary);
    border-radius: $border-radius-base;
    transition: background-color 0.2s ease;

    .upload-area {
      .ant-upload-drag {
        border-radius: $border-radius-base;
        border-color: var(--theme-border-color);

        &:hover {
          border-color: $primary-color;
        }
      }
    }

    .upload-tips {
      margin-top: $spacing-md;
      color: var(--theme-text-secondary);
      font-size: $font-size-sm;
      transition: color 0.2s ease;
    }
  }

  .document-list {
    .document-item {
      padding: $spacing-md;
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;
      transition: background-color 0.2s ease, border-color 0.2s ease;

      &:hover {
        background-color: var(--theme-bg-tertiary);
      }

      .document-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-sm;

        .document-name {
          font-weight: $font-weight-medium;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;
        }

        .document-actions {
          display: flex;
          gap: $spacing-sm;
        }
      }

      .document-meta {
        display: flex;
        gap: 16px;
        color: var(--theme-text-secondary);
        font-size: $font-size-sm;
        transition: color 0.2s ease;

        .meta-item {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
        }
      }

      .document-status {
        margin-top: $spacing-sm;

        .status-tag {
          &.processing {
            background-color: $warning-color;
          }

          &.completed {
            background-color: $success-color;
          }

          &.failed {
            background-color: $error-color;
          }
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    text-align: center;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: var(--theme-text-tertiary);
    transition: color 0.2s ease;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: $font-size-lg;
      margin-bottom: $spacing-md;
    }

    .empty-description {
      color: var(--theme-text-secondary);
      transition: color 0.2s ease;
    }
  }

  // 文档管理特定样式
  .ant-tabs {
    .ant-tabs-tab {
      font-weight: $font-weight-medium;

      &.ant-tabs-tab-active {
        font-weight: $font-weight-semibold;
      }
    }
  }

  .ant-upload-drag {
    border-radius: $border-radius-base;
    border-style: dashed;
    border-width: 2px;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.02);
    }

    .ant-upload-drag-icon {
      margin-bottom: $spacing-md;

      .anticon {
        font-size: 48px;
        color: $primary-color;
      }
    }

    .ant-upload-text {
      font-size: $font-size-lg;
      color: var(--theme-text-primary);
      margin-bottom: $spacing-sm;
      transition: color 0.2s ease;
    }

    .ant-upload-hint {
      color: var(--theme-text-secondary);
      transition: color 0.2s ease;
    }
  }

  .chunk-detail-content {
    > .ant-card {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-card-head {
        .ant-card-head-title {
          font-weight: $font-weight-semibold;
          color: var(--theme-text-primary);
          transition: color 0.2s ease;
        }
      }

      .ant-card-body {
        .chunk-content-container {
          background-color: var(--theme-bg-tertiary);
          border: 1px solid var(--theme-border-color-split);
          border-radius: $border-radius-base;
          padding: 16px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          line-height: $line-height-base;
          color: var(--theme-text-primary);
          white-space: pre-wrap;
          word-wrap: break-word;
          transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease;
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .document-management {
    .ant-table {
      .ant-table-content {
        overflow-x: auto;
      }
    }
  }
}

// 暗色主题支持
.dark .document-management {
  .ant-card {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;
  }

  .ant-upload-drag {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;

    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }

  .chunk-content-container {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;
    color: $dark-text-color;
  }
}
