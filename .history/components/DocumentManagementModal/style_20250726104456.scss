@use '../../styles/variables' as *;

/**
 * 文档管理模态框样式
 */
.document-management-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid $border-color-split;
    padding: 16px;
  }

  .ant-modal-body {
    padding: 16px;
    max-height: 80vh;
    overflow-y: auto;
  }

  .stats-section {
    margin-bottom: 16px;

    .ant-statistic-card {
      text-align: center;
      border: 1px solid $border-color-split;
      border-radius: $border-radius-base;
    }
  }

  .upload-section {
    margin-bottom: 16px;
    padding: 16px;
    background-color: $background-color-light;
    border-radius: $border-radius-base;

    .upload-area {
      .ant-upload-drag {
        border-radius: $border-radius-base;
        border-color: $border-color-base;

        &:hover {
          border-color: $primary-color;
        }
      }
    }

    .upload-tips {
      margin-top: $spacing-md;
      color: $text-color-secondary;
      font-size: $font-size-sm;
    }
  }

  .document-list {
    .document-item {
      padding: $spacing-md;
      border: 1px solid $border-color-split;
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;

      &:hover {
        background-color: $background-color-light;
      }

      .document-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: $spacing-sm;

        .document-name {
          font-weight: $font-weight-medium;
          color: $text-color;
        }

        .document-actions {
          display: flex;
          gap: $spacing-sm;
        }
      }

      .document-meta {
        display: flex;
        gap: 16px;
        color: $text-color-secondary;
        font-size: $font-size-sm;

        .meta-item {
          display: flex;
          align-items: center;
          gap: $spacing-xs;
        }
      }

      .document-status {
        margin-top: $spacing-sm;

        .status-tag {
          &.processing {
            background-color: $warning-color;
          }

          &.completed {
            background-color: $success-color;
          }

          &.failed {
            background-color: $error-color;
          }
        }
      }
    }
  }

  .pagination-container {
    margin-top: 16px;
    text-align: center;
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: $text-color-disabled;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: $font-size-lg;
      margin-bottom: $spacing-md;
    }

    .empty-description {
      color: $text-color-secondary;
    }
  }

  // 文档管理特定样式
  .ant-tabs {
    .ant-tabs-tab {
      font-weight: $font-weight-medium;

      &.ant-tabs-tab-active {
        font-weight: $font-weight-semibold;
      }
    }
  }

  .ant-upload-drag {
    border-radius: $border-radius-base;
    border-style: dashed;
    border-width: 2px;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      background-color: rgba($primary-color, 0.02);
    }

    .ant-upload-drag-icon {
      margin-bottom: $spacing-md;

      .anticon {
        font-size: 48px;
        color: $primary-color;
      }
    }

    .ant-upload-text {
      font-size: $font-size-lg;
      color: $text-color;
      margin-bottom: $spacing-sm;
    }

    .ant-upload-hint {
      color: $text-color-secondary;
    }
  }

  .chunk-detail-content {
    > .ant-card {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }

      .ant-card-head {
        .ant-card-head-title {
          font-weight: $font-weight-semibold;
          color: $text-color;
        }
      }

      .ant-card-body {
        .chunk-content-container {
          background-color: $background-color-light;
          border: 1px solid $border-color-split;
          border-radius: $border-radius-base;
          padding: 16px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          line-height: $line-height-base;
          color: $text-color;
          white-space: pre-wrap;
          word-wrap: break-word;
          max-height: 300px;
          overflow-y: auto;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: $breakpoint-md) {
  .document-management {
    .ant-table {
      .ant-table-content {
        overflow-x: auto;
      }
    }
  }
}

// 暗色主题支持
.dark .document-management {
  .ant-card {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;
  }

  .ant-upload-drag {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;

    &:hover {
      background-color: rgba($primary-color, 0.05);
    }
  }

  .chunk-content-container {
    background-color: $dark-bg-color-container;
    border-color: $dark-border-color-base;
    color: $dark-text-color;
  }
}
