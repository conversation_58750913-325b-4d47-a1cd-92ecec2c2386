@use '../../styles/variables' as *;

/**
 * 分类管理模态框样式
 */
.category-management-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .category-form {
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 16px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-md;
    }
  }

  .category-list {
    .ant-list-item {
      padding: $spacing-md;
      border: 1px solid $border-color-split;
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;

      &:hover {
        background-color: $background-color-light;
      }

      .category-info {
        flex: 1;

        .category-name {
          font-weight: $font-weight-medium;
          color: $text-color;
          margin-bottom: $spacing-xs;
        }

        .category-description {
          color: $text-color-secondary;
          font-size: $font-size-sm;
        }
      }

      .category-actions {
        display: flex;
        gap: $spacing-sm;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: $text-color-disabled;
  }
}
