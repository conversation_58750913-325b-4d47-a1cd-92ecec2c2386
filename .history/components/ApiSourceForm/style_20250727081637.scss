@use '../../styles/variables' as *;

// API源表单样式
.api-source-form-modal {
  .ant-modal-body {
    padding: 16px;
  }
}

.api-source-form {
  .form-section {
    margin-bottom: 20px;
    border-radius: 8px;
    
    .ant-card-head {
      border-bottom: 1px solid var(--theme-border-color-split);
      transition: border-color 0.2s ease;

      .ant-card-head-title {
        font-weight: 600;
        color: var(--theme-text-primary);
        transition: color 0.2s ease;
      }
    }
    
    .ant-card-body {
      padding: 16px 20px;
    }
    
    &.template-info {
      background: var(--theme-bg-tertiary);
      border: 1px solid var(--theme-border-color);
      transition: background-color 0.2s ease, border-color 0.2s ease;

      .template-detail {
        .ant-typography {
          margin-bottom: 8px;
          font-weight: 500;
        }

        p {
          margin: 0;
          color: var(--theme-text-secondary);
          font-size: 14px;
          transition: color 0.2s ease;
        }
      }
    }
  }

  // 表单控件样式
  .ant-form-item {
    margin-bottom: 16px;
    
    .ant-form-item-label > label {
      font-weight: 500;
      color: #374151;
    }
  }

  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-picker {
    border-radius: 6px;
    border-color: #d1d5db;
    
    &:hover {
      border-color: #3b82f6;
    }
    
    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector,
    &.ant-picker-focused {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
  }

  .ant-switch {
    &.ant-switch-checked {
      background-color: #3b82f6;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .ant-row {
      .ant-col {
        margin-bottom: 16px;
      }
    }
  }
} 