import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Tabs,
  Slider,
  InputNumber
} from "antd";
import KnowledgeBaseSelector from "../KnowledgeBaseSelector";
import { assistantAPI, modelAPI, handleApiResponse, handleApiError } from "../../services/assistantService";
import type {
  Assistant,
  AssistantCategory,
  // CreateAssistantRequest,
  // UpdateAssistantRequest,
  RAGConfig,
} from "../../types/assistant";
import "./style.scss";

const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

interface AssistantFormModalProps {
  visible: boolean;
  assistant?: Assistant | null;
  categories: AssistantCategory[];
  onCancel: () => void;
  onSuccess: () => void;
}

const AssistantFormModal: React.FC<AssistantFormModalProps> = ({
  visible,
  assistant,
  categories,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [ragEnabled, setRagEnabled] = useState(false);
  const [ragConfig, setRagConfig] = useState<RAGConfig | undefined>(undefined);
  const [selectedKnowledgeBases, setSelectedKnowledgeBases] = useState<
    string[]
  >([]);
  const [models, setModels] = useState<any[]>([]);

  useEffect(() => {
    if (visible) {
      loadModels();
      if (assistant) {
        // 编辑模式
        form.setFieldsValue({
          name: assistant.name,
          description: assistant.description,
          category: assistant.category,
          tags: assistant.tags,
          model_id: assistant.model_id,
          is_public: assistant.is_public,
          is_active: assistant.is_active,
          system_prompt: assistant.config.system_prompt,
          temperature: assistant.config.temperature,
          max_tokens: assistant.config.max_tokens,
          top_p: assistant.config.top_p,
        });
        setRagEnabled(assistant.rag_enabled);
        setRagConfig(assistant.config.rag_config);
        setSelectedKnowledgeBases(assistant.knowledge_base_ids || []);
      } else {
        // 创建模式
        form.resetFields();
        setRagEnabled(false);
        setRagConfig(undefined);
        setSelectedKnowledgeBases([]);
      }
    }
  }, [visible, assistant, form]);

  const loadModels = async () => {
    try {
      const response = await modelAPI.getModels();
      if (response.data?.success) {
        setModels(response.data.data || []);
      }
    } catch (error) {
      console.error("加载模型列表失败:", error);
    }
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 构建请求数据
      const requestData = {
        name: values.name,
        description: values.description,
        category: values.category,
        tags: values.tags || [],
        model_id: values.model_id,
        is_public: values.is_public || false,
        is_active: values.is_active !== false,
        rag_enabled: ragEnabled,
        knowledge_base_ids: ragEnabled ? selectedKnowledgeBases : [],
        config: {
          system_prompt: values.system_prompt,
          temperature: values.temperature,
          max_tokens: values.max_tokens,
          top_p: values.top_p,
          rag_config: ragEnabled ? ragConfig : undefined,
        },
      };

      let response;
      if (assistant) {
        // 更新助手
        response = await assistantAPI.updateAssistant(assistant.id, requestData);
      } else {
        // 创建助手
        response = await assistantAPI.createAssistant(requestData);
      }

      if (handleApiResponse(response)) {
        message.success(assistant ? "更新助手成功" : "创建助手成功");
        onSuccess();
      }
    } catch (error) {
      handleApiError(error, assistant ? "更新助手失败" : "创建助手失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={assistant ? "编辑助手" : "创建助手"}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          is_active: true,
          is_public: false,
          temperature: 0.7,
          max_tokens: 2000,
          top_p: 0.9,
        }}
      >
        <Tabs defaultActiveKey="basic">
          <TabPane tab="基础信息" key="basic">
            <Form.Item
              label="助手名称"
              name="name"
              rules={[{ required: true, message: "请输入助手名称" }]}
            >
              <Input placeholder="请输入助手名称" />
            </Form.Item>

            <Form.Item
              label="助手描述"
              name="description"
              rules={[{ required: true, message: "请输入助手描述" }]}
            >
              <TextArea
                rows={3}
                placeholder="请输入助手描述"
                showCount
                maxLength={200}
              />
            </Form.Item>

            <Form.Item
              label="分类"
              name="category"
              rules={[{ required: true, message: "请选择分类" }]}
            >
              <Select placeholder="请选择分类">
                {categories.map((category) => (
                  <Option key={category.key} value={category.key}>
                    {category.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="标签" name="tags">
              <Select
                mode="tags"
                placeholder="请输入标签，按回车添加"
                style={{ width: "100%" }}
              />
            </Form.Item>

            <Form.Item
              label="模型"
              name="model_id"
              rules={[{ required: true, message: "请选择模型" }]}
            >
              <Select placeholder="请选择模型" showSearch>
                {models.map((model) => (
                  <Option key={model.id} value={model.id}>
                    {model.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="设置" style={{ marginBottom: 0 }}>
              <Space direction="vertical" style={{ width: "100%" }}>
                <Form.Item name="is_active" valuePropName="checked" style={{ marginBottom: 8 }}>
                  <Space>
                    <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                    <span>启用助手</span>
                  </Space>
                </Form.Item>
                <Form.Item name="is_public" valuePropName="checked" style={{ marginBottom: 0 }}>
                  <Space>
                    <Switch checkedChildren="公开" unCheckedChildren="私有" />
                    <span>公开助手</span>
                  </Space>
                </Form.Item>
              </Space>
            </Form.Item>
          </TabPane>

          <TabPane tab="系统提示词" key="prompt">
            <Form.Item
              label="系统提示词"
              name="system_prompt"
              rules={[{ required: true, message: "请输入系统提示词" }]}
            >
              <TextArea
                rows={10}
                placeholder="请输入系统提示词，这将定义助手的行为和角色"
                showCount
                maxLength={2000}
              />
            </Form.Item>
          </TabPane>

          <TabPane tab="模型参数" key="parameters">
            <Form.Item label="Temperature" name="temperature">
              <Slider
                min={0}
                max={2}
                step={0.1}
                marks={{
                  0: "0",
                  0.5: "0.5",
                  1: "1",
                  1.5: "1.5",
                  2: "2",
                }}
              />
            </Form.Item>

            <Form.Item label="Max Tokens" name="max_tokens">
              <InputNumber
                min={1}
                max={4000}
                style={{ width: "100%" }}
              />
            </Form.Item>

            <Form.Item label="Top-P" name="top_p">
              <InputNumber
                min={0}
                max={1}
                step={0.1}
                style={{ width: "100%" }}
              />
            </Form.Item>
          </TabPane>

          <TabPane tab="知识库集成" key="rag">
            <KnowledgeBaseSelector
              ragEnabled={ragEnabled}
              onRAGEnabledChange={setRagEnabled}
              ragConfig={ragConfig}
              onRAGConfigChange={setRagConfig}
              selectedKnowledgeBases={selectedKnowledgeBases}
              onKnowledgeBasesChange={setSelectedKnowledgeBases}
            />
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  );
};

export default AssistantFormModal;
