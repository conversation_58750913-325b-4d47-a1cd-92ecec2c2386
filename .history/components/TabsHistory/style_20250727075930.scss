@use '../../styles/variables' as *;

// 标签页历史记录组件样式 - 参考 vue-pure-admin 设计
.tabs-history {
  display: flex;
  align-items: center;
  background: transparent;
  position: relative;
  height: 34px;
  overflow: hidden;
  width: 100%;
  // padding: 5px;
  padding-top: 0px;
  padding-bottom: 0px;
  padding-left: 2px;
  padding-right: 16px;


  // 🎨 滚动按钮 - 简化样式
  .scroll-button {
    flex-shrink: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-secondary);
    background: var(--theme-bg-primary);
    border: 1px solid var(--theme-border-color-split);
    z-index: 2;
    font-size: 12px;
    // 🎨 统一过渡时间和简洁悬停效果
    transition: all 0.2s ease;

    &:hover {
      color: $primary-color;
      background: var(--theme-bg-tertiary);
      // 🎨 简洁的悬停效果
      transform: translateY(-1px);
      // 🎨 统一阴影效果
      box-shadow: var(--theme-shadow-1);
    }

    &:active {
      transform: translateY(0) scale(0.95);
    }

    &.scroll-left {
      border-radius: 0 $border-radius-base $border-radius-base 0;
      border-left: none;
    }

    &.scroll-right {
      border-radius: $border-radius-base 0 0 $border-radius-base;
      border-right: none;
    }
  }

  // 标签页容器
  .tabs-container {
    flex: 1;
    overflow-x: auto;
    overflow-y: hidden;
    height: 100%;
    position: relative;
    padding-right: 36px;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  // 标签页包装器
  .tabs-wrapper {
    display: flex;
    align-items: center;
    height: 100%;
    min-width: max-content;
    padding: 0 4px;
  }

  // 🎨 标签页项目 - 统一设计风格
  .tab-item {
    display: flex;
    align-items: center;
    height: 28px;
    padding: 0 10px;
    padding-left: 5px;
    padding-right: 4px;
    margin-right: 2px;
    background: var(--theme-bg-tertiary);
    border: 1px solid var(--theme-border-color-split);
    border-radius: $border-radius-base $border-radius-base 0 0;
    cursor: pointer;
    // 🎨 统一过渡时间
    transition: all 0.2s ease;
    position: relative;
    white-space: nowrap;
    min-width: 70px;
    max-width: 180px;
    
    // 标签内容
    .tab-content {
      display: flex;
      align-items: center;
      gap: 5px;
      flex: 1;
      min-width: 0;

      .tab-icon {
        flex-shrink: 0;
        font-size: 12px;
        color: var(--theme-text-secondary);
        // 🎨 统一过渡时间
        transition: all 0.2s ease;
      }

      .tab-title {
        flex: 1;
        font-size: 12px;
        color: var(--theme-text-primary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
        // 🎨 统一过渡时间
        transition: all 0.2s ease;
      }

      .pin-icon {
        flex-shrink: 0;
        font-size: 9px;
        color: $primary-color;
        transform: rotate(45deg);
        // 🎨 统一过渡时间
        transition: all 0.2s ease;
      }
    }
    
    // 关闭按钮
    .close-icon {
      flex-shrink: 0;
      width: 14px;
      height: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 4px;
      font-size: 9px;
      color: var(--theme-text-secondary);
      border-radius: $border-radius-xs;
      opacity: 0;
      // 🎨 统一过渡时间
      transition: all 0.2s ease;

      &:hover {
        color: $error-color;
        background: rgba($error-color, 0.1);
        // 🎨 简洁的悬停效果
        transform: translateY(-1px);
      }
    }
    
    // 🎨 悬停状态 - Demo风格现代化效果
    &:hover {
      background: var(--theme-bg-primary);
      border-color: rgba($primary-color, 0.2);
      transform: translateY(-1px);
      box-shadow: var(--theme-shadow-2);

      .close-icon {
        opacity: 0.8;
      }

      .tab-content {
        .tab-icon {
          color: $primary-color;
          transform: scale(1.05);
        }

        .tab-title {
          color: var(--theme-text-primary);
        }
      }
    }

    // 🎨 激活状态 - Demo风格现代化指示
    &.active {
      background: var(--theme-bg-primary);
      border-color: rgba($primary-color, 0.3);
      z-index: 1;
      box-shadow: 0 2px 8px rgba($primary-color, 0.12);

      // 激活状态的左侧指示线
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 4px;
        bottom: 4px;
        width: 3px;
        background: $primary-color;
        border-radius: 0 2px 2px 0;
      }

      .tab-content {
        .tab-icon {
          color: $primary-color;
        }

        .tab-title {
          color: $primary-color;
        }
      }

      .close-icon {
        opacity: 1;
      }
    }
    
    // 固定标签样式
    &.pinned {
      .tab-content .tab-title {
        font-weight: 500;
      }
      
      // 固定标签不显示关闭按钮
      .close-icon {
        display: none;
      }
    }
  }

  // 🎨 更多操作按钮 - demo风格极简设计
  .more-button {
    flex-shrink: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(0, 0, 0, 0.45);
    background: transparent;
    border: none;
    border-radius: 6px;
    margin-left: 6px;
    font-size: 14px;
    transition: all 0.15s ease-out;
    opacity: 0.8;

    &:hover {
      color: $primary-color;
      background: rgba($primary-color, 0.04);
      opacity: 1;
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      background: rgba($primary-color, 0.08);
    }

    // 图标样式优化
    .anticon {
      font-size: 13px;
      transition: all 0.15s ease-out;
    }

    &:hover .anticon {
      transform: scale(1.1);
    }
  }
}

// 响应式适配
@media (max-width: $breakpoint-md) {
  .tabs-history {
    height: 30px;

    .tab-item {
      height: 24px;
      padding: 0 6px;
      min-width: 55px;
      max-width: 110px;

      .tab-content {
        gap: 3px;

        .tab-icon {
          font-size: 11px;
        }

        .tab-title {
          font-size: 11px;
        }

        .pin-icon {
          font-size: 8px;
        }
      }

      .close-icon {
        width: 12px;
        height: 12px;
        margin-left: 3px;
        font-size: 8px;
      }
    }

    .scroll-button,
    .more-button {
      width: 24px;
      height: 24px;
      font-size: 11px;
    }
  }
}

@media (max-width: $breakpoint-sm) {
  .tabs-history {
    height: 28px; // 移动端减小高度但保持可见
    padding: 0 $spacing-md;

    .tab-item {
      height: 22px;
      padding: 0 5px;
      min-width: 45px;
      max-width: 90px;

      .tab-content {
        gap: 2px;

        .tab-icon {
          font-size: 10px;
        }

        .tab-title {
          font-size: 10px;
        }

        .pin-icon {
          font-size: 7px;
        }
      }

      .close-icon {
        width: 10px;
        height: 10px;
        margin-left: 2px;
        font-size: 7px;
      }
    }

    .scroll-button,
    .more-button {
      width: 22px;
      height: 22px;
      font-size: 10px;
    }
  }
}

// 暗色主题适配
.v2-basic-layout.dark {
  .tabs-history {
    background: $content-bg-dark;
    border-bottom-color: rgba(255, 255, 255, 0.1);
    
    .scroll-button,
    .more-button {
      background: $content-bg-dark;
      border-color: rgba(255, 255, 255, 0.1);
      color: rgba(255, 255, 255, 0.65);
      
      &:hover {
        background: rgba(255, 255, 255, 0.05);
        color: $primary-color;
        // 🎨 暗色主题阴影
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }
    
    .tab-item {
      background: rgba(255, 255, 255, 0.05);
      border-color: rgba(255, 255, 255, 0.1);
      
      .tab-content {
        .tab-icon {
          color: rgba(255, 255, 255, 0.45);
        }
        
        .tab-title {
          color: rgba(255, 255, 255, 0.85);
        }
      }
      
      .close-icon {
        color: rgba(255, 255, 255, 0.45);
        
        &:hover {
          color: $error-color;
          background: rgba($error-color, 0.2);
        }
      }
      
      &:hover {
        background: rgba(255, 255, 255, 0.08);
        border-color: rgba(255, 255, 255, 0.2);
        // 🎨 暗色主题阴影
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        .tab-content .tab-icon {
          color: $primary-color;
        }
      }
      
      &.active {
        background: $content-bg-dark;
        border-color: $primary-color;
        border-bottom-color: $content-bg-dark;
        // 🎨 暗色主题阴影
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        
        &::after {
          background: $content-bg-dark;
        }
        
        .tab-content {
          .tab-icon {
            color: $primary-color;
          }
          
          .tab-title {
            color: $primary-color;
          }
        }
      }
    }
  }
}
