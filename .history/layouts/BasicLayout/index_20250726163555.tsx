import React, { useEffect } from 'react';
import { Layout as AntLayout, Modal } from 'antd';
import { useLocation, Outlet } from 'react-router-dom';
import { LayoutProps } from '../../types';
import { useLayoutStore, useThemeStore } from '../../stores';
import { useResponsive } from '../../hooks/useResponsiveListener';
import Sidebar from '../../components/Sidebar';
import Header from '../../components/Header';
import DynamicBreadcrumb from '../../components/DynamicBreadcrumb';
import menuData, { getMenuStateByPath } from '../../utils/menuData';
import authUtils from '../../utils/auth';
import { authAPI } from '../../services/api';
import './style.scss';

const { Content } = AntLayout;

/**
 * 基础布局组件 - 基于demo项目的布局设计
 * 提供完整的管理后台布局结构
 */
const BasicLayout: React.FC<LayoutProps> = ({
  children,
  sidebarProps = {},
  headerProps = {},
  className = '',
  style = {},
}) => {
  const location = useLocation();
  const { isMobile } = useResponsive();
  const {
    sidebarCollapsed,
    setSidebarCollapsed,
  } = useLayoutStore();

  const { theme } = useThemeStore();

  // 获取当前用户数据
  const currentUser = authUtils.getCurrentUser();
  const user = currentUser ? {
    id: currentUser.userId || '1',
    username: currentUser.username || 'Admin',
    email: '<EMAIL>',
    role: currentUser.role || 'admin',
    avatar: currentUser.avatar || '',
    isSuperAdmin: currentUser.superAdmin || false,
  } : {
    id: '1',
    username: 'Admin',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '',
    isSuperAdmin: false,
  };

  // 面包屑配置
  const breadcrumbConfig = {
    showHome: true,
    homeTitle: '首页',
    homePath: '/dashboard',
    showIcon: true,
    maxItems: 5,
  };

  // 处理菜单选择
  const handleMenuSelect = (_key: string, _path?: string) => {
    console.log('🎯 菜单选择:', _key, _path, 'isMobile:', isMobile);
    // 移动端选择菜单后自动收起侧边栏
    if (isMobile) {
      // 延迟收起，避免与用户操作冲突
      setTimeout(() => {
        setSidebarCollapsed(true);
      }, 300);
    }
  };

  // 处理用户菜单点击
  const handleUserMenuClick = async (key: string) => {
    switch (key) {
      case 'profile':
        console.log('打开个人资料');
        // TODO: 实现个人资料页面
        break;
      case 'settings':
        console.log('打开账户设置');
        // TODO: 实现用户设置页面
        break;
      case 'logout':
        // 显示确认对话框
        Modal.confirm({
          title: '确认退出',
          content: '您确定要退出登录吗？',
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 调用登出API
              await authAPI.logout();
            } catch (error) {
              console.error('登出API调用失败:', error);
              // 即使API调用失败，也要清除本地认证信息
            } finally {
              // 清除认证信息并跳转到登录页
              authUtils.clearAuthAndRedirect(true, '正在退出登录...', 1000);
            }
          },
        });
        break;
      default:
        break;
    }
  };

  // 调试：监听侧边栏状态变化
  useEffect(() => {
    console.log(`📱 侧边栏状态变化: collapsed=${sidebarCollapsed}, isMobile=${isMobile}`);
  }, [sidebarCollapsed, isMobile]);

  return (
    <div
      className={`v2-basic-layout ${theme} ${className}`}
      style={style}
      data-theme={theme}
    >
      {/* 侧边栏 */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onCollapse={setSidebarCollapsed}
        menuItems={menuData}
        onMenuSelect={handleMenuSelect}
        theme={theme}
        width={240}
        collapsedWidth={64}
        {...sidebarProps}
      />

      {/* 主要内容区域 - flexbox自动填充剩余空间 */}
      <div className="layout-main">
        {/* 顶部导航栏 */}
        <Header
          collapsed={sidebarCollapsed}
          onCollapse={setSidebarCollapsed}
          user={user}
          onUserMenuClick={handleUserMenuClick}
          showBreadcrumb={!isMobile}
          breadcrumbComponent={<DynamicBreadcrumb config={breadcrumbConfig} />}
          {...headerProps}
        />

        {/* 内容区域 - 可滚动区域 */}
        <Content className="layout-content">
          <div className="content-wrapper">
            {children || <Outlet />}
          </div>
        </Content>
      </div>

      {/* 移动端遮罩层 - Drawer组件自带遮罩，这里不需要额外的遮罩 */}
      {/* {isMobile && !sidebarCollapsed && (
        <div
          className="layout-mask"
          onClick={() => setSidebarCollapsed(true)}
        />
      )} */}
    </div>
  );
};

export default BasicLayout;
