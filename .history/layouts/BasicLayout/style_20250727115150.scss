@use '../../styles/variables' as *;

/* ===== V2 基础布局样式 ===== */

/* 主布局容器 */
.v2-basic-layout {
  height: 100vh;
  height: 100dvh;
  background: var(--theme-bg-primary);
  display: flex;
  flex-direction: row;
  margin: 0;
  padding: 0;
  overflow: hidden;

  * {
    box-sizing: border-box;
  }
}

/* 主内容区域 */
.v2-basic-layout .layout-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: var(--theme-bg-primary);
  overflow: hidden;
  height: 100vh;
  height: 100dvh;
}

/* 内容区域 */
.v2-basic-layout .layout-content {
  flex: 1;
  border: none;
  margin: 0;
  overflow-y: scroll;
  overflow-x: hidden;
  height: 0;
  transform: translateZ(0);
  will-change: scroll-position;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 内容包装器 */
.v2-basic-layout .content-wrapper {
  padding: $content-padding;
  background: transparent;
  margin: 0;
  border: none;
  box-sizing: border-box;
  min-height: 100%;
}

/* 移动端遮罩层 */
.v2-basic-layout .layout-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-mask-bg);
  z-index: $z-index-modal-backdrop;
}

/* ===== 滚动条样式 ===== */
.v2-basic-layout .layout-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.v2-basic-layout .layout-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.v2-basic-layout .layout-content::-webkit-scrollbar-thumb {
  background: var(--theme-scrollbar-thumb);
  border-radius: 3px;
}

.v2-basic-layout .layout-content::-webkit-scrollbar-thumb:hover {
  background: var(--theme-scrollbar-thumb-hover);
}

.v2-basic-layout .layout-content::-webkit-scrollbar-corner {
  background: transparent;
}

.v2-basic-layout .layout-content {
  scrollbar-width: thin;
  scrollbar-color: var(--theme-scrollbar-thumb) transparent;
}

/* ===== 加载状态 ===== */
.v2-basic-layout.loading .content-wrapper {
  position: relative;
}

.v2-basic-layout.loading .content-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-loading-bg);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.v2-basic-layout.loading .content-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32px;
  height: 32px;
  margin: -16px 0 0 -16px;
  border: 3px solid var(--theme-border-color);
  border-top-color: $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1001;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== Ant Design 组件修复 ===== */
.v2-basic-layout .ant-layout,
.v2-basic-layout .ant-layout-content {
  border: none !important;
  margin: 0 !important;
}

.v2-basic-layout .ant-layout {
  padding: 0 !important;
}

.v2-basic-layout .ant-layout-header {
  border-left: none !important;
  border-right: none !important;
  padding-left: 16px !important;
  padding-right: 16px !important;
}

.v2-basic-layout .ant-menu,
.v2-basic-layout .ant-menu-inline {
  border: none !important;
  border-right: none !important;
}

/* ===== 浏览器兼容性 ===== */
.no-dvh-support .v2-basic-layout,
.no-dvh-support .v2-basic-layout .layout-main {
  height: calc(var(--dvh-fallback, 1vh) * 100) !important;
}

/* ===== 响应式设计 ===== */

/* 移动端 */
@media (max-width: $breakpoint-sm) {
  .v2-basic-layout .layout-main {
    margin-left: 0 !important;
  }

  .v2-basic-layout .content-wrapper {
    padding: 16px;
  }
}

/* 平板端 */
@media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md) {
  .v2-basic-layout .content-wrapper {
    padding: 20px;
  }
}

/* 桌面端 */
@media (min-width: $breakpoint-md) {
  .v2-basic-layout .content-wrapper {
    padding: 24px;
  }
}

/* 大屏幕 */
@media (min-width: $breakpoint-lg) {
  .v2-basic-layout .content-wrapper {
    padding: 32px;
  }
}

/* 超大屏幕 */
@media (min-width: $breakpoint-xl) {
  .v2-basic-layout .content-wrapper {
    padding: 48px;
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* 超超大屏幕 */
@media (min-width: $breakpoint-xxl) {
  .v2-basic-layout .content-wrapper {
    max-width: 1800px;
    padding: 48px;
  }
}
