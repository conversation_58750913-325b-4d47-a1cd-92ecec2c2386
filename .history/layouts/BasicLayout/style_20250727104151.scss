@use '../../styles/variables' as *;

// V2 基础布局样式 - 固定侧边栏和顶部，主内容区域可滚动
.v2-basic-layout {
  // 使用动态视口高度，自动适配移动端浏览器UI
  height: 100vh; // 回退值，兼容旧浏览器
  height: 100dvh; // 动态视口高度，排除浏览器UI
  background: $background-color-base;
  // 使用flexbox进行水平布局，消除间隙
  display: flex;
  flex-direction: row;
  // 确保布局无间隙
  margin: 0;
  padding: 0;
  overflow: hidden; // 防止整体页面滚动



  // 主要内容区域 - 使用flex-grow填充剩余空间
  .layout-main {
    // 使用flex填充父容器剩余空间
    display: flex;
    flex-direction: column;
    flex: 1; // 自动填充剩余空间
    background: $background-color-base;
    overflow: hidden; // 防止主容器滚动
    // 使用动态视口高度，自动适配移动端
    height: 100vh; // 回退值
    height: 100dvh; // 动态视口高度
  }



  // 内容区域 - 可滚动区域
  .layout-content {
    flex: 1;
    background: $content-bg;
    transition: $transition-base;
    // 确保内容区域无边框
    border: none;
    margin: 0;
    // 启用垂直滚动
    overflow-y: auto;
    overflow-x: hidden;
    // 确保内容区域占满剩余高度
    height: 0; // 配合flex: 1使用，确保正确的滚动行为

    .content-wrapper {
      padding: $content-padding;
      background: transparent;
      // 确保内容包装器无间隙
      margin: 0;
      border: none;
      box-sizing: border-box; // 确保padding包含在高度内
      // 移除固定高度，让内容自然撑开
      min-height: 100%;
    }
  }

  // 移动端遮罩层
  .layout-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.45);
    z-index: $z-index-modal-backdrop;
    transition: opacity 0.3s ease;
  }

  // 暗色主题
  &.dark {
    background: #0f1419;

    .layout-content {
      background: $content-bg-dark;
    }

    .content-wrapper {
      background: transparent;
    }
  }
}

// 移动端适配
@media (max-width: $breakpoint-sm) {
  .v2-basic-layout {
    .layout-main {
      margin-left: 0 !important;
    }

    .layout-content {
      // 移动端保持滚动行为
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch; // iOS平滑滚动

      .content-wrapper {
        padding: 16px;
        min-height: 100%;
      }
    }
  }
}

// 平板端适配
@media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md) {
  .v2-basic-layout {
    .layout-content {
      .content-wrapper {
        padding: 20px;
      }
    }
  }
}

// 桌面端适配
@media (min-width: $breakpoint-md) {
  .v2-basic-layout {
    .layout-content {
      .content-wrapper {
        padding: 24px;
      }
    }
  }
}

// 大屏幕适配
@media (min-width: $breakpoint-lg) {
  .v2-basic-layout {
    .layout-content {
      .content-wrapper {
        padding: 32px 32px;
      }
    }
  }
}

// 超大屏幕适配 - 居中卡片效果
@media (min-width: $breakpoint-xl) {
  .v2-basic-layout {
    .layout-content {
      .content-wrapper {
        padding: 48px;
        max-width: 1600px;
        margin: 0 auto;
      }
    }
  }
}

// 不支持dvh的浏览器回退方案 - 使用更高优先级的选择器
.no-dvh-support .v2-basic-layout {
  height: calc(var(--dvh-fallback, 1vh) * 100) !important;

  .layout-main {
    height: calc(var(--dvh-fallback, 1vh) * 100) !important;
  }
}

// 内容区域动画
.v2-basic-layout {
  .layout-content {
    .content-wrapper {
      opacity: 1;
      transform: translateY(0);
      transition: opacity 0.3s ease, transform 0.3s ease;

      // 页面切换动画
      &.page-enter {
        opacity: 0;
        transform: translateY(20px);
      }

      &.page-enter-active {
        opacity: 1;
        transform: translateY(0);
      }

      &.page-exit {
        opacity: 1;
        transform: translateY(0);
      }

      &.page-exit-active {
        opacity: 0;
        transform: translateY(-20px);
      }
    }
  }
}

// 滚动条样式 - 只在内容区域显示
.v2-basic-layout {
  .layout-content {
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 3px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.25);
      }
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }

    // Firefox滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
  }

  // 暗色主题滚动条
  &.dark {
    .layout-content {
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.15);

        &:hover {
          background: rgba(255, 255, 255, 0.25);
        }
      }

      &::-webkit-scrollbar-corner {
        background: transparent;
      }

      // Firefox暗色主题滚动条
      scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
    }
  }
}

// 加载状态
.v2-basic-layout {
  &.loading {
    .layout-content {
      .content-wrapper {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.8);
          z-index: 1000;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 32px;
          height: 32px;
          margin: -16px 0 0 -16px;
          border: 3px solid $border-color-light;
          border-top-color: $primary-color;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          z-index: 1001;
        }
      }
    }

    &.dark {
      .layout-content {
        .content-wrapper {
          &::before {
            background: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 滚动条样式 - 只在内容区域显示
.v2-basic-layout {
  .layout-content {
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.15);
      border-radius: 3px;
      transition: background 0.2s ease;

      &:hover {
        background: rgba(0, 0, 0, 0.25);
      }
    }

    &::-webkit-scrollbar-corner {
      background: transparent;
    }

    // Firefox滚动条样式
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.15) transparent;
  }

  // 暗色主题滚动条
  &.dark {
    .layout-content {
      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.15);

        &:hover {
          background: rgba(255, 255, 255, 0.25);
        }
      }

      &::-webkit-scrollbar-corner {
        background: transparent;
      }

      // Firefox暗色主题滚动条
      scrollbar-color: rgba(255, 255, 255, 0.15) transparent;
    }
  }
}

// 加载状态
.v2-basic-layout {
  &.loading {
    .layout-content {
      .content-wrapper {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.8);
          z-index: 1000;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 32px;
          height: 32px;
          margin: -16px 0 0 -16px;
          border: 3px solid $border-color-light;
          border-top-color: $primary-color;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          z-index: 1001;
        }
      }
    }

    &.dark {
      .layout-content {
        .content-wrapper {
          &::before {
            background: rgba(0, 0, 0, 0.8);
          }
        }
      }
    }
  }
}

// 旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}



// 确保布局稳定性
.v2-basic-layout {
  // 防止布局跳动
  * {
    box-sizing: border-box;
  }

  // 确保主内容区域正确的滚动行为
  .layout-content {
    // 使用GPU加速优化滚动性能
    transform: translateZ(0);
    will-change: scroll-position;

    // 平滑滚动
    scroll-behavior: smooth;

    // 确保滚动条始终可见（避免布局跳动）
    overflow-y: scroll;

    // 在某些浏览器中优化滚动性能
    -webkit-overflow-scrolling: touch;
  }
}

// 修复Antd组件可能的边框问题
.v2-basic-layout {
  // 确保Layout组件无边框
  .ant-layout {
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .ant-layout-content {
    border: none !important;
    margin: 0 !important;
  }

  .ant-layout-header {
    border-left: none !important;
    border-right: none !important;
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  // 修复可能的Menu组件边框
  .ant-menu {
    border: none !important;
    border-right: none !important;
  }

  .ant-menu-inline {
    border-right: none !important;
  }
}

// 响应式布局调整
.v2-basic-layout {
  // 确保在不同屏幕尺寸下的最佳显示效果
  @media (max-width: $breakpoint-xs) {
    .layout-content {
      // 移动端优化滚动体验
      -webkit-overflow-scrolling: touch;

      .content-wrapper {
        padding: $spacing-md;
      }
    }
  }

  @media (min-width: $breakpoint-xxl) {
    .layout-content {
      .content-wrapper {
        max-width: 1800px;
        padding: 48px;
        margin: 0 auto;
      }
    }
  }
}
