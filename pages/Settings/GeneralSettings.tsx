import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  message,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Tooltip,
  Card,
} from "antd";
import FramerPageAnimation from "../../components/FramerPageAnimation";
import {
  SaveOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import {
  settingsAPI,
  // SystemSettings, // 未使用
} from "../../../src/services/settingsService";
import { handleApiResponse, handleApiError } from "../../../src/utils/apiResponseHandler";

/**
 * 基础设置页面 - 合并基础设置、性能设置、超时设置
 */
const GeneralSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  // 加载系统设置
  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await settingsAPI.getSettings();
      const result = handleApiResponse(response);

      if (result.success) {
        form.setFieldsValue(result.data);
      }
    } catch (error) {
      console.error("加载基础设置失败:", error);
      handleApiError(error);
    } finally {
      setLoading(false);
    }
  };

  // 保存设置
  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();

      const response = await settingsAPI.updateSettings(values);
      const result = handleApiResponse(response);

      if (result.success) {
        message.success("基础设置保存成功");
      }
    } catch (error: any) {
      console.error("保存基础设置失败:", error);
      if (error.errorFields) {
        message.error("请检查表单输入");
      } else {
        handleApiError(error);
      }
    } finally {
      setSaving(false);
    }
  };

  // 组件挂载时加载设置
  useEffect(() => {
    loadSettings();
  }, []);

  return (
    <div className="general-settings">
      <FramerPageAnimation delay={0}>
        <Spin spinning={loading}>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            system_name: "",
            system_version: "",
            load_balancing_enabled: false,
            default_page_size: 20,
            max_page_size: 100,
            model_refresh_interval: 300,
            api_timeout: 30,
            http_client_timeout: 30,
            stream_context_timeout: 120,
            model_list_timeout: 10,
          }}
        >
          {/* 系统基本信息 */}
          <Card
            title={
              <Space>
                <SettingOutlined />
                系统基本信息
              </Space>
            }
            style={{ marginBottom: 24 }}
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="系统名称"
                  name="system_name"
                  rules={[{ required: true, message: "请输入系统名称" }]}
                >
                  <Input placeholder="请输入系统名称" />
                </Form.Item>
                <div className="setting-description">
                  显示在系统界面中的名称
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="系统版本"
                  name="system_version"
                  rules={[{ required: true, message: "请输入系统版本" }]}
                >
                  <Input placeholder="请输入系统版本" />
                </Form.Item>
                <div className="setting-description">
                  当前系统的版本号
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用负载均衡"
                  name="load_balancing_enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  启用后将在多个API源之间分配请求负载
                </div>
              </Col>
            </Row>
          </Card>

          {/* 性能配置 */}
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                性能配置
              </Space>
            }
            style={{ marginBottom: 24 }}
          >

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      默认页面大小
                      <Tooltip title="列表页面默认显示的记录数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="default_page_size"
                  rules={[
                    { required: true, message: "请输入默认页面大小" },
                    { type: "number", min: 1, max: 100, message: "页面大小必须在1-100之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    placeholder="20"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：20，适合大多数列表显示
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      最大页面大小
                      <Tooltip title="单次查询允许的最大记录数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="max_page_size"
                  rules={[
                    { required: true, message: "请输入最大页面大小" },
                    { type: "number", min: 1, max: 1000, message: "最大页面大小必须在1-1000之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={1000}
                    placeholder="100"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：100，防止单次查询数据过多
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      模型刷新间隔(秒)
                      <Tooltip title="自动刷新模型列表的时间间隔">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="model_refresh_interval"
                  rules={[
                    { required: true, message: "请输入模型刷新间隔" },
                    { type: "number", min: 30, max: 3600, message: "刷新间隔必须在30-3600秒之间" }
                  ]}
                >
                  <InputNumber
                    min={30}
                    max={3600}
                    placeholder="300"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：300秒（5分钟），平衡性能和实时性
                </div>
              </Col>
            </Row>
          </Card>

          {/* 超时配置 */}
          <Card
            title={
              <Space>
                <ClockCircleOutlined />
                超时配置
              </Space>
            }
            style={{ marginBottom: 24 }}
          >

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      API超时时间(秒)
                      <Tooltip title="API请求的默认超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="api_timeout"
                  rules={[
                    { required: true, message: "请输入API超时时间" },
                    { type: "number", min: 5, max: 300, message: "超时时间必须在5-300秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={300}
                    placeholder="30"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：30秒，适合大多数API调用
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      HTTP客户端超时(秒)
                      <Tooltip title="HTTP客户端连接超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="http_client_timeout"
                  rules={[
                    { required: true, message: "请输入HTTP客户端超时时间" },
                    { type: "number", min: 5, max: 300, message: "超时时间必须在5-300秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={300}
                    placeholder="30"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：30秒，与API超时保持一致
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      流式上下文超时(秒)
                      <Tooltip title="流式响应的上下文保持时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="stream_context_timeout"
                  rules={[
                    { required: true, message: "请输入流式上下文超时时间" },
                    { type: "number", min: 10, max: 600, message: "超时时间必须在10-600秒之间" }
                  ]}
                >
                  <InputNumber
                    min={10}
                    max={600}
                    placeholder="120"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：120秒，适合长时间流式对话
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      模型列表超时(秒)
                      <Tooltip title="获取模型列表的超时时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="model_list_timeout"
                  rules={[
                    { required: true, message: "请输入模型列表超时时间" },
                    { type: "number", min: 5, max: 120, message: "超时时间必须在5-120秒之间" }
                  ]}
                >
                  <InputNumber
                    min={5}
                    max={120}
                    placeholder="10"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：10秒，快速获取模型信息
                </div>
              </Col>
            </Row>
          </Card>

          <div style={{ textAlign: "right" }}>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={saving}
            >
              保存设置
            </Button>
          </div>
        </Form>
      </Spin>
      </FramerPageAnimation>
    </div>
  );
};

export default GeneralSettings;
