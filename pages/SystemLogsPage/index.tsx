import React, { useEffect, useCallback, useMemo } from "react";
import { Tag, Card, Space, Button, Input, Select, DatePicker, Tooltip } from "antd";
import { ReloadOutlined, DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import TanStackTable from "../../components/TanStackTable";
import { fuzzyFilter } from '../../components/TanStackTable/utils';
import { useSystemLogsStore } from '../../stores';
import type { SystemLogItem } from '../../stores/pages/systemLogsSlice';
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import moment from 'moment';
import "./style.scss";

const { RangePicker } = DatePicker;

const SystemLogsPage: React.FC = () => {
  // 使用 Zustand store 状态管理
  const {
    data,
    loading,
    sorting,
    pagination,
    columnFilters,
    globalFilter,
    searchParams,
    setData,
    setLoading,
    setSorting,
    setPagination,
    setColumnFilters,
    setGlobalFilter,
    setSearchParams,
    resetSearchParams,
    loadData,
  } = useSystemLogsStore();

  // 创建列助手
  const columnHelper = createColumnHelper<SystemLogItem>();

  // TanStackTable 列定义
  const columns = useMemo<ColumnDef<SystemLogItem, any>[]>(() => [
    columnHelper.display({
      id: 'index',
      header: '序号',
      size: 48,
      cell: ({ row }) => (
        <span className="sequence-number">{row.index + 1}</span>
      ),
    }),
    columnHelper.accessor('level', {
      id: 'level',
      header: '日志级别',
      size: 100,
      enableSorting: true,
      cell: ({ getValue }) => {
        const level = getValue();
        if (level === "ERROR") return <Tag color="error">ERROR</Tag>;
        if (level === "WARN") return <Tag color="warning">WARN</Tag>;
        return <Tag color="default">INFO</Tag>;
      },
    }),
    columnHelper.accessor('content', {
      id: 'content',
      header: '日志内容',
      size: 200,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => {
        const content = getValue();
        return (
          <Tooltip title={content} placement="topLeft">
            <span className="content-text">{content}</span>
          </Tooltip>
        );
      },
    }),
    columnHelper.accessor('module', {
      id: 'module',
      header: '来源模块',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="module-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('time', {
      id: 'time',
      header: '时间',
      size: 170,
      enableSorting: true,
      cell: ({ getValue }) => (
        <span className="time-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('user', {
      id: 'user',
      header: '操作用户',
      size: 120,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="user-text">{getValue()}</span>
      ),
    }),
    columnHelper.accessor('ip', {
      id: 'ip',
      header: 'IP地址',
      size: 140,
      enableSorting: true,
      enableGlobalFilter: true,
      cell: ({ getValue }) => (
        <span className="ip-text">{getValue()}</span>
      ),
    }),
  ], [columnHelper]);

  // 初始化数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // TanStackTable 事件处理
  const handlePageChange = useCallback((pageIndex: number, pageSize: number) => {
    setPagination({ pageIndex, pageSize });
  }, []);

  const handleRowClick = useCallback((row: SystemLogItem) => {
    console.log('点击日志行:', row);
  }, []);

  // 刷新处理
  const handleRefresh = useCallback(() => {
    console.log('刷新系统日志数据');
    loadData();
  }, [loadData]);

  // 导出处理
  const handleExport = useCallback(() => {
    console.log('导出系统日志数据');
    // 这里可以添加导出逻辑
  }, []);

  // 搜索处理
  const handleSearch = useCallback(() => {
    loadData();
  }, [loadData]);

  // 重置搜索
  const handleReset = useCallback(() => {
    resetSearchParams();
  }, [resetSearchParams]);

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
      className="system-logs-page"
    >


          {/* 搜索区域 */}
          <div className="search-section">
            <Space direction="horizontal" size={16} wrap style={{ width: '100%', justifyContent: 'space-between' }}>
              {/* 左侧：搜索框和筛选器 */}
              <Space size={16} wrap>
                <Select
                  placeholder="日志级别"
                  value={searchParams.level || undefined}
                  onChange={(value) => setSearchParams({ ...searchParams, level: value || '' })}
                  style={{ width: 150, height: 32 }}
                  allowClear
                  options={[
                    { label: 'ERROR', value: 'ERROR' },
                    { label: 'WARN', value: 'WARN' },
                    { label: 'INFO', value: 'INFO' },
                  ]}
                />
                <Input
                  placeholder="日志内容"
                  value={searchParams.content}
                  onChange={(e) => setSearchParams({ ...searchParams, content: e.target.value })}
                  style={{ width: 170, height: 32 }}
                  allowClear
                />
                <Select
                  placeholder="来源模块"
                  value={searchParams.module || undefined}
                  onChange={(value) => setSearchParams({ ...searchParams, module: value || '' })}
                  style={{ width: 150, height: 32 }}
                  allowClear
                  options={[
                    { label: '系统模块', value: '系统模块' },
                    { label: '用户模块', value: '用户模块' },
                    { label: '数据模块', value: '数据模块' },
                    { label: '网络模块', value: '网络模块' },
                  ]}
                />
                <RangePicker
                  placeholder={['开始时间', '结束时间']}
                  onChange={(dates, dateStrings) => {
                    setSearchParams({
                      ...searchParams,
                      startTime: dateStrings[0],
                      endTime: dateStrings[1],
                    });
                  }}
                  style={{ width: 240, height: 32 }}
                />
              </Space>

              {/* 右侧：操作按钮 */}
              <Space size={12}>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button onClick={handleReset}>重置</Button>
              </Space>
            </Space>
          </div>



          {/* 表格区域 */}
          <div className="table-section">
            <TanStackTable
              data={data}
              columns={columns}
              loading={loading}
              sorting={sorting}
              pagination={pagination}
              columnFilters={columnFilters}
              globalFilter={globalFilter}
              filterFns={{
                fuzzy: fuzzyFilter,
              }}
              config={{
                size: 'compact',
                emptyStateHeight: 280,
                minRowsForDynamic: 10,
                pageSizeOptions: [10, 20, 50],
                defaultPageSize: 10,
                minTableWidth: 1000,
              }}
              events={{
                onSortingChange: setSorting,
                onPageChange: handlePageChange,
                onColumnFiltersChange: setColumnFilters,
                onGlobalFilterChange: setGlobalFilter,
                onRowClick: handleRowClick,
              }}
              showPagination={true}
              className="system-logs-table"
            />
          </div>
    </motion.div>
  );
};

export default SystemLogsPage;