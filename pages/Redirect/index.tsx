import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';

/**
 * 重定向页面 - 用于刷新标签页
 */
const Redirect: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // 从location.pathname中提取目标路径
    const currentPath = location.pathname;
    console.log('🔄 Redirect page - current path:', currentPath);

    // 移除 /redirect 前缀，获取真实的目标路径
    const targetPath = currentPath.replace(/^\/redirect/, '') || '/dashboard';

    console.log('🔄 Redirect page - target path:', targetPath);

    // 验证目标路径，防止无限循环
    if (targetPath.includes('/redirect')) {
      console.error('🔄 Redirect loop detected, redirecting to dashboard');
      navigate('/dashboard', { replace: true });
      return;
    }

    // 立即重定向到目标页面
    navigate(targetPath, { replace: true });
  }, [navigate, location.pathname]);

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '200px'
    }}>
      <Spin size="large" />
    </div>
  );
};

export default Redirect;
