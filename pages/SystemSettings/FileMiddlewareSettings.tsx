import React, { useEffect } from "react";
import {
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Select,
  Tooltip,
  Card,
} from "antd";
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import {
  InfoCircleOutlined,
  SaveOutlined,
  FolderOutlined,
  CloudUploadOutlined,
  SecurityScanOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import { useFileMiddlewareSettingsStore } from "../../stores";
import type { FileMiddlewareSettingsData } from "../../stores/settings/fileMiddlewareSettingsSlice";
import "./style.scss";

const { Option } = Select;

interface FileMiddlewareSettingsProps {
  onFormChange?: () => void;
}

/**
 * 文件中间件设置组件 - V2 Admin版本
 * 适配v2-admin设计系统
 */
const FileMiddlewareSettingsComponent: React.FC<
  FileMiddlewareSettingsProps
> = ({ onFormChange }) => {
  const [form] = Form.useForm<FileMiddlewareSettingsData>();

  // 使用 Zustand store
  const {
    fileMiddlewareSettings,
    fileMiddlewareLoading,
    fileMiddlewareSaving,
    fileMiddlewareError,
    fileMiddlewareIsDirty,
    testResult,
    testing,
    loadFileMiddlewareSettings,
    updateFileMiddlewareSettings,
    markFileMiddlewareDirty,
    getFileMiddlewareDefaultSettings,
    testFileMiddleware,
  } = useFileMiddlewareSettingsStore();

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await updateFileMiddlewareSettings(values);
      markFileMiddlewareDirty(false);

      if (onFormChange) {
        onFormChange();
      }
    } catch (error: any) {
      if (error.errorFields) {
        // Ant Design 表单验证错误
        const { message } = await import('antd');
        message.error("请检查表单输入");
      }
      // API 错误已在 store 中处理
    }
  };

  // 测试文件中间件功能
  const handleTestUpload = async () => {
    await testFileMiddleware('upload');
  };

  const handleTestScan = async () => {
    await testFileMiddleware('scan');
  };

  const handleTestCompression = async () => {
    await testFileMiddleware('compression');
  };

  // 表单值变化时标记为脏数据
  const handleFormChange = () => {
    if (!fileMiddlewareIsDirty) {
      markFileMiddlewareDirty(true);
    }
    if (onFormChange) {
      onFormChange();
    }
  };

  // 组件挂载时加载设置
  useEffect(() => {
    loadFileMiddlewareSettings();
  }, [loadFileMiddlewareSettings]);

  // 当设置加载完成时，更新表单值
  useEffect(() => {
    if (fileMiddlewareSettings) {
      form.setFieldsValue(fileMiddlewareSettings);
      markFileMiddlewareDirty(false);
    }
  }, [fileMiddlewareSettings, form, markFileMiddlewareDirty]);

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
      className="file-middleware-settings"
    >
        <Spin spinning={fileMiddlewareLoading}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          initialValues={getFileMiddlewareDefaultSettings()}
        >
          {/* 基础设置 */}
          <Card
            title={
              <Space>
                <FolderOutlined />
                基础设置
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用文件中间件"
                  name="enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  开启后，系统将启用文件上传和处理功能
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      最大文件大小 (MB)
                      <Tooltip title="单个文件的最大大小限制">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="maxFileSize"
                  rules={[
                    { required: true, message: "请输入最大文件大小" },
                    { type: "number", min: 1, max: 1024, message: "文件大小必须在1-1024MB之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={1024}
                    placeholder="10"
                    style={{ width: "100%" }}
                    addonAfter="MB"
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：10MB，根据需要调整
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      存储类型
                      <Tooltip title="选择文件存储方式">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="storageType"
                  rules={[{ required: true, message: "请选择存储类型" }]}
                >
                  <Select placeholder="选择存储类型">
                    <Option value="local">本地存储</Option>
                    <Option value="s3">Amazon S3</Option>
                    <Option value="oss">阿里云OSS</Option>
                    <Option value="cos">腾讯云COS</Option>
                  </Select>
                </Form.Item>
                <div className="setting-description">
                  本地存储适合开发环境，生产环境推荐云存储
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="允许的文件扩展名"
                  name="allowedExtensions"
                  rules={[{ required: true, message: "请选择允许的文件类型" }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="选择允许的文件类型"
                    options={[
                      { label: "PDF (.pdf)", value: ".pdf" },
                      { label: "Word (.doc)", value: ".doc" },
                      { label: "Word (.docx)", value: ".docx" },
                      { label: "文本 (.txt)", value: ".txt" },
                      { label: "Markdown (.md)", value: ".md" },
                      { label: "Excel (.xls)", value: ".xls" },
                      { label: "Excel (.xlsx)", value: ".xlsx" },
                      { label: "PowerPoint (.ppt)", value: ".ppt" },
                      { label: "PowerPoint (.pptx)", value: ".pptx" },
                      { label: "图片 (.jpg)", value: ".jpg" },
                      { label: "图片 (.png)", value: ".png" },
                      { label: "图片 (.gif)", value: ".gif" },
                    ]}
                  />
                </Form.Item>
                <div className="setting-description">
                  选择系统允许上传的文件类型
                </div>
              </Col>
            </Row>
          </Card>

          {/* 存储设置 */}
          <Card
            title={
              <Space>
                <CloudUploadOutlined />
                存储设置
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="存储路径"
                  name="storagePath"
                  rules={[{ required: true, message: "请输入存储路径" }]}
                >
                  <Input placeholder="/uploads" />
                </Form.Item>
                <div className="setting-description">
                  文件在服务器上的存储路径
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="临时目录"
                  name="tempPath"
                  rules={[{ required: true, message: "请输入临时目录" }]}
                >
                  <Input placeholder="/tmp" />
                </Form.Item>
                <div className="setting-description">
                  文件处理过程中的临时存储路径
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用文件压缩"
                  name="compressionEnabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  自动压缩上传的文件以节省存储空间
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      压缩质量
                      <Tooltip title="压缩质量，值越高质量越好但文件越大">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="compressionQuality"
                  rules={[
                    { type: "number", min: 1, max: 100, message: "压缩质量必须在1-100之间" }
                  ]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    placeholder="80"
                    style={{ width: "100%" }}
                    addonAfter="%"
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：80%，平衡质量和文件大小
                </div>
              </Col>
            </Row>
          </Card>

          {/* 安全设置 */}
          <Card
            title={
              <Space>
                <SecurityScanOutlined />
                安全设置
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用病毒扫描"
                  name="virusScanEnabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  上传文件时进行病毒扫描检查
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="文件名加密"
                  name="encryptFilenames"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  使用加密文件名存储，提高安全性
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Form.Item
                  label="禁止的文件名模式"
                  name="forbiddenPatterns"
                >
                  <Select
                    mode="tags"
                    placeholder="输入禁止的文件名模式，如 *.exe"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  支持通配符，如 *.exe, temp*, *virus* 等
                </div>
              </Col>
            </Row>
          </Card>

          {/* 测试功能 */}
          <Card
            title={
              <Space>
                <ThunderboltOutlined />
                测试功能
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={24}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space wrap>
                    <Button
                      type="default"
                      icon={<CloudUploadOutlined />}
                      onClick={handleTestUpload}
                      loading={testing}
                      disabled={!fileMiddlewareSettings?.enabled}
                    >
                      测试文件上传
                    </Button>
                    <Button
                      type="default"
                      icon={<SecurityScanOutlined />}
                      onClick={handleTestScan}
                      loading={testing}
                      disabled={!fileMiddlewareSettings?.enabled || !fileMiddlewareSettings?.virusScanEnabled}
                    >
                      测试病毒扫描
                    </Button>
                    <Button
                      type="default"
                      icon={<FolderOutlined />}
                      onClick={handleTestCompression}
                      loading={testing}
                      disabled={!fileMiddlewareSettings?.enabled || !fileMiddlewareSettings?.compressionEnabled}
                    >
                      测试文件压缩
                    </Button>
                  </Space>
                  {testResult && (
                    <Alert
                      message="测试结果"
                      description={testResult}
                      type="success"
                      showIcon
                    />
                  )}
                </Space>
              </Col>
            </Row>
          </Card>

          <div style={{ textAlign: "right" }}>
            <Space>
              {fileMiddlewareError && (
                <span style={{ color: '#ff4d4f', marginRight: 16 }}>
                  {fileMiddlewareError}
                </span>
              )}
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={fileMiddlewareSaving}
                disabled={!fileMiddlewareIsDirty}
              >
                {fileMiddlewareIsDirty ? '保存设置' : '已保存'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </motion.div>
  );
};

export default FileMiddlewareSettingsComponent;
