import React, { useEffect } from "react";
import {
  Form,
  InputNumber,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Select,
  Tooltip,
  Card,
  Radio,
} from "antd";
import {
  InfoCircleOutlined,
  SaveOutlined,
  SafetyOutlined,
  EyeOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { useCaptchaSettingsStore } from "../../stores";
import type { CaptchaSettingsData } from "../../stores/settings/captchaSettingsSlice";
import "./style.scss";

const { Option } = Select;

interface CaptchaSettingsProps {
  onFormChange?: () => void;
}

/**
 * 验证码设置组件 - V2 Admin版本
 * 适配v2-admin设计系统
 */
const CaptchaSettingsComponent: React.FC<CaptchaSettingsProps> = ({
  onFormChange,
}) => {
  const [form] = Form.useForm<CaptchaSettingsData>();

  // 使用 Zustand store
  const {
    captchaSettings,
    captchaLoading,
    captchaSaving,
    captchaError,
    captchaIsDirty,
    testCaptcha,
    testCaptchaLoading,
    loadCaptchaSettings,
    updateCaptchaSettings,
    setCaptchaSettings,
    markCaptchaDirty,
    getCaptchaDefaultSettings,
    generateTestCaptcha,
    refreshCaptcha,
  } = useCaptchaSettingsStore();

  // 保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      await updateCaptchaSettings(values);
      markCaptchaDirty(false);

      if (onFormChange) {
        onFormChange();
      }
    } catch (error: any) {
      console.error("保存验证码设置失败:", error);
      if (error.errorFields) {
        // Ant Design 表单验证错误
        const { message } = await import('antd');
        message.error("请检查表单输入");
      }
      // API 错误已在 store 中处理
    }
  };

  // 测试验证码
  const handleTestCaptcha = async () => {
    await generateTestCaptcha();
  };

  // 刷新验证码
  const handleRefreshCaptcha = async () => {
    await refreshCaptcha();
  };

  // 表单值变化时标记为脏数据
  const handleFormChange = () => {
    if (!captchaIsDirty) {
      markCaptchaDirty(true);
    }
    if (onFormChange) {
      onFormChange();
    }
  };

  // 组件挂载时加载设置
  useEffect(() => {
    loadCaptchaSettings();
  }, [loadCaptchaSettings]);

  // 当设置加载完成时，更新表单值
  useEffect(() => {
    if (captchaSettings) {
      form.setFieldsValue(captchaSettings);
      markCaptchaDirty(false);
    }
  }, [captchaSettings, form, markCaptchaDirty]);

  return (
    <div className="captcha-settings">
      <Spin spinning={captchaLoading}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
          initialValues={getCaptchaDefaultSettings()}
        >
          {/* 基础设置 */}
          <Card
            title={
              <Space>
                <SafetyOutlined />
                基础设置
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="启用验证码"
                  name="enabled"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  开启后，登录和注册时将需要验证码
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="验证码类型"
                  name="type"
                  rules={[{ required: true, message: "请选择验证码类型" }]}
                >
                  <Radio.Group>
                    <Radio value="image">图片验证码</Radio>
                    <Radio value="math">数学运算</Radio>
                    <Radio value="text">文字验证码</Radio>
                  </Radio.Group>
                </Form.Item>
                <div className="setting-description">
                  选择验证码的显示类型
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="难度级别"
                  name="difficulty"
                  rules={[{ required: true, message: "请选择难度级别" }]}
                >
                  <Select placeholder="选择难度级别">
                    <Option value="easy">简单</Option>
                    <Option value="medium">中等</Option>
                    <Option value="hard">困难</Option>
                  </Select>
                </Form.Item>
                <div className="setting-description">
                  控制验证码的识别难度
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      验证码长度
                      <Tooltip title="验证码字符数量">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="length"
                  rules={[
                    { required: true, message: "请输入验证码长度" },
                    { type: "number", min: 3, max: 8, message: "长度必须在3-8之间" }
                  ]}
                >
                  <InputNumber
                    min={3}
                    max={8}
                    placeholder="4"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：4，平衡安全性和用户体验
                </div>
              </Col>
            </Row>
          </Card>

          {/* 安全设置 */}
          <Card
            title={
              <Space>
                <SafetyOutlined />
                安全设置
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      过期时间 (秒)
                      <Tooltip title="验证码的有效时间">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="expireTime"
                  rules={[
                    { required: true, message: "请输入过期时间" },
                    { type: "number", min: 60, max: 1800, message: "过期时间必须在60-1800秒之间" }
                  ]}
                >
                  <InputNumber
                    min={60}
                    max={1800}
                    placeholder="300"
                    style={{ width: "100%" }}
                    addonAfter="秒"
                  />
                </Form.Item>
                <div className="setting-description">
                  推荐值：300秒（5分钟）
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <Space>
                      最大尝试次数
                      <Tooltip title="验证码错误的最大尝试次数">
                        <InfoCircleOutlined />
                      </Tooltip>
                    </Space>
                  }
                  name="maxAttempts"
                  rules={[
                    { required: true, message: "请输入最大尝试次数" },
                    { type: "number", min: 3, max: 10, message: "尝试次数必须在3-10之间" }
                  ]}
                >
                  <InputNumber
                    min={3}
                    max={10}
                    placeholder="5"
                    style={{ width: "100%" }}
                  />
                </Form.Item>
                <div className="setting-description">
                  超过次数后需要重新生成验证码
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="区分大小写"
                  name="caseSensitive"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  开启后验证码将区分大小写
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="包含数字"
                  name="includeNumbers"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  验证码中包含数字字符
                </div>
              </Col>
            </Row>

            <Row gutter={[24, 16]}>
              <Col span={12}>
                <Form.Item
                  label="包含字母"
                  name="includeLetters"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  验证码中包含字母字符
                </div>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="排除相似字符"
                  name="excludeSimilar"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <div className="setting-description">
                  排除容易混淆的字符，如 0/O, 1/l/I
                </div>
              </Col>
            </Row>
          </Card>

          {/* 测试区域 */}
          <Card
            title={
              <Space>
                <EyeOutlined />
                验证码测试
              </Space>
            }
            className="responsive-card-spacing"
          >
            <Row gutter={[24, 16]} align="middle">
              <Col span={12}>
                <Space>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleTestCaptcha}
                  >
                    生成测试验证码
                  </Button>
                </Space>
                <div className="setting-description">
                  点击生成验证码预览效果
                </div>
              </Col>
              <Col span={12}>
                {testCaptcha && (
                  <div style={{ textAlign: "center" }}>
                    <img
                      src={testCaptcha}
                      alt="测试验证码"
                      style={{
                        border: "1px solid #d9d9d9",
                        borderRadius: "4px",
                        padding: "8px",
                        background: "#fff",
                      }}
                    />
                  </div>
                )}
              </Col>
            </Row>
          </Card>

          <div style={{ textAlign: "right" }}>
            <Space>
              {captchaError && (
                <span style={{ color: '#ff4d4f', marginRight: 16 }}>
                  {captchaError}
                </span>
              )}
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={captchaSaving}
                disabled={!captchaIsDirty}
              >
                {captchaIsDirty ? '保存设置' : '已保存'}
              </Button>
            </Space>
          </div>
        </Form>
      </Spin>
    </div>
  );
};

export default CaptchaSettingsComponent;
