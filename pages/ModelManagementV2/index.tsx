import React, { useEffect, useMemo, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import { usePageRefresh } from '../../utils/refreshUtils';
import {
  createColumnHelper,
  type ColumnDef,
} from '@tanstack/react-table';
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  EyeOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SearchOutlined,
  MoreOutlined,
  AppstoreOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import {
  Button,
  Space,
  Switch,
  Tag,
  message,
  Modal,
  Tabs,
  Input,
  Dropdown,
  Select
} from 'antd';
import TanStackTable from '../../components/TanStackTable';
import { fuzzyFilter } from '../../components/TanStackTable/utils';
import { motion } from 'framer-motion';
import { usePageTransition } from '../../hooks/usePageTransition';
import { AnimationType } from '../../stores/global/pageAnimationSlice';
import { Model } from '../../types/model';
import ModelCreateForm from '../../components/ModelCreateForm';
import ApiSourceForm from '../../components/ApiSourceForm';
import { useModelsManagementStore, useFilteredModels, useFilteredApiSources, useModelsStats, useApiSourcesStats } from '../../stores';
import './style.scss';

// 临时 API 源类型定义
enum ApiSourceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error'
}

interface ApiSource {
  id: string;
  name: string;
  description?: string;
  type: string;
  apiUrl: string;
  apiKey?: string;
  status: ApiSourceStatus;
  isActive?: boolean;
  provider?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 创建列助手
const modelColumnHelper = createColumnHelper<Model>();
const apiSourceColumnHelper = createColumnHelper<ApiSource>();

interface ModelManagementV2Props {}

const ModelManagementV2: React.FC<ModelManagementV2Props> = () => {
  const location = useLocation();
  const {
    activeTab,
    isModelModalVisible,
    isApiSourceModalVisible,
    selectedModel,
    selectedApiSource,
    modelsLoading,
    apiSourcesLoading,
    setActiveTab,
    setIsModelModalVisible,
    setIsApiSourceModalVisible,
    setSelectedModel,
    setSelectedApiSource,
    setModelsLoading,
    setApiSourcesLoading,
    refreshAllData
  } = useModelsManagementStore();

  // 本地状态
  const [refreshKey, setRefreshKey] = React.useState(0);

  // 监听页面刷新事件
  usePageRefresh(useCallback((event) => {
    if (event.path === location.pathname) {
      console.log('🔄 ModelManagement received refresh event:', event);
      setRefreshKey(prev => prev + 1);
      // 这里可以添加重新获取数据的逻辑
      handleRefresh();
    }
  }, [location.pathname]));

  // TanStack Table 状态管理
  const {
    modelsSorting,
    modelsPagination,
    modelsColumnFilters,
    modelsGlobalFilter,
    apiSourcesSorting,
    apiSourcesPagination,
    apiSourcesColumnFilters,
    apiSourcesGlobalFilter,
    setModelsSorting,
    setModelsPagination,
    setModelsColumnFilters,
    setModelsGlobalFilter,
    setApiSourcesSorting,
    setApiSourcesPagination,
    setApiSourcesColumnFilters,
    setApiSourcesGlobalFilter
  } = useModelsManagementStore();

  // 使用过滤后的数据
  const filteredModels = useFilteredModels();
  const filteredApiSources = useFilteredApiSources();
  const modelsStats = useModelsStats();
  const apiSourcesStats = useApiSourcesStats();

  // 页面初始化时加载数据
  useEffect(() => {
    console.log('🚀 ModelManagement 组件挂载，开始初始化数据...');
    refreshAllData();
  }, []); // 只在组件挂载时执行一次

















  // 标签页切换 effect - 简化逻辑，移除不必要的状态
  useEffect(() => {
    if (activeTab === 'models') {
      setModelsLoading(true);
    } else {
      setApiSourcesLoading(true);
    }
    const timer = setTimeout(() => {
      if (activeTab === 'models') {
        setModelsLoading(false);
      } else {
        setApiSourcesLoading(false);
      }
    }, 300);

    // 清理定时器
    return () => clearTimeout(timer);
  }, [activeTab]); // 只依赖 activeTab

  const handleToggleStatus = async () => {
    try {
      if (activeTab === 'models') {
        setModelsLoading(true);
      } else {
        setApiSourcesLoading(true);
      }
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success(`${activeTab === 'models' ? '模型' : 'API源'}状态更新成功`);
    } catch (error) {
      message.error('操作失败');
    } finally {
      if (activeTab === 'models') {
        setModelsLoading(false);
      } else {
        setApiSourcesLoading(false);
      }
    }
  };

  const handleEdit = (record: Model | ApiSource) => {
    if (activeTab === 'models') {
      setSelectedModel(record as Model);
      setIsModelModalVisible(true);
    } else {
      setSelectedApiSource(record as ApiSource);
      setIsApiSourceModalVisible(true);
    }
  };

  const handleDelete = (record: Model | ApiSource) => {
    Modal.confirm({
      title: `确定要删除${activeTab === 'models' ? '模型' : 'API源'}？`,
      content: `这将永久删除${record.name}，此操作不可恢复。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          if (activeTab === 'models') {
            setModelsLoading(true);
          } else {
            setApiSourcesLoading(true);
          }
          await new Promise(resolve => setTimeout(resolve, 500));
          message.success('删除成功');
        } catch (error) {
          message.error('删除失败');
        } finally {
          if (activeTab === 'models') {
            setModelsLoading(false);
          } else {
            setApiSourcesLoading(false);
          }
        }
      }
    });
  };

  const handleCreateNew = () => {
    if (activeTab === 'models') {
      setSelectedModel(null);
      setIsModelModalVisible(true);
    } else {
      setSelectedApiSource(null);
      setIsApiSourceModalVisible(true);
    }
  };

  const handleModalClose = () => {
    setIsModelModalVisible(false);
    setIsApiSourceModalVisible(false);
    setSelectedModel(null);
    setSelectedApiSource(null);
  };

  const handleModalSubmit = async () => {
    try {
      if (activeTab === 'models') {
        setModelsLoading(true);
      } else {
        setApiSourcesLoading(true);
      }
      await new Promise(resolve => setTimeout(resolve, 500));
      
      if (activeTab === 'models') {
        message.success(selectedModel ? '模型更新成功' : '模型创建成功');
      } else {
        message.success(selectedApiSource ? 'API源更新成功' : 'API源创建成功');
      }
      
      handleModalClose();
    } catch (error) {
      message.error('操作失败');
    } finally {
      if (activeTab === 'models') {
        setModelsLoading(false);
      } else {
        setApiSourcesLoading(false);
      }
    }
  };



  const handleRefresh = async () => {
    try {
      console.log('🔄 手动刷新数据...');
      await refreshAllData();
      message.success('刷新成功');
    } catch (error) {
      console.error('刷新失败:', error);
      message.error('刷新失败');
    }
  };

  // 模型表格列配置 - TanStack Table 格式
  const modelColumns = useMemo<ColumnDef<Model, any>[]>(() => [
    modelColumnHelper.accessor('name', {
      id: 'name',
      header: '模型名称',
      size: 200,
      enableSorting: true,
      cell: ({ getValue }) => (
        <Space>
          <DatabaseOutlined style={{ color: '#1890ff' }} />
          <span>{getValue()}</span>
        </Space>
      ),
    }),
    modelColumnHelper.accessor('type', {
      id: 'type',
      header: '类型',
      size: 100,
      enableSorting: false,
      cell: ({ getValue }) => <Tag color="blue">{getValue()}</Tag>,
    }),
    modelColumnHelper.accessor('vendor', {
      id: 'vendor',
      header: '供应商',
      size: 120,
      enableSorting: false,
      cell: ({ getValue }) => <Tag color="green">{getValue()}</Tag>,
    }),
    modelColumnHelper.display({
      id: 'features',
      header: '功能特性',
      size: 212,
      cell: ({ row }) => {
        const record = row.original;
        return (
          <Space wrap size={[2, 2]}>
            {record.enableVision && <Tag color="purple" data-feature="true">视觉</Tag>}
            {record.enableFunctionCalling && <Tag color="orange" data-feature="true">函数调用</Tag>}
            {record.enableInference && <Tag color="cyan" data-feature="true">推理</Tag>}
            {record.enableOnline && <Tag color="magenta" data-feature="true">在线</Tag>}
          </Space>
        );
      },
    }),
    modelColumnHelper.accessor('status', {
      id: 'status',
      header: '状态',
      size: 100,
      enableSorting: false,
      cell: ({ getValue }) => (
        <Switch
          checked={getValue()}
          onChange={() => handleToggleStatus()}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    }),
    modelColumnHelper.accessor('createdAt', {
      id: 'createdAt',
      header: '创建时间',
      size: 160,
      enableSorting: true,
      cell: ({ getValue }) => getValue(),
    }),
    modelColumnHelper.display({
      id: 'actions',
      header: '操作',
      size: 120,
      cell: ({ row }) => {
        const record = row.original;
        return (
          <Space>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleEdit(record)}
            />
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'delete',
                    label: '删除',
                    onClick: () => handleDelete(record)
                  }
                ]
              }}
            >
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      },
    }),
  ], []);

  // API源表格列配置 - TanStack Table 格式
  const apiSourceColumns = useMemo<ColumnDef<ApiSource, any>[]>(() => [
    apiSourceColumnHelper.accessor('name', {
      id: 'name',
      header: 'API源名称',
      size: 200,
      enableSorting: true,
      cell: ({ getValue }) => (
        <Space>
          <ApiOutlined style={{ color: '#1890ff' }} />
          <span>{getValue()}</span>
        </Space>
      ),
    }),
    apiSourceColumnHelper.accessor('type', {
      id: 'type',
      header: '供应商类型',
      size: 120,
      enableSorting: false,
      cell: ({ getValue }) => {
        const type = getValue();
        const colorMap: Record<string, string> = {
          openai: 'green',
          anthropic: 'purple',
          google: 'blue',
          azure: 'orange',
          claude: 'magenta',
        };
        return <Tag color={colorMap[type] || 'default'}>{type.toUpperCase()}</Tag>;
      },
    }),
    apiSourceColumnHelper.accessor('apiUrl', {
      id: 'apiUrl',
      header: '基础URL',
      size: 250,
      enableSorting: false,
      cell: ({ getValue }) => (
        <span style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          display: 'block'
        }}>
          {getValue()}
        </span>
      ),
    }),
    apiSourceColumnHelper.accessor('description', {
      id: 'description',
      header: '描述信息',
      size: 200,
      enableSorting: false,
      cell: ({ getValue }) => (
        <span style={{ color: '#8c8c8c' }}>
          {getValue() || '暂无描述'}
        </span>
      ),
    }),
    apiSourceColumnHelper.accessor('status', {
      id: 'status',
      header: '状态',
      size: 100,
      enableSorting: false,
      cell: ({ getValue }) => (
        <Switch
          checked={getValue() === ApiSourceStatus.ACTIVE}
          onChange={() => handleToggleStatus()}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    }),
    apiSourceColumnHelper.accessor('createdAt', {
      id: 'createdAt',
      header: '创建时间',
      size: 160,
      enableSorting: true,
      cell: ({ getValue }) => getValue(),
    }),
    apiSourceColumnHelper.display({
      id: 'actions',
      header: '操作',
      size: 120,
      cell: ({ row }) => {
        const record = row.original;
        return (
          <Space>
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleEdit(record)}
            />
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'delete',
                    label: '删除',
                    onClick: () => handleDelete(record)
                  }
                ]
              }}
            >
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          </Space>
        );
      },
    }),
  ], []);

  // TanStack Table 事件处理函数
  const handleModelPageChange = useCallback((pageIndex: number, pageSize: number) => {
    setModelsPagination({ pageIndex, pageSize });
  }, []);

  const handleApiSourcePageChange = useCallback((pageIndex: number, pageSize: number) => {
    setApiSourcesPagination({ pageIndex, pageSize });
  }, []);

  const handleRowClick = useCallback((record: Model | ApiSource) => {
    console.log('点击行:', record);
  }, []);

  // 稳定的过滤函数引用
  const stableFilterFns = useMemo(() => ({
    fuzzy: fuzzyFilter,
  }), []);

  // 稳定的表格配置引用
  const stableTableConfig = useMemo(() => ({
    size: 'standard' as const,
    emptyStateHeight: 280,
    minRowsForDynamic: 10,
    pageSizeOptions: [10, 20, 50, 100],
    defaultPageSize: 10,
    minTableWidth: 1200,
  }), []);

  // 稳定的模型表格事件引用
  const stableModelEvents = useMemo(() => ({
    onSortingChange: setModelsSorting,
    onPageChange: handleModelPageChange,
    onColumnFiltersChange: setModelsColumnFilters,
    onGlobalFilterChange: setModelsGlobalFilter,
    onRowClick: handleRowClick,
  }), [handleModelPageChange, handleRowClick]);

  // 稳定的API源表格事件引用
  const stableApiSourceEvents = useMemo(() => ({
    onSortingChange: setApiSourcesSorting,
    onPageChange: handleApiSourcePageChange,
    onColumnFiltersChange: setApiSourcesColumnFilters,
    onGlobalFilterChange: setApiSourcesGlobalFilter,
    onRowClick: handleRowClick,
  }), [handleApiSourcePageChange, handleRowClick]);

  // 页面动画配置
  const pageTransition = usePageTransition({
    type: AnimationType.FADE_IN_UP,
    duration: 0.6,
    delay: 0,
  });

  return (
    <motion.div
      {...pageTransition}
    >
      <div className="model-management-v2-pro" key={refreshKey}>
        {/* 主表格区域 */}
        <div className="unified-container">
          <Tabs
            activeKey={activeTab}
            onChange={(key) => {
              setActiveTab(key as 'models' | 'apiSources');
            }}
            animated={false}
            className="modern-tabs"
            items={[
              {
                key: 'models',
                label: (
                  <span className="tab-trigger">
                    <DatabaseOutlined />
                    模型管理
                  </span>
                ),
              children: (
                <div className="tab-content">
                  {/* 紧凑操作区域 */}
                  <div className="compact-action-section">
                    <div className="stats-summary">
                      <Space size={16} className="summary-items">
                        <span className="summary-item">
                          <DatabaseOutlined className="summary-icon" />
                          总计: <strong>{modelsStats.total}</strong>
                        </span>
                        <span className="summary-item">
                          <CheckCircleOutlined className="summary-icon enabled" />
                          启用: <strong>{modelsStats.enabledCount}</strong>
                        </span>
                        <span className="summary-item">
                          <AppstoreOutlined className="summary-icon" />
                          类型: <strong>{Object.keys(modelsStats.typeStats).length}种</strong>
                        </span>
                      </Space>
                    </div>
                    <div className="action-controls">
                      <Space size={8}>
                        <Input
                          placeholder="搜索模型..."
                          prefix={<SearchOutlined />}
                          allowClear
                          value={modelsGlobalFilter}
                          onChange={(e) => setModelsGlobalFilter(e.target.value)}
                          onPressEnter={(e) => setModelsGlobalFilter((e.target as HTMLInputElement).value)}
                          className="compact-search"
                          style={{ width: 200 }}
                        />
                        <Select
                          placeholder="筛选类型"
                          allowClear
                          style={{ width: 120 }}
                          options={[
                            { label: '全部类型', value: '' },
                            { label: 'OpenAI', value: 'openai' },
                            { label: 'Anthropic', value: 'anthropic' },
                            { label: 'Google', value: 'google' },
                          ]}
                        />
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={handleRefresh}
                          loading={modelsLoading}
                          size="small"
                        />
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={handleCreateNew}
                          size="small"
                        >
                          新建模型
                        </Button>
                      </Space>
                    </div>
                  </div>

                  {/* 表格容器 */}
                  <div className="table-section">
                    <TanStackTable
                    data={filteredModels}
                    columns={modelColumns}
                    loading={modelsLoading}
                    sorting={modelsSorting}
                    pagination={modelsPagination}
                    columnFilters={modelsColumnFilters}
                    globalFilter={modelsGlobalFilter}
                    filterFns={stableFilterFns}
                    config={stableTableConfig}
                    events={stableModelEvents}
                    showPagination={true}
                    className="model-management-table"
                  />
                  </div>
                </div>
              ),
            },
            {
              key: 'apiSources',
              label: (
                <span className="tab-trigger">
                  <ApiOutlined />
                  API源管理
                </span>
              ),
              children: (
                <div className="tab-content">
                  {/* 紧凑操作区域 */}
                  <div className="compact-action-section">
                    <div className="stats-summary">
                      <Space size={16} className="summary-items">
                        <span className="summary-item">
                          <ApiOutlined className="summary-icon" />
                          总计: <strong>{apiSourcesStats.total}</strong>
                        </span>
                        <span className="summary-item">
                          <CheckCircleOutlined className="summary-icon enabled" />
                          启用: <strong>{apiSourcesStats.statusStats.active || 0}</strong>
                        </span>
                        <span className="summary-item">
                          <AppstoreOutlined className="summary-icon" />
                          供应商: <strong>{Object.keys(apiSourcesStats.typeStats).length}个</strong>
                        </span>
                      </Space>
                    </div>
                    <div className="action-controls">
                      <Space size={8}>
                        <Input
                          placeholder="搜索API源..."
                          prefix={<SearchOutlined />}
                          allowClear
                          value={apiSourcesGlobalFilter}
                          onChange={(e) => setApiSourcesGlobalFilter(e.target.value)}
                          onPressEnter={(e) => setApiSourcesGlobalFilter((e.target as HTMLInputElement).value)}
                          className="compact-search"
                          style={{ width: 200 }}
                        />
                        <Select
                          placeholder="筛选供应商"
                          allowClear
                          style={{ width: 120 }}
                          options={[
                            { label: '全部供应商', value: '' },
                            { label: 'OpenAI', value: 'openai' },
                            { label: 'Anthropic', value: 'anthropic' },
                            { label: 'Google', value: 'google' },
                          ]}
                        />
                        <Button
                          icon={<ReloadOutlined />}
                          onClick={handleRefresh}
                          loading={apiSourcesLoading}
                          size="small"
                        />
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          onClick={handleCreateNew}
                          size="small"
                        >
                          新建API源
                        </Button>
                      </Space>
                    </div>
                  </div>

                  {/* 表格容器 */}
                  <div className="table-section">
                    <TanStackTable
                    data={filteredApiSources}
                    columns={apiSourceColumns}
                    loading={apiSourcesLoading}
                    sorting={apiSourcesSorting}
                    pagination={apiSourcesPagination}
                    columnFilters={apiSourcesColumnFilters}
                    globalFilter={apiSourcesGlobalFilter}
                    filterFns={stableFilterFns}
                    config={stableTableConfig}
                    events={stableApiSourceEvents}
                    showPagination={true}
                    className="api-source-management-table"
                  />
                  </div>
                </div>
              ),
            },
          ]}
        />
        </div>

      {/* 模态框 */}
      {isModelModalVisible && (
        <ModelCreateForm
          visible={isModelModalVisible}
          onCancel={handleModalClose}
          onSubmit={handleModalSubmit}
          loading={modelsLoading}
        />
      )}

      {isApiSourceModalVisible && (
        <ApiSourceForm
          visible={isApiSourceModalVisible}
          onCancel={handleModalClose}
          onSubmit={handleModalSubmit}
          loading={apiSourcesLoading}
          editingRecord={selectedApiSource || undefined}
        />
      )}
      </div>
    </motion.div>
  );
};

export default ModelManagementV2;