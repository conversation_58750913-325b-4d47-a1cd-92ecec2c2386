@use '../../styles/variables' as *;

.theme-demo-page {
  padding: 24px;
  min-height: 100vh;
  background: var(--theme-bg-tertiary, #f5f5f5);
  
  .demo-header {
    margin-bottom: 24px;
    padding: 24px;
    background: var(--theme-bg-primary, #ffffff);
    border-radius: 8px;
    box-shadow: var(--theme-shadow-1);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;
    
    .header-content {
      flex: 1;
      
      h2 {
        margin-bottom: 16px;
        color: var(--theme-text-primary);
        
        .anticon {
          margin-right: 8px;
          color: var(--theme-primary, #2464F1);
        }
      }
      
      p {
        color: var(--theme-text-secondary);
        margin-bottom: 0;
      }
    }
    
    .theme-controls {
      flex-shrink: 0;
      
      .control-group {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .ant-typography {
          margin-bottom: 0;
          color: var(--theme-text-primary);
        }
      }
    }
    
    // 响应式
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      
      .theme-controls {
        .ant-space {
          flex-direction: column;
          width: 100%;
          
          .ant-space-item {
            width: 100%;
            
            .control-group {
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
  
  .demo-content {
    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 24px;
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
    }
  }
  
  // 卡片样式
  .demo-card,
  .info-card,
  .feature-card {
    .ant-card-head {
      background: var(--theme-bg-secondary, #fafafa);
      border-bottom: 1px solid var(--theme-border-color-split, #f0f0f0);
      
      .ant-card-head-title {
        color: var(--theme-text-primary);
        font-weight: 600;
      }
    }
    
    .ant-card-body {
      background: var(--theme-bg-primary, #ffffff);
    }
  }
  
  // 信息卡片
  .info-card {
    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      
      .ant-typography {
        margin-bottom: 0;
      }
    }
  }
  
  // 颜色展示
  .color-palette {
    .color-group {
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .ant-typography {
        margin-bottom: 12px;
        color: var(--theme-text-primary);
      }
    }
    
    .color-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 12px;
    }
    
    .color-item {
      padding: 16px 12px;
      border-radius: 6px;
      text-align: center;
      font-weight: 500;
      font-size: 12px;
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      // 主要颜色
      &.primary {
        background: var(--theme-primary, #2464F1);
        color: white;
      }
      
      &.success {
        background: #52c41a;
        color: white;
      }
      
      &.warning {
        background: #faad14;
        color: white;
      }
      
      &.error {
        background: #ff4d4f;
        color: white;
      }
      
      // 背景颜色
      &.bg-primary {
        background: var(--theme-bg-primary, #ffffff);
        color: var(--theme-text-primary);
        border: 1px solid var(--theme-border-color, #d9d9d9);
      }
      
      &.bg-secondary {
        background: var(--theme-bg-secondary, #fafafa);
        color: var(--theme-text-primary);
        border: 1px solid var(--theme-border-color, #d9d9d9);
      }
      
      &.bg-tertiary {
        background: var(--theme-bg-tertiary, #f5f5f5);
        color: var(--theme-text-primary);
        border: 1px solid var(--theme-border-color, #d9d9d9);
      }
    }
  }
  
  // 特性卡片
  .feature-card {
    .feature-item {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .ant-typography {
        margin-bottom: 8px;
        
        &.ant-typography-caption {
          margin-bottom: 0;
        }
      }
    }
  }
  
  // 深色模式特殊处理
  .dark & {
    .demo-header {
      background: var(--theme-bg-primary, #141414);
      border: 1px solid var(--theme-border-color, #424242);
    }
    
    .color-item {
      &:hover {
        box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
      }
    }
  }
  
  // 动画效果
  .demo-card,
  .info-card,
  .feature-card {
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--theme-shadow-2);
    }
  }
  
  // 表格样式
  .ant-table {
    .ant-table-thead > tr > th {
      background: var(--theme-bg-secondary, #fafafa);
      color: var(--theme-text-primary);
      border-bottom: 1px solid var(--theme-border-color-split);
    }
    
    .ant-table-tbody > tr > td {
      background: var(--theme-bg-primary, #ffffff);
      color: var(--theme-text-primary);
      border-bottom: 1px solid var(--theme-border-color-split);
    }
    
    .ant-table-tbody > tr:hover > td {
      background: var(--theme-fill-quaternary, rgba(0, 0, 0, 0.02));
    }
  }
  
  // 确保所有组件都能正确响应主题变化
  * {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }
}