import React from 'react';
import { Card, Space, Typography, Button, Table, Tag, Divider } from 'antd';
import { UserOutlined, SettingOutlined, HomeOutlined } from '@ant-design/icons';
import ThemeSwitcher, { ThemeSelector } from '../../components/ThemeSwitcher';
import { useTheme } from '../../stores/settings/themeSlice';
import './style.scss';

const { Title, Paragraph, Text } = Typography;

/**
 * 主题演示页面
 * 用于展示主题切换动画效果和各种组件在不同主题下的表现
 */
const ThemeDemo: React.FC = () => {
  const { mode, actualTheme, isDark } = useTheme();

  // 示例表格数据
  const tableData = [
    {
      key: '1',
      name: '张三',
      age: 32,
      address: '上海市浦东新区',
      status: 'active',
    },
    {
      key: '2',
      name: '李四',
      age: 28,
      address: '北京市朝阳区',
      status: 'inactive',
    },
    {
      key: '3',
      name: '王五',
      age: 35,
      address: '深圳市南山区',
      status: 'active',
    },
  ];

  const tableColumns = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      key: 'age',
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      ),
    },
  ];

  return (
    <div className="theme-demo-page">
      <div className="demo-header">
        <div className="header-content">
          <Title level={2}>
            <SettingOutlined /> 主题切换演示
          </Title>
          <Paragraph>
            演示炫酷的主题切换动画效果，使用 View Transition API 实现从点击位置向外扩散的圆形过渡动画。
          </Paragraph>
        </div>
        
        <div className="theme-controls">
          <Space size="large">
            <div className="control-group">
              <Text strong>快速切换:</Text>
              <ThemeSwitcher mode="icon" showTooltip={true} />
            </div>
            <div className="control-group">
              <Text strong>模式选择:</Text>
              <ThemeSelector />
            </div>
            <div className="control-group">
              <Text strong>带文字:</Text>
              <ThemeSwitcher mode="both" showTooltip={false} />
            </div>
          </Space>
        </div>
      </div>

      <div className="demo-content">
        <div className="demo-grid">
          {/* 当前主题信息 */}
          <Card 
            title="当前主题信息" 
            className="info-card"
            extra={<HomeOutlined />}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="info-item">
                <Text strong>主题模式: </Text>
                <Tag color="blue">{mode}</Tag>
              </div>
              <div className="info-item">
                <Text strong>实际主题: </Text>
                <Tag color={isDark ? 'purple' : 'orange'}>{actualTheme}</Tag>
              </div>
              <div className="info-item">
                <Text strong>是否暗色: </Text>
                <Tag color={isDark ? 'green' : 'red'}>{isDark ? '是' : '否'}</Tag>
              </div>
            </Space>
          </Card>

          {/* 按钮演示 */}
          <Card title="按钮组件演示" className="demo-card">
            <Space wrap>
              <Button type="primary">主要按钮</Button>
              <Button>默认按钮</Button>
              <Button type="dashed">虚线按钮</Button>
              <Button type="link">链接按钮</Button>
              <Button danger>危险按钮</Button>
              <Button type="primary" icon={<UserOutlined />}>
                图标按钮
              </Button>
            </Space>
          </Card>

          {/* 表格演示 */}
          <Card title="表格组件演示" className="demo-card">
            <Table 
              dataSource={tableData} 
              columns={tableColumns} 
              pagination={false}
              size="small"
            />
          </Card>

          {/* 颜色系统 */}
          <Card title="颜色系统" className="demo-card">
            <div className="color-palette">
              <div className="color-group">
                <Text strong>主要颜色</Text>
                <div className="color-row">
                  <div className="color-item primary">Primary</div>
                  <div className="color-item success">Success</div>
                  <div className="color-item warning">Warning</div>
                  <div className="color-item error">Error</div>
                </div>
              </div>
              
              <Divider />
              
              <div className="color-group">
                <Text strong>背景颜色</Text>
                <div className="color-row">
                  <div className="color-item bg-primary">Primary BG</div>
                  <div className="color-item bg-secondary">Secondary BG</div>
                  <div className="color-item bg-tertiary">Tertiary BG</div>
                </div>
              </div>
            </div>
          </Card>

          {/* 技术特性 */}
          <Card title="技术特性" className="feature-card">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div className="feature-item">
                <Text strong>🎨 View Transition API</Text>
                <Paragraph type="secondary">
                  使用现代浏览器的原生过渡动画API，实现丝滑的主题切换效果
                </Paragraph>
              </div>
              
              <div className="feature-item">
                <Text strong>🌈 CSS clip-path</Text>
                <Paragraph type="secondary">
                  通过圆形裁剪路径创建从点击位置向外扩散的动画效果
                </Paragraph>
              </div>
              
              <div className="feature-item">
                <Text strong>🔄 智能降级</Text>
                <Paragraph type="secondary">
                  自动检测浏览器兼容性，不支持时降级为无动画切换
                </Paragraph>
              </div>
              
              <div className="feature-item">
                <Text strong>♿ 无障碍支持</Text>
                <Paragraph type="secondary">
                  尊重用户的减少动画偏好设置，提供完整的键盘导航支持
                </Paragraph>
              </div>
            </Space>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ThemeDemo;