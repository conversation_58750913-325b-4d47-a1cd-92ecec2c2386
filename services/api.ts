/**
 * V2 Admin API Service
 * 基于主项目的API服务，适配V2 Admin项目结构
 */
import axios, { AxiosInstance } from 'axios';
import authUtils from '../utils/auth';
import { getEnvConfig } from '../utils/envConfig';

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data?: T;
}

// 获取环境配置
const envConfig = getEnvConfig();

// 调试信息：打印跨域配置
console.log('🔗 API 配置信息:');
console.log('  - Base URL:', envConfig.api.baseUrl);
console.log('  - 使用代理:', envConfig.api.useProxy);
console.log('  - 代理目标:', envConfig.api.proxyTarget);
console.log('  - 原始环境变量:', import.meta.env.VITE_API_BASE_URL);

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: envConfig.api.baseUrl,
  timeout: envConfig.api.timeout,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = authUtils.getToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 401 未授权错误处理
    if (error.response?.status === 401) {
      authUtils.clearAuthAndRedirect();
    }
    
    // 网络错误增强处理
    if (!error.response && error.code === 'NETWORK_ERROR') {
      console.error('网络连接失败，请检查网络连接或后端服务状态');
    }
    
    // 超时错误处理
    if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
      console.error('请求超时，请检查网络连接或稍后重试');
    }
    
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 登录
  login: (data: { username: string; password: string; captchaId?: string; captcha?: string }) => {
    return api.post('/admin/login', data);
  },

  // 获取验证码
  getCaptcha: () => {
    return api.get('/admin/captcha');
  },

  // 获取用户信息
  getProfile: () => {
    return api.get('/admin/profile');
  },

  // 登出
  logout: () => {
    return api.post('/admin/logout');
  },
};

// 用户管理API
export const userAPI = {
  // 获取用户列表
  getUsers: (params?: any) => {
    return api.get('/admin/users', { params });
  },

  // 获取单个用户详情
  getUserById: (id: string) => {
    return api.get(`/admin/users/${id}`);
  },

  // 创建用户
  createUser: (userData: any) => {
    return api.post('/admin/users', userData);
  },

  // 更新用户
  updateUser: (userId: string, userData: any) => {
    return api.put(`/admin/users/${userId}`, userData);
  },

  // 删除用户
  deleteUser: (userId: string) => {
    return api.delete(`/admin/users/${userId}`);
  },

  // 删除管理员用户（需要密码验证）
  deleteAdminUser: (id: string, password: string) => {
    return api.delete(`/admin/users/${id}`, {
      data: { password }
    });
  },

  // 批量删除用户
  batchDeleteUsers: (userIds: string[], password?: string) => {
    return api.post('/admin/users/batch-delete', { userIds, password });
  },

  // 修改用户状态
  updateUserStatus: (id: string, status: boolean, password?: string) => {
    const requestData: any = {
      status: status ? "true" : "false"
    };

    if (password) {
      requestData.password = password;
    }

    return api.put(`/admin/users/${id}/status`, requestData);
  },

  // 上传头像
  uploadAvatar: (file: File) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return api.post('/admin/users/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 登录
  login: (credentials: { username: string; password: string }) => 
    api.post('/admin/login', credentials),
};

// 助手管理API
export const assistantAPI = {
  getAssistants: (params?: any) => 
    api.get('/admin/assistants', { params }),
  
  createAssistant: (assistantData: any) => 
    api.post('/admin/assistants', assistantData),
  
  updateAssistant: (assistantId: string, assistantData: any) => 
    api.put(`/admin/assistants/${assistantId}`, assistantData),
  
  deleteAssistant: (assistantId: string) => 
    api.delete(`/admin/assistants/${assistantId}`),
};

// 模型管理API
export const modelAPI = {
  // 获取模型列表
  getModels: (params?: any) => 
    api.get('/admin/models', { params }),
  
  // 创建模型
  createModel: (modelData: any) => 
    api.post('/admin/models', modelData),
  
  // 更新模型
  updateModel: (modelId: string, modelData: any) => 
    api.put(`/admin/models/${modelId}`, modelData),
  
  // 删除模型
  deleteModel: (modelId: string) => 
    api.delete(`/admin/models/${modelId}`),
  
  // 更新模型状态
  updateModelStatus: (modelId: string, status: boolean) => 
    api.put(`/admin/models/${modelId}/status`, { status }),
  
  // 批量更新模型状态
  batchUpdateModelStatus: (modelIds: string[], status: boolean) => 
    api.put('/admin/models/batch-status', { modelIds, status }),
};

// API源管理API
export const apiSourceAPI = {
  // 获取API源列表
  getApiSources: () => 
    api.get('/admin/api-sources'),
  
  // 获取API源模板
  getApiSourceTemplates: () => 
    api.get('/public/api-templates'),
  
  // 获取单个API源详情
  getApiSourceById: (id: string) => 
    api.get(`/admin/api-sources/${id}`),
  
  // 创建API源
  createApiSource: (data: any) => 
    api.post('/admin/api-sources', data),
  
  // 更新API源
  updateApiSource: (id: string, data: any) => 
    api.put(`/admin/api-sources/${id}`, data),
  
  // 删除API源
  deleteApiSource: (id: string) => 
    api.delete(`/admin/api-sources/${id}`),
  
  // 更新API源状态
  updateApiSourceStatus: (id: string, status: string) => 
    api.put(`/admin/api-sources/${id}/status`, { status }),
  
  // 测试API源连接
  testApiSource: (id: string) => 
    api.post(`/admin/api-sources/${id}/test`),
};

// 知识库管理API
export const knowledgeBaseAPI = {
  // ==================== 知识库CRUD操作 ====================

  // 获取知识库列表（支持分页、搜索、筛选）
  getKnowledgeBases: (params?: any) => {
    return api.get('/admin/knowledge-bases', { params });
  },

  // 获取知识库详情
  getKnowledgeBase: (id: string) => {
    return api.get(`/admin/knowledge-bases/${id}`);
  },

  // 创建知识库
  createKnowledgeBase: (kbData: any) => {
    return api.post('/admin/knowledge-bases', kbData);
  },

  // 更新知识库
  updateKnowledgeBase: (kbId: string, kbData: any) => {
    return api.put(`/admin/knowledge-bases/${kbId}`, kbData);
  },

  // 删除知识库
  deleteKnowledgeBase: (kbId: string) => {
    return api.delete(`/admin/knowledge-bases/${kbId}`);
  },

  // 批量删除知识库
  batchDeleteKnowledgeBases: (ids: string[]) => {
    return api.post('/admin/knowledge-bases/batch-delete', { ids });
  },

  // ==================== 文档管理 ====================

  // 获取知识库文档列表
  getDocuments: (knowledgeBaseId: string, params?: {
    search?: string;
    type?: string;
    status?: string;
    page?: number;
    page_size?: number;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }) => {
    return api.get(`/admin/knowledge-bases/${knowledgeBaseId}/documents`, { params });
  },

  // 上传文档
  uploadDocuments: (knowledgeBaseId: string, files: File[]) => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    return api.post(`/admin/knowledge-bases/${knowledgeBaseId}/documents/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // 删除文档
  deleteDocument: (knowledgeBaseId: string, documentId: string) => {
    return api.delete(`/admin/knowledge-bases/${knowledgeBaseId}/documents/${documentId}`);
  },

  // 获取文档分块
  getDocumentChunks: (knowledgeBaseId: string, documentId: string, params?: {
    search?: string;
    page?: number;
    page_size?: number;
  }) => {
    return api.get(`/admin/knowledge-bases/${knowledgeBaseId}/documents/${documentId}/chunks`, { params });
  },

  // ==================== 统计信息 ====================

  // 获取知识库统计
  getKnowledgeBaseStats: () => {
    return api.get('/admin/knowledge-bases/stats');
  },
};

// 系统设置API
export const settingsAPI = {
  getSettings: () => 
    api.get('/admin/settings'),
  
  updateSettings: (settings: any) => 
    api.put('/admin/settings', settings),
};

// 验证码API
export const captchaAPI = {
  getCaptchaSettings: () => 
    api.get('/admin/captcha/settings'),
  
  updateCaptchaSettings: (settings: any) => 
    api.put('/admin/captcha/settings', settings),
  
  testCaptcha: (captchaType: string) => 
    api.post('/admin/captcha/test', { type: captchaType }),
};

// 导出 API 实例
export { api };
export default api;
