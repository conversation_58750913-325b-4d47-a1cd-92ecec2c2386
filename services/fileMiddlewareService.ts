/**
 * 文件中间件设置服务 - V2 Admin版本
 * 处理文件中间件相关的API调用
 */

import { api } from './api';

// 文件中间件设置数据类型
export interface FileMiddlewareSettings {
  enabled: boolean;
  maxFileSize: number; // MB
  allowedExtensions: string[];
  storageType: "local" | "s3" | "oss" | "cos";
  storagePath: string;
  tempPath: string;
  compressionEnabled: boolean;
  compressionQuality?: number;
  virusScanEnabled: boolean;
  encryptFilenames: boolean;
  forbiddenPatterns?: string[];
}

// 文件中间件测试请求
export interface FileMiddlewareTestRequest {
  testType: 'upload' | 'scan' | 'compression';
  fileSize?: number;
  fileExtension?: string;
}

// 文件中间件测试响应
export interface FileMiddlewareTestResponse {
  success: boolean;
  message: string;
  details?: any;
}

// 文件中间件设置服务
class FileMiddlewareService {
  /**
   * 获取文件中间件设置
   */
  async getSettings(): Promise<FileMiddlewareSettings> {
    try {
      const response = await api.get('/api/settings/file-middleware');

      // 如果API返回数据，使用API数据
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }

      // 否则返回默认设置
      return this.getDefaultSettings();
    } catch (error) {
      console.warn('Failed to load file middleware settings from API, using defaults:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * 更新文件中间件设置
   */
  async updateSettings(settings: FileMiddlewareSettings): Promise<void> {
    try {
      const response = await api.put('/api/settings/file-middleware', settings);

      if (!response.data?.success) {
        throw new Error(response.data?.message || '保存文件中间件设置失败');
      }
    } catch (error: any) {
      console.error('Failed to update file middleware settings:', error);
      throw new Error(error.response?.data?.message || error.message || '保存文件中间件设置失败');
    }
  }

  /**
   * 测试文件中间件功能
   */
  async testFileMiddleware(request: FileMiddlewareTestRequest): Promise<FileMiddlewareTestResponse> {
    try {
      const response = await api.post('/api/file-middleware/test', request);

      if (response.data?.success) {
        return {
          success: true,
          message: response.data.message || '测试成功',
          details: response.data.data
        };
      } else {
        return {
          success: false,
          message: response.data?.message || '测试失败'
        };
      }
    } catch (error: any) {
      console.error('Failed to test file middleware:', error);
      return {
        success: false,
        message: error.response?.data?.message || error.message || '文件中间件测试失败'
      };
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings(): FileMiddlewareSettings {
    return {
      enabled: true,
      maxFileSize: 10,
      allowedExtensions: [".pdf", ".doc", ".docx", ".txt", ".md"],
      storageType: "local",
      storagePath: "/uploads",
      tempPath: "/tmp",
      compressionEnabled: true,
      compressionQuality: 80,
      virusScanEnabled: false,
      encryptFilenames: false,
      forbiddenPatterns: [],
    };
  }
}

// 创建并导出服务实例
export const fileMiddlewareService = new FileMiddlewareService();
