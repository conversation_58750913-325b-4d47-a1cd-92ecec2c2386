// 验证码设置服务 - V2 Admin版本
import { captchaAPI } from "./api";

// 验证码设置类型定义
export interface CaptchaSettings {
  enabled: boolean;
  type: "image" | "math" | "text";
  difficulty: "easy" | "medium" | "hard";
  length: number;
  expireTime: number; // 秒
  maxAttempts: number;
  caseSensitive: boolean;
  includeNumbers?: boolean;
  includeLetters?: boolean;
  excludeSimilar?: boolean;
}

// 验证码测试数据类型
export interface CaptchaTestData {
  id: string;
  image: string; // base64 图片数据
  answer: string;
}

// 重试配置
const RETRY_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000, // 1秒
};

// 延迟函数
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 带重试的请求函数
const requestWithRetry = async <T>(
  requestFn: () => Promise<T>,
  retries = RETRY_CONFIG.maxRetries
): Promise<T> => {
  try {
    return await requestFn();
  } catch (error) {
    if (retries > 0) {
      console.warn(`请求失败，${RETRY_CONFIG.retryDelay / 1000}秒后重试...剩余重试次数: ${retries}`);
      await delay(RETRY_CONFIG.retryDelay);
      return requestWithRetry(requestFn, retries - 1);
    }
    throw error;
  }
};

// 验证码设置服务
export const captchaService = {
  // 获取验证码设置
  getSettings: async (): Promise<CaptchaSettings> => {
    try {
      const response = await requestWithRetry(() => captchaAPI.getCaptchaSettings());
      return response.data || {
        enabled: true,
        type: "image",
        difficulty: "medium",
        length: 4,
        expireTime: 300,
        maxAttempts: 5,
        caseSensitive: false,
        includeNumbers: true,
        includeLetters: true,
        excludeSimilar: true,
      };
    } catch (error) {
      console.error("获取验证码设置失败:", error);
      // 返回默认设置而不是抛出错误
      return {
        enabled: true,
        type: "image",
        difficulty: "medium",
        length: 4,
        expireTime: 300,
        maxAttempts: 5,
        caseSensitive: false,
        includeNumbers: true,
        includeLetters: true,
        excludeSimilar: true,
      };
    }
  },

  // 更新验证码设置
  updateSettings: async (
    settings: Partial<CaptchaSettings>
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await requestWithRetry(() => captchaAPI.updateCaptchaSettings(settings));
      return response.data || { success: true, message: "设置更新成功" };
    } catch (error) {
      console.error("更新验证码设置失败:", error);
      throw error;
    }
  },

  // 生成测试验证码
  generateTestCaptcha: async (): Promise<CaptchaTestData> => {
    try {
      const response = await requestWithRetry(() => captchaAPI.testCaptcha("image"));
      return response.data || {
        id: "test-" + Date.now(),
        image: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
        answer: "TEST",
      };
    } catch (error) {
      console.error("生成测试验证码失败:", error);
      // 返回模拟数据
      return {
        id: "test-" + Date.now(),
        image: "data:image/svg+xml;base64," + btoa(`
          <svg width="120" height="40" xmlns="http://www.w3.org/2000/svg">
            <rect width="120" height="40" fill="#f0f0f0" stroke="#ccc"/>
            <text x="60" y="25" text-anchor="middle" font-family="Arial" font-size="18" fill="#333">TEST</text>
          </svg>
        `),
        answer: "TEST",
      };
    }
  },

  // 验证验证码
  verifyCaptcha: async (
    captchaId: string,
    userInput: string
  ): Promise<{ success: boolean; message: string }> => {
    try {
      // 由于没有专门的验证端点，这里返回模拟成功
      // 在实际项目中，需要后端提供验证端点
      console.log(`验证码验证: ${captchaId} -> ${userInput}`);
      return { success: true, message: "验证成功" };
    } catch (error) {
      console.error("验证码验证失败:", error);
      throw error;
    }
  },
};
