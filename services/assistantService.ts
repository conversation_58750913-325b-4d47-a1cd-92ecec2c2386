import type {
  Assistant,
  Assistant<PERSON><PERSON><PERSON><PERSON>,
  CreateAssistantRequest,
  UpdateAssistantRequest,
  AssistantQueryParams,
  AssistantListResponse,
  AssistantResponse,
  CategoriesResponse,
  ApiResponse,
} from "../types/assistant";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || "http://127.0.0.1:8081/api";

// 通用请求函数
const request = async <T = any>(
  url: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  const token = localStorage.getItem("token");
  
  const defaultOptions: RequestInit = {
    headers: {
      "Content-Type": "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  const response = await fetch(`${API_BASE_URL}${url}`, {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return response.json();
};

export const assistantAPI = {
  // 获取助手列表
  getAssistants: async (params?: AssistantQueryParams): Promise<AssistantListResponse> => {
    const searchParams = new URLSearchParams();
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
    }

    const queryString = searchParams.toString();
    const url = `/assistants${queryString ? `?${queryString}` : ""}`;
    
    return request<AssistantListResponse["data"]>(url);
  },

  // 获取单个助手详情
  getAssistant: async (id: string): Promise<AssistantResponse> => {
    return request<Assistant>(`/assistants/${id}`);
  },

  // 创建助手
  createAssistant: async (data: CreateAssistantRequest): Promise<AssistantResponse> => {
    return request<Assistant>("/assistants", {
      method: "POST",
      body: JSON.stringify(data),
    });
  },

  // 更新助手
  updateAssistant: async (
    id: string,
    data: UpdateAssistantRequest
  ): Promise<AssistantResponse> => {
    return request<Assistant>(`/assistants/${id}`, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  },

  // 删除助手
  deleteAssistant: async (id: string): Promise<ApiResponse> => {
    return request(`/assistants/${id}`, {
      method: "DELETE",
    });
  },

  // 复制助手
  duplicateAssistant: async (id: string): Promise<AssistantResponse> => {
    return request<Assistant>(`/assistants/${id}/duplicate`, {
      method: "POST",
    });
  },

  // 获取助手分类
  getCategories: async (): Promise<CategoriesResponse> => {
    return request<AssistantCategory[]>("/assistants/categories");
  },

  // 更新助手分类
  updateCategories: async (categories: AssistantCategory[]): Promise<CategoriesResponse> => {
    return request<AssistantCategory[]>("/assistants/categories", {
      method: "PUT",
      body: JSON.stringify({ categories }),
    });
  },

  // 绑定知识库
  bindKnowledgeBases: async (
    assistantId: string,
    knowledgeBaseIds: string[]
  ): Promise<ApiResponse> => {
    return request(`/assistants/${assistantId}/knowledge-bases`, {
      method: "POST",
      body: JSON.stringify({ knowledge_base_ids: knowledgeBaseIds }),
    });
  },

  // 解绑知识库
  unbindKnowledgeBases: async (
    assistantId: string,
    knowledgeBaseIds: string[]
  ): Promise<ApiResponse> => {
    return request(`/assistants/${assistantId}/knowledge-bases`, {
      method: "DELETE",
      body: JSON.stringify({ knowledge_base_ids: knowledgeBaseIds }),
    });
  },

  // 获取助手使用统计
  getAssistantStats: async (id: string): Promise<ApiResponse> => {
    return request(`/assistants/${id}/stats`);
  },

  // 评价助手
  rateAssistant: async (
    id: string,
    rating: number,
    comment?: string
  ): Promise<ApiResponse> => {
    return request(`/assistants/${id}/rate`, {
      method: "POST",
      body: JSON.stringify({ rating, comment }),
    });
  },
};

export const modelAPI = {
  // 获取可用模型列表
  getModels: async (): Promise<ApiResponse> => {
    return request("/models");
  },

  // 获取模型详情
  getModel: async (id: string): Promise<ApiResponse> => {
    return request(`/models/${id}`);
  },
};

export const knowledgeBaseAPI = {
  // 获取知识库列表
  getKnowledgeBases: async (): Promise<ApiResponse> => {
    return request("/knowledge-bases");
  },

  // 获取知识库详情
  getKnowledgeBase: async (id: string): Promise<ApiResponse> => {
    return request(`/knowledge-bases/${id}`);
  },
};

// 通用API响应处理函数
export const handleApiResponse = (response: ApiResponse): boolean => {
  return response.success;
};

export const handleApiError = (error: any, defaultMessage: string): void => {
  console.error("API Error:", error);
  
  let errorMessage = defaultMessage;
  
  if (error?.response?.data?.message) {
    errorMessage = error.response.data.message;
  } else if (error?.message) {
    errorMessage = error.message;
  }
  
  // 这里可以集成消息提示组件
  // message.error(errorMessage);
  throw new Error(errorMessage);
};
