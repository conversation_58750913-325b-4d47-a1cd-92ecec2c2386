/**
 * 标题生成服务
 * 处理标题生成相关的API调用
 */

import { api } from './api';

// 标题生成设置数据类型
export interface TitleGenerationFormData {
  enabled: boolean;
  titleGenerationModelId: string;
  titlePromptTemplate: string;
  temperature: number;
  maxTokens: number;
}

// 标题生成测试请求
export interface TitleGenerationTestRequest {
  content: string;
  modelId: string;
  promptTemplate: string;
  temperature: number;
  maxTokens: number;
}

// 标题生成测试响应
export interface TitleGenerationTestResponse {
  title: string;
  success: boolean;
  message?: string;
}

// 标题生成设置服务
class TitleGenerationService {
  /**
   * 获取标题生成设置
   */
  async getTitleGenerationSettings(): Promise<TitleGenerationFormData> {
    try {
      const response = await api.get('/api/settings/title-generation');
      
      // 如果API返回数据，使用API数据
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      // 否则返回默认设置
      return this.getDefaultSettings();
    } catch (error) {
      console.warn('Failed to load title generation settings from API, using defaults:', error);
      return this.getDefaultSettings();
    }
  }

  /**
   * 更新标题生成设置
   */
  async updateTitleGenerationSettings(settings: TitleGenerationFormData): Promise<void> {
    try {
      const response = await api.put('/api/settings/title-generation', settings);
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || '保存标题生成设置失败');
      }
    } catch (error: any) {
      console.error('Failed to update title generation settings:', error);
      throw new Error(error.response?.data?.message || error.message || '保存标题生成设置失败');
    }
  }

  /**
   * 测试标题生成
   */
  async testTitleGeneration(request: TitleGenerationTestRequest): Promise<TitleGenerationTestResponse> {
    try {
      const response = await api.post('/api/title-generation/test', request);
      
      if (response.data?.success) {
        return {
          title: response.data.data?.title || '生成失败',
          success: true
        };
      } else {
        return {
          title: '',
          success: false,
          message: response.data?.message || '标题生成失败'
        };
      }
    } catch (error: any) {
      console.error('Failed to test title generation:', error);
      return {
        title: '',
        success: false,
        message: error.response?.data?.message || error.message || '标题生成测试失败'
      };
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings(): TitleGenerationFormData {
    return {
      enabled: true,
      titleGenerationModelId: '',
      titlePromptTemplate: '你是一个专业的对话标题生成器。请根据以下对话内容生成一个简洁的标题。\n\n要求：\n1. 标题长度：3-5个中文词汇\n2. 准确概括：抓住对话的核心主题\n3. 简洁明了：避免冗长和复杂表述\n4. 中文输出：使用简体中文\n5. 无标点：不包含任何标点符号\n\n对话内容：\n用户：{user_message}\n助手：{ai_response}\n\n请直接输出标题，不要包含任何解释或额外内容：',
      temperature: 0.3,
      maxTokens: 50,
    };
  }
}

// 创建并导出服务实例
const titleGenerationService = new TitleGenerationService();
export default titleGenerationService;
