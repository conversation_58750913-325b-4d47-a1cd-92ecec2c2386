/**
 * FAQ 服务
 * 处理 FAQ 相关的 API 调用
 */

import { api } from './api';
import type {
  FAQSuggestion,
  CreateFAQSuggestionRequest,
  UpdateFAQSuggestionRequest,
  FAQQueryParams,
  FAQPaginatedResponse,
  FAQStats,
} from '../types/faq';

// FAQ 服务类
class FAQService {
  /**
   * 获取所有 FAQ 建议
   */
  async getAllSuggestions(params?: FAQQueryParams): Promise<FAQPaginatedResponse> {
    try {
      const response = await api.get('/api/faq/suggestions', { params });
      
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      // 返回默认数据
      return this.getDefaultSuggestions();
    } catch (error) {
      console.warn('Failed to load FAQ suggestions from API, using defaults:', error);
      return this.getDefaultSuggestions();
    }
  }

  /**
   * 根据 ID 获取 FAQ 建议
   */
  async getSuggestionById(id: string): Promise<FAQSuggestion | null> {
    try {
      const response = await api.get(`/api/faq/suggestions/${id}`);
      
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get FAQ suggestion by ID:', error);
      return null;
    }
  }

  /**
   * 创建 FAQ 建议
   */
  async createSuggestion(data: CreateFAQSuggestionRequest): Promise<FAQSuggestion> {
    try {
      const response = await api.post('/api/faq/suggestions', data);
      
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      throw new Error(response.data?.message || '创建 FAQ 建议失败');
    } catch (error: any) {
      console.error('Failed to create FAQ suggestion:', error);
      throw new Error(error.response?.data?.message || error.message || '创建 FAQ 建议失败');
    }
  }

  /**
   * 更新 FAQ 建议
   */
  async updateSuggestion(id: string, data: Partial<CreateFAQSuggestionRequest>): Promise<FAQSuggestion> {
    try {
      const response = await api.put(`/api/faq/suggestions/${id}`, data);
      
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      throw new Error(response.data?.message || '更新 FAQ 建议失败');
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion:', error);
      throw new Error(error.response?.data?.message || error.message || '更新 FAQ 建议失败');
    }
  }

  /**
   * 删除 FAQ 建议
   */
  async deleteSuggestion(id: string): Promise<void> {
    try {
      const response = await api.delete(`/api/faq/suggestions/${id}`);
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || '删除 FAQ 建议失败');
      }
    } catch (error: any) {
      console.error('Failed to delete FAQ suggestion:', error);
      throw new Error(error.response?.data?.message || error.message || '删除 FAQ 建议失败');
    }
  }

  /**
   * 批量删除 FAQ 建议
   */
  async batchDeleteSuggestions(ids: string[]): Promise<void> {
    try {
      const response = await api.post('/api/faq/suggestions/batch-delete', { ids });
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || '批量删除 FAQ 建议失败');
      }
    } catch (error: any) {
      console.error('Failed to batch delete FAQ suggestions:', error);
      throw new Error(error.response?.data?.message || error.message || '批量删除 FAQ 建议失败');
    }
  }

  /**
   * 更新 FAQ 建议状态
   */
  async updateSuggestionStatus(id: string, status: 'active' | 'inactive' | 'draft'): Promise<void> {
    try {
      const response = await api.patch(`/api/faq/suggestions/${id}/status`, { status });
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || '更新 FAQ 建议状态失败');
      }
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion status:', error);
      throw new Error(error.response?.data?.message || error.message || '更新 FAQ 建议状态失败');
    }
  }

  /**
   * 更新 FAQ 建议排序
   */
  async updateSuggestionOrder(id: string, order: number): Promise<void> {
    try {
      const response = await api.patch(`/api/faq/suggestions/${id}/order`, { order });
      
      if (!response.data?.success) {
        throw new Error(response.data?.message || '更新 FAQ 建议排序失败');
      }
    } catch (error: any) {
      console.error('Failed to update FAQ suggestion order:', error);
      throw new Error(error.response?.data?.message || error.message || '更新 FAQ 建议排序失败');
    }
  }

  /**
   * 获取 FAQ 统计信息
   */
  async getStats(): Promise<FAQStats> {
    try {
      const response = await api.get('/api/faq/stats');
      
      if (response.data?.success && response.data?.data) {
        return response.data.data;
      }
      
      return this.getDefaultStats();
    } catch (error) {
      console.warn('Failed to load FAQ stats from API, using defaults:', error);
      return this.getDefaultStats();
    }
  }

  /**
   * 获取默认 FAQ 建议数据
   */
  private getDefaultSuggestions(): FAQPaginatedResponse {
    const defaultSuggestions: FAQSuggestion[] = [
      {
        id: '1',
        title: '如何重置密码？',
        content: '您可以在登录页面点击"忘记密码"链接，然后按照提示操作重置密码。',
        category: 'general',
        status: 'active',
        icon: 'QuestionCircleOutlined',
        order: 1,
        tags: ['密码', '重置', '登录'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        title: '如何联系技术支持？',
        content: '您可以通过邮件 <EMAIL> 或在线客服联系我们的技术支持团队。',
        category: 'support',
        status: 'active',
        icon: 'CustomerServiceOutlined',
        order: 2,
        tags: ['支持', '联系', '客服'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    return {
      data: defaultSuggestions,
      total: defaultSuggestions.length,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    };
  }

  /**
   * 获取默认统计信息
   */
  private getDefaultStats(): FAQStats {
    return {
      total: 2,
      active: 2,
      inactive: 0,
      draft: 0,
      byCategory: {
        general: 1,
        technical: 0,
        billing: 0,
        support: 1,
        product: 0,
        other: 0,
      },
    };
  }
}

// 创建并导出服务实例
export const faqService = new FAQService();
