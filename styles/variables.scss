// V2 Admin 样式变量
// 独立项目版本 - 无全局样式冲突

// ===== 颜色系统 =====
// 主色调
$primary-color: #1890ff;
$primary-color-hover: #40a9ff;
$primary-color-light: #e6f7ff;

// 辅助色
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$info-color: #1890ff;

// 中性色
$text-color: #262626;
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-tertiary: #8c8c8c;
$text-color-disabled: #bfbfbf;

// 背景色
$background-color-base: #f5f5f5;
$background-color-light: #fafafa;
$background-color-white: #ffffff;

// 边框色
$border-color-base: #d9d9d9;
$border-color-dark: #bfbfbf;
$border-color-light: #e8e8e8;
$border-color-split: #f0f0f0;

// ===== 布局尺寸 =====
// 顶部栏
$header-height: 48px;
$header-height-mobile: 44px;

// 内容区域
$content-padding: 24px;
$content-padding-mobile: 16px;

// 菜单系统
$menu-item-height: 46px;
$menu-icon-size: 18px;
$menu-item-margin-block: 0;
$menu-item-margin-inline: 0;

// ===== 响应式断点 =====
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 1024px;
$breakpoint-lg: 1440px;
$breakpoint-xl: 1920px;
$breakpoint-xxl: 2560px;

// ===== 字体系统 =====
// 字体大小层次
$font-size-xs: 11px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-xxl: 20px;

// 字体权重层次
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高系统
$line-height-tight: 1.25;
$line-height-base: 1.5;

// ===== 间距系统 =====
// 统一间距标准：所有设备使用相同的16px间距
$spacing-md: 16px;  // 统一间距标准

// ===== 圆角系统 =====
$border-radius-xs: 2px;
$border-radius-sm: 4px;
$border-radius-base: 8px;
//6px
$border-radius-md: 6px;
$border-radius-lg: 8px;

// ===== 阴影系统 =====
$box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$box-shadow-large: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
$box-shadow-hover: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);

// ===== 动画系统 =====
$transition-base: all 0.2s ease;

// ===== 按钮系统 =====
$button-font-size-base: $font-size-base;
$button-border-radius: $border-radius-sm;

// ===== 暗色主题变量 =====
$dark-bg-color-container: #1f1f1f;
$dark-bg-color-light: #262626;
$dark-border-color-base: #434343;
$dark-border-color-light: #303030;
$dark-border-color-split: #303030;
$dark-component-background: #1f1f1f;
$dark-text-color: rgba(255, 255, 255, 0.85);
$dark-text-color-secondary: rgba(255, 255, 255, 0.65);
$dark-text-color-tertiary: rgba(255, 255, 255, 0.45);

// ===== Z-index 层级 =====
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;

// ===== 组件特定变量 =====
// 侧边栏
$sidebar-bg-light: $background-color-white;
$sidebar-bg-dark: #001529;
$sidebar-text-color-dark: rgba(255, 255, 255, 0.85);

// 顶部栏
$header-bg-light: $background-color-white;
$header-bg-dark: #1f1f1f;

// 内容区域
$content-bg: $background-color-white;
$content-bg-dark: #141414;

// ===== 工具类 =====
$item-hover-bg: $background-color-light;

// ===== Mixins =====
@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
