// ===== 主题切换动画样式 =====

// 禁用过渡效果 - 避免与 View Transition 冲突
html[disabled-transition] *,
html[disabled-transition] *::before,
html[disabled-transition] *::after {
  transition: none !important;
  animation: none !important;
}

// View Transition 动画样式
::view-transition-old(root),
::view-transition-new(root) {
  animation: none;
  mix-blend-mode: normal;
}

// 暗色模式下的层级控制
.dark::view-transition-old(root) {
  z-index: 1;
}

.dark::view-transition-new(root) {
  z-index: 9999;
}

// 亮色模式下的层级控制
:not(.dark)::view-transition-old(root) {
  z-index: 9999;
}

:not(.dark)::view-transition-new(root) {
  z-index: 1;
}

// 主题切换按钮动画
.theme-switcher-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: 1px solid var(--theme-border-color, #d9d9d9);
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, rgba(0, 0, 0, 0.88));
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--theme-fill-quaternary, rgba(0, 0, 0, 0.02));
    border-color: var(--theme-primary, #2464F1);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  // 图标动画
  .icon {
    transition: transform 0.3s ease;
    
    &.rotating {
      transform: rotate(180deg);
    }
  }
  
  // 深色模式下的样式
  .dark & {
    background: var(--theme-bg-primary, #141414);
    border-color: var(--theme-border-color, #424242);
    color: var(--theme-text-primary, rgba(255, 255, 255, 0.85));
    
    &:hover {
      background: var(--theme-fill-quaternary, rgba(255, 255, 255, 0.04));
      border-color: var(--theme-primary, #2464F1);
    }
  }
}

// 主题切换工具提示
.theme-tooltip {
  .ant-tooltip-inner {
    font-size: 12px;
  }
}

// 确保主题变量平滑过渡
:root {
  color-scheme: light;
  transition: color-scheme 0.3s ease;
}

.dark {
  color-scheme: dark;
}

// 主题切换时的背景过渡
body {
  background-color: var(--theme-bg-tertiary, #f5f5f5);
  color: var(--theme-text-primary, rgba(0, 0, 0, 0.88));
  transition: background-color 0.3s ease, color 0.3s ease;
}

// 为不支持 View Transition 的浏览器提供降级动画
@media (prefers-reduced-motion: no-preference) {
  html:not([disabled-transition]) {
    * {
      transition: 
        background-color 0.3s ease,
        border-color 0.3s ease,
        color 0.3s ease,
        box-shadow 0.3s ease;
    }
  }
}

// 用户禁用动画时的样式
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  ::view-transition-old(root),
  ::view-transition-new(root) {
    animation: none !important;
  }
}

// 主题切换加载状态
.theme-switching {
  pointer-events: none;
  
  .theme-switcher-button {
    opacity: 0.6;
    cursor: not-allowed;
    
    .icon {
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 确保所有组件都能正确响应主题变化
.ant-layout,
.ant-menu,
.ant-table,
.ant-card,
.ant-modal,
.ant-drawer,
.ant-dropdown,
.ant-select-dropdown,
.ant-tooltip,
.ant-popover {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

// 特殊处理一些组件的主题切换
.ant-table-thead > tr > th {
  transition: background-color 0.3s ease;
}

.ant-menu-item,
.ant-menu-submenu-title {
  transition: background-color 0.3s ease, color 0.3s ease;
}

// 确保滚动条在主题切换时也能正确显示
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--theme-bg-secondary, rgba(0, 0, 0, 0.06));
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--theme-text-tertiary, rgba(0, 0, 0, 0.45));
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-secondary, rgba(0, 0, 0, 0.65));
}

.dark {
  ::-webkit-scrollbar-track {
    background: var(--theme-bg-secondary, rgba(255, 255, 255, 0.08));
  }

  ::-webkit-scrollbar-thumb {
    background: var(--theme-text-tertiary, rgba(255, 255, 255, 0.45));
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--theme-text-secondary, rgba(255, 255, 255, 0.65));
  }
}