import React, { useCallback, useState } from 'react';
import { Button, Tooltip } from 'antd';
import { 
  SunOutlined, 
  MoonOutlined, 
  DesktopOutlined,
  LoadingOutlined 
} from '@ant-design/icons';
import { useTheme, useThemeActions, type ThemeMode } from '../../stores/settings/themeSlice';
import './style.scss';

// 检测浏览器是否支持 View Transition API 和用户动画偏好
function enableTransitions(): boolean {
  return (
    typeof document !== 'undefined' &&
    'startViewTransition' in document &&
    window.matchMedia('(prefers-reduced-motion: no-preference)').matches
  );
}

// 主题切换组件属性
interface ThemeSwitcherProps {
  /**
   * 显示模式
   * - 'icon' - 只显示图标
   * - 'text' - 只显示文字  
   * - 'both' - 显示图标和文字
   */
  mode?: 'icon' | 'text' | 'both';
  
  /**
   * 按钮大小
   */
  size?: 'small' | 'middle' | 'large';
  
  /**
   * 是否显示边框
   */
  bordered?: boolean;
  
  /**
   * 是否显示工具提示
   */
  showTooltip?: boolean;
  
  /**
   * 自定义类名
   */
  className?: string;
  
  /**
   * 主题切换完成回调
   */
  onThemeChange?: (theme: 'light' | 'dark') => void;
}

const ThemeSwitcher: React.FC<ThemeSwitcherProps> = ({
  mode = 'icon',
  size = 'middle',
  bordered = true,
  showTooltip = true,
  className = '',
  onThemeChange,
}) => {
  const { mode: themeMode, actualTheme, isDark } = useTheme();
  const { setMode, toggleTheme } = useThemeActions();
  const [isAnimating, setIsAnimating] = useState(false);

  // 获取主题图标
  const getThemeIcon = useCallback((theme: ThemeMode) => {
    switch (theme) {
      case 'light':
        return <SunOutlined />;
      case 'dark':
        return <MoonOutlined />;
      case 'auto':
        return <DesktopOutlined />;
      default:
        return <SunOutlined />;
    }
  }, []);

  // 获取主题文字
  const getThemeText = useCallback((theme: ThemeMode) => {
    switch (theme) {
      case 'light':
        return '亮色模式';
      case 'dark':
        return '暗色模式';
      case 'auto':
        return '跟随系统';
      default:
        return '亮色模式';
    }
  }, []);

  // 主题切换处理函数
  const handleToggle = useCallback(async (e: React.MouseEvent) => {
    // 防止重复点击
    if (isAnimating) return;
    
    setIsAnimating(true);

    try {
      // 兼容性检测
      if (!enableTransitions()) {
        // 不支持动画时，直接切换主题
        toggleTheme();
        onThemeChange?.(isDark ? 'light' : 'dark');
        return;
      }

      // 获取点击坐标
      const { clientX, clientY } = e;
      const html = document.documentElement;

      if (html) {
        // 禁用CSS过渡，避免与View Transition冲突
        html.setAttribute('disabled-transition', '');

        // 计算圆形裁剪路径 - 从点击位置扩散到整个屏幕
        const maxRadius = Math.hypot(
          Math.max(clientX, window.innerWidth - clientX),
          Math.max(clientY, window.innerHeight - clientY)
        );

        const clipPath = [
          `circle(0px at ${clientX}px ${clientY}px)`,
          `circle(${maxRadius}px at ${clientX}px ${clientY}px)`
        ];

        // 启动 View Transition
        await document.startViewTransition(async () => {
          // 在这里执行主题切换
          toggleTheme();
        }).ready;

        // 创建自定义动画
        const animation = document.documentElement.animate(
          { 
            clipPath: isDark ? clipPath.reverse() : clipPath 
          },
          {
            duration: 500,
            easing: 'ease-in',
            pseudoElement: `::view-transition-${isDark ? 'old' : 'new'}(root)`
          }
        );

        // 动画完成后的清理工作
        animation.finished.then(() => {
          html.removeAttribute('disabled-transition');
          setIsAnimating(false);
          onThemeChange?.(isDark ? 'light' : 'dark');
        });
      } else {
        setIsAnimating(false);
      }
    } catch (error) {
      console.warn('Theme transition animation failed:', error);
      // 动画失败时，确保主题仍然能够切换
      toggleTheme();
      setIsAnimating(false);
      onThemeChange?.(isDark ? 'light' : 'dark');
    }
  }, [isDark, toggleTheme, onThemeChange, isAnimating]);

  // 渲染按钮内容
  const renderButtonContent = () => {
    if (isAnimating) {
      return <LoadingOutlined spin />;
    }

    switch (mode) {
      case 'icon':
        return getThemeIcon(themeMode);
      case 'text':
        return getThemeText(themeMode);
      case 'both':
        return (
          <>
            {getThemeIcon(themeMode)}
            <span style={{ marginLeft: 8 }}>{getThemeText(themeMode)}</span>
          </>
        );
      default:
        return getThemeIcon(themeMode);
    }
  };

  // 渲染工具提示内容
  const renderTooltipTitle = () => {
    if (isAnimating) {
      return '主题切换中...';
    }
    
    const nextTheme = isDark ? '亮色模式' : '暗色模式';
    return `切换到${nextTheme}`;
  };

  const button = (
    <Button
      type="text"
      size={size}
      className={`theme-switcher-button ${className} ${isAnimating ? 'animating' : ''}`}
      style={{
        border: bordered ? undefined : 'none',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onClick={handleToggle}
      disabled={isAnimating}
      aria-label={`切换主题，当前为${getThemeText(themeMode)}`}
    >
      <span className={`icon ${isAnimating ? 'rotating' : ''}`}>
        {renderButtonContent()}
      </span>
    </Button>
  );

  // 是否显示工具提示
  if (showTooltip && mode === 'icon') {
    return (
      <Tooltip 
        title={renderTooltipTitle()}
        placement="bottom"
        className="theme-tooltip"
      >
        {button}
      </Tooltip>
    );
  }

  return button;
};

// 高级主题选择器组件 - 支持三种模式
interface ThemeSelecterProps {
  className?: string;
  onThemeChange?: (theme: ThemeMode) => void;
}

export const ThemeSelector: React.FC<ThemeSelecterProps> = ({
  className = '',
  onThemeChange,
}) => {
  const { mode: themeMode } = useTheme();
  const { setMode } = useThemeActions();
  const [isAnimating, setIsAnimating] = useState(false);

  const handleModeChange = useCallback(async (newMode: ThemeMode, e: React.MouseEvent) => {
    if (isAnimating || newMode === themeMode) return;
    
    setIsAnimating(true);

    try {
      if (enableTransitions()) {
        const { clientX, clientY } = e;
        const html = document.documentElement;

        if (html) {
          html.setAttribute('disabled-transition', '');

          const maxRadius = Math.hypot(
            Math.max(clientX, window.innerWidth - clientX),
            Math.max(clientY, window.innerHeight - clientY)
          );

          const clipPath = [
            `circle(0px at ${clientX}px ${clientY}px)`,
            `circle(${maxRadius}px at ${clientX}px ${clientY}px)`
          ];

          await document.startViewTransition(async () => {
            setMode(newMode);
          }).ready;

          const animation = document.documentElement.animate(
            { clipPath },
            {
              duration: 500,
              easing: 'ease-in',
              pseudoElement: '::view-transition-new(root)'
            }
          );

          animation.finished.then(() => {
            html.removeAttribute('disabled-transition');
            setIsAnimating(false);
            onThemeChange?.(newMode);
          });
        }
      } else {
        setMode(newMode);
        setIsAnimating(false);
        onThemeChange?.(newMode);
      }
    } catch (error) {
      console.warn('Theme transition animation failed:', error);
      setMode(newMode);
      setIsAnimating(false);
      onThemeChange?.(newMode);
    }
  }, [themeMode, setMode, onThemeChange, isAnimating]);

  const themes: { mode: ThemeMode; icon: React.ReactNode; label: string }[] = [
    { mode: 'light', icon: <SunOutlined />, label: '亮色' },
    { mode: 'dark', icon: <MoonOutlined />, label: '暗色' },
    { mode: 'auto', icon: <DesktopOutlined />, label: '自动' },
  ];

  return (
    <div className={`theme-selector ${className}`}>
      {themes.map((theme) => (
        <Button
          key={theme.mode}
          type={themeMode === theme.mode ? 'primary' : 'text'}
          size="small"
          className="theme-option"
          onClick={(e) => handleModeChange(theme.mode, e)}
          disabled={isAnimating}
          icon={isAnimating ? <LoadingOutlined spin /> : theme.icon}
        >
          {theme.label}
        </Button>
      ))}
    </div>
  );
};

export default ThemeSwitcher;