@use '../../styles/variables' as *;

// 主题切换按钮样式
.theme-switcher-button {
  position: relative;
  border-radius: 8px !important;
  transition: all 0.2s ease;
  
  // 图标容器
  .icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
    
    // 旋转动画
    &.rotating {
      animation: theme-rotate 0.6s ease-in-out;
    }
  }
  
  // 悬停效果
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  // 点击效果
  &:active:not(:disabled) {
    transform: translateY(0) scale(0.98);
  }
  
  // 禁用状态
  &:disabled,
  &.animating {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  // 深色模式适配
  .dark & {
    &:hover:not(:disabled) {
      box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
    }
  }
}

// 旋转动画
@keyframes theme-rotate {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

// 主题选择器样式
.theme-selector {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background: var(--theme-bg-primary, #ffffff);
  border: 1px solid var(--theme-border-color, #d9d9d9);
  border-radius: 8px;
  transition: all 0.2s ease;
  
  .theme-option {
    min-width: 60px;
    border-radius: 6px !important;
    transition: all 0.2s ease;
    
    &:hover {
      background: var(--theme-fill-quaternary, rgba(0, 0, 0, 0.02));
    }
    
    // 选中状态
    &.ant-btn-primary {
      box-shadow: 0 1px 4px rgba(36, 100, 241, 0.3);
    }
  }
  
  // 深色模式适配
  .dark & {
    background: var(--theme-bg-primary, #141414);
    border-color: var(--theme-border-color, #424242);
    
    .theme-option {
      &:hover {
        background: var(--theme-fill-quaternary, rgba(255, 255, 255, 0.04));
      }
    }
  }
}

// 工具提示样式
.theme-tooltip {
  .ant-tooltip-inner {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
  }
  
  .ant-tooltip-arrow::before {
    background: var(--theme-bg-elevated, #ffffff);
  }
  
  // 深色模式适配
  .dark & {
    .ant-tooltip-inner {
      background: var(--theme-bg-elevated, #1f1f1f);
      color: var(--theme-text-primary, rgba(255, 255, 255, 0.85));
    }
    
    .ant-tooltip-arrow::before {
      background: var(--theme-bg-elevated, #1f1f1f);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .theme-switcher-button {
    // 在移动端稍微调整按钮大小
    &.ant-btn-small {
      width: 32px;
      height: 32px;
    }
    
    &.ant-btn-middle {
      width: 36px;
      height: 36px;
    }
    
    &.ant-btn-large {
      width: 40px;
      height: 40px;
    }
  }
  
  .theme-selector {
    // 移动端选择器更紧凑
    gap: 2px;
    
    .theme-option {
      min-width: 50px;
      font-size: 12px;
    }
  }
}

// 主题切换时的全局状态指示
.theme-switching {
  .theme-switcher-button {
    .icon {
      animation: theme-pulse 1.5s ease-in-out infinite;
    }
  }
}

@keyframes theme-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// 无障碍支持
.theme-switcher-button:focus-visible {
  outline: 2px solid var(--theme-primary, #2464F1);
  outline-offset: 2px;
}

// 用户偏好：减少动画
@media (prefers-reduced-motion: reduce) {
  .theme-switcher-button,
  .theme-option,
  .icon {
    transition: none !important;
    animation: none !important;
  }
  
  .icon.rotating {
    animation: none !important;
    transform: none !important;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  .theme-switcher-button {
    border-width: 2px;
    
    &:hover {
      border-color: var(--theme-primary, #2464F1);
    }
  }
  
  .theme-selector {
    border-width: 2px;
  }
}