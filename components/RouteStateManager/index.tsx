import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { RouteConfig, RouteMeta } from '../../types';
import { getRouteMeta, isValidRoute } from '../../utils/routeUtils';
import routeConfig from '../../config/routes';

interface RouteState {
  currentPath: string;
  currentMeta: RouteMeta | null;
  isValidPath: boolean;
  previousPath: string | null;
}

interface RouteStateContextValue extends RouteState {
  updateDocumentTitle: (title?: string) => void;
  updateMetaTags: (meta: RouteMeta) => void;
}

const RouteStateContext = createContext<RouteStateContextValue | null>(null);

interface RouteStateManagerProps {
  children: React.ReactNode;
  routes?: RouteConfig[];
  titleTemplate?: string;
  defaultTitle?: string;
}

/**
 * 路由状态管理组件
 * 管理路由状态、文档标题、meta标签等
 */
const RouteStateManager: React.FC<RouteStateManagerProps> = ({
  children,
  routes = routeConfig,
  titleTemplate = '%s - V2 Admin',
  defaultTitle = 'V2 Admin',
}) => {
  const location = useLocation();

  const [routeState, setRouteState] = useState<RouteState>({
    currentPath: location.pathname,
    currentMeta: null,
    isValidPath: false,
    previousPath: null,
  });

  /**
   * 更新文档标题
   */
  const updateDocumentTitle = (title?: string) => {
    const finalTitle = title || routeState.currentMeta?.title || defaultTitle;
    
    if (finalTitle === defaultTitle) {
      document.title = defaultTitle;
    } else {
      document.title = titleTemplate.replace('%s', finalTitle);
    }
  };

  /**
   * 更新meta标签
   */
  const updateMetaTags = (meta: RouteMeta) => {
    // 更新description
    if (meta.description) {
      let descriptionMeta = document.querySelector('meta[name="description"]');
      if (!descriptionMeta) {
        descriptionMeta = document.createElement('meta');
        descriptionMeta.setAttribute('name', 'description');
        document.head.appendChild(descriptionMeta);
      }
      descriptionMeta.setAttribute('content', meta.description);
    }

    // 更新keywords
    if (meta.keywords && meta.keywords.length > 0) {
      let keywordsMeta = document.querySelector('meta[name="keywords"]');
      if (!keywordsMeta) {
        keywordsMeta = document.createElement('meta');
        keywordsMeta.setAttribute('name', 'keywords');
        document.head.appendChild(keywordsMeta);
      }
      keywordsMeta.setAttribute('content', meta.keywords.join(', '));
    }
  };

  // 监听路由变化
  useEffect(() => {
    const currentPath = location.pathname;
    const currentMeta = getRouteMeta(routes, currentPath);
    const isValidPath = isValidRoute(routes, currentPath);

    setRouteState(prevState => {
      // 避免不必要的状态更新
      if (prevState.currentPath === currentPath) {
        return prevState;
      }

      return {
        ...prevState,
        previousPath: prevState.currentPath,
        currentPath,
        currentMeta,
        isValidPath,
      };
    });

    // 更新文档标题
    updateDocumentTitle(currentMeta?.title);

    // 更新meta标签
    if (currentMeta) {
      updateMetaTags(currentMeta);
    }

    // 发送页面浏览事件（用于统计分析）
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: currentPath,
        page_title: currentMeta?.title || currentPath,
      });
    }

    // 记录路由变化日志（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('Route changed:', {
        to: currentPath,
        meta: currentMeta,
        isValid: isValidPath,
      });
    }
  }, [location.pathname]); // 移除可能导致循环的依赖

  // 处理页面刷新时的状态恢复
  useEffect(() => {
    const handleBeforeUnload = () => {
      // 保存当前状态到sessionStorage
      const stateToSave = {
        currentPath: routeState.currentPath,
        timestamp: Date.now(),
      };
      
      try {
        sessionStorage.setItem('route_state_backup', JSON.stringify(stateToSave));
      } catch (error) {
        console.warn('Failed to save route state:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [routeState.currentPath]);

  // 页面加载时恢复状态
  useEffect(() => {
    try {
      const savedState = sessionStorage.getItem('route_state_backup');
      if (savedState) {
        const parsed = JSON.parse(savedState);
        // 检查是否是最近的状态（5分钟内）
        if (Date.now() - parsed.timestamp < 5 * 60 * 1000) {
          console.log('Restored route state from session storage');
        }
        // 清理保存的状态
        sessionStorage.removeItem('route_state_backup');
      }
    } catch (error) {
      console.warn('Failed to restore route state:', error);
    }
  }, []);

  const contextValue: RouteStateContextValue = {
    ...routeState,
    updateDocumentTitle,
    updateMetaTags,
  };

  return (
    <RouteStateContext.Provider value={contextValue}>
      {children}
    </RouteStateContext.Provider>
  );
};

/**
 * 使用路由状态的Hook
 */
export const useRouteState = (): RouteStateContextValue => {
  const context = useContext(RouteStateContext);
  if (!context) {
    throw new Error('useRouteState must be used within a RouteStateManager');
  }
  return context;
};

export default RouteStateManager;
