import React, { useEffect, useMemo } from 'react';
import { ConfigProvider, theme, App } from 'antd';
import { useTheme } from '../../stores/settings/themeSlice';
import { getThemeConfig, getCSSVariables } from '../../config/theme';

// 导入主题切换样式
import '../../styles/theme-transition.scss';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { actualTheme, isDark } = useTheme();

  // 获取当前主题配置
  const currentThemeConfig = useMemo(() => {
    return getThemeConfig(isDark);
  }, [isDark]);

  // 更新CSS变量
  useEffect(() => {
    const cssVariables = getCSSVariables(isDark);
    const root = document.documentElement;
    
    // 应用CSS变量到根元素
    Object.entries(cssVariables).forEach(([key, value]) => {
      if (value !== undefined) {
        root.style.setProperty(key, String(value));
      }
    });

    // 更新HTML元素的class和data属性
    root.classList.remove('light', 'dark');
    root.classList.add(actualTheme);
    root.setAttribute('data-theme', actualTheme);
    
    // 更新meta标签中的theme-color（用于移动端浏览器）
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute('content', isDark ? '#141414' : '#ffffff');
    } else {
      // 如果不存在meta标签，创建一个
      const meta = document.createElement('meta');
      meta.name = 'theme-color';
      meta.content = isDark ? '#141414' : '#ffffff';
      document.head.appendChild(meta);
    }

    // 更新CSS媒体查询中的color-scheme
    document.documentElement.style.colorScheme = actualTheme;
  }, [isDark, actualTheme]);

  // 监听系统主题变化
  useEffect(() => {
    // 这个effect在themeSlice中已经处理了，这里不需要重复
  }, []);

  return (
    <ConfigProvider 
      theme={currentThemeConfig}
      componentSize="middle"
    >
      <App
        message={{ maxCount: 3 }}
        notification={{ maxCount: 5, placement: 'topRight' }}
      >
        {children}
      </App>
    </ConfigProvider>
  );
};

export default ThemeProvider;