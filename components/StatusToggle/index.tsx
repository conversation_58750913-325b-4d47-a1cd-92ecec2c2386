/**
 * V2 Admin 状态切换组件
 * 用于用户状态的启用/禁用切换
 */
import React from 'react';
import { Switch } from 'antd';
import type { SwitchProps } from 'antd';

interface StatusToggleProps extends Omit<SwitchProps, 'onChange'> {
  checked: boolean;
  onChange: (checked: boolean) => void;
  size?: 'default' | 'small';
}

/**
 * 状态切换组件
 * 基于Ant Design Switch组件的封装
 */
const StatusToggle: React.FC<StatusToggleProps> = ({
  checked,
  onChange,
  size = 'default',
  ...props
}) => {
  return (
    <Switch
      checked={checked}
      onChange={onChange}
      size={size as 'small' | 'default'}
      checkedChildren="启用"
      unCheckedChildren="禁用"
      {...props}
    />
  );
};

export default StatusToggle;
