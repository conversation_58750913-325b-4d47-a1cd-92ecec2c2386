import React from "react";
import {
  Modal,
  Descriptions,
  Tag,
  Progress,
  Space,
  Statistic,
  Row,
  Col,
  Card,
  Divider,
  Typography,
  Badge,
  Tooltip,
} from "antd";
import {
  DatabaseOutlined,
  FileTextOutlined,
  CloudOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  InfoCircleOutlined,
  TagOutlined,
} from "@ant-design/icons";
import type { KnowledgeBase } from "../../types/knowledgeBase";
import { KNOWLEDGE_BASE_TYPES, EMBEDDING_MODELS } from "../../types/knowledgeBase";
import "./style.scss";

const { Text, Title } = Typography;

interface KnowledgeBaseDetailModalProps {
  visible: boolean;
  knowledgeBase?: KnowledgeBase | null;
  onCancel: () => void;
}

const KnowledgeBaseDetailModal: React.FC<KnowledgeBaseDetailModalProps> = ({
  visible,
  knowledgeBase,
  onCancel,
}) => {
  if (!knowledgeBase) return null;

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    const typeConfig = KNOWLEDGE_BASE_TYPES.find(t => t.key === type);
    return typeConfig ? typeConfig.label : type;
  };

  // 获取嵌入模型标签
  const getEmbeddingModelLabel = (model: string) => {
    const modelConfig = EMBEDDING_MODELS.find(m => m.key === model);
    return modelConfig ? modelConfig.label : model;
  };

  // 获取状态标签和图标
  const getStatusTag = (status: string) => {
    const statusConfig = {
      ready: {
        color: 'green',
        text: '就绪',
        icon: <CheckCircleOutlined />,
        description: '知识库已准备就绪，可以正常使用'
      },
      processing: {
        color: 'blue',
        text: '处理中',
        icon: <LoadingOutlined spin />,
        description: '正在处理文档并生成向量嵌入'
      },
      error: {
        color: 'red',
        text: '错误',
        icon: <ExclamationCircleOutlined />,
        description: '处理过程中出现错误，请检查配置'
      },
      inactive: {
        color: 'default',
        text: '未激活',
        icon: <ClockCircleOutlined />,
        description: '知识库尚未激活或已被禁用'
      }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'default',
      text: status,
      icon: <InfoCircleOutlined />,
      description: '未知状态'
    };
    return (
      <Tooltip title={config.description}>
        <Tag color={config.color} icon={config.icon}>
          {config.text}
        </Tag>
      </Tooltip>
    );
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString(navigator.language);
    } catch (e) {
      console.error('Invalid date format:', dateString);
      return 'Invalid date';
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      title={
        <Space>
          <DatabaseOutlined />
          <span>知识库详情</span>
          <Badge
            status={knowledgeBase.status === 'ready' ? 'success' :
                   knowledgeBase.status === 'processing' ? 'processing' :
                   knowledgeBase.status === 'error' ? 'error' : 'default'}
          />
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={900}
    >
      <div className="knowledge-base-detail">
        {/* 知识库标题和状态 */}
        <div className="mb-4">
          <Title level={4} className="mb-2">
            {knowledgeBase.name}
          </Title>
          <Text type="secondary" className="text-sm">
            {knowledgeBase.description}
          </Text>
        </div>

        <Divider className="my-4" />

        {/* 基础信息 */}
        <Card
          title={
            <Space>
              <InfoCircleOutlined />
              <span>基础信息</span>
            </Space>
          }
          size="small"
          className="mb-4"
        >
          <Descriptions column={2} size="small">
            <Descriptions.Item label="知识库名称">
              <Text strong>{knowledgeBase.name}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="类型">
              <Tag color="blue">{getTypeLabel(knowledgeBase.type)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {getStatusTag(knowledgeBase.status)}
            </Descriptions.Item>
            <Descriptions.Item label="嵌入模型">
              <Tag color="purple">{getEmbeddingModelLabel(knowledgeBase.embedding_model)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建者">
              <Space>
                <UserOutlined />
                <Text>{knowledgeBase.created_by}</Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="所有者ID">
              <Text code copyable>{knowledgeBase.owner_id}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              <Space>
                <ClockCircleOutlined />
                <Text>{formatDate(knowledgeBase.created_at)}</Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              <Space>
                <ClockCircleOutlined />
                <Text>{formatDate(knowledgeBase.updated_at)}</Text>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="知识库ID" span={2}>
              <Text code copyable>{knowledgeBase.id}</Text>
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 统计信息 */}
        <Card
          title={
            <Space>
              <DatabaseOutlined />
              <span>统计信息</span>
            </Space>
          }
          size="small"
          className="mb-4"
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={8}>
              <Card size="small" className="text-center">
                <Statistic
                  title="文档数量"
                  value={knowledgeBase.document_count}
                  prefix={<FileTextOutlined className="text-blue-500" />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card size="small" className="text-center">
                <Statistic
                  title="向量数量"
                  value={knowledgeBase.vector_count}
                  prefix={<DatabaseOutlined className="text-green-500" />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={8}>
              <Card size="small" className="text-center">
                <Statistic
                  title="存储大小"
                  value={formatFileSize(knowledgeBase.total_size)}
                  prefix={<CloudOutlined className="text-purple-500" />}
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
          </Row>
        </Card>

        {/* 处理进度 */}
        {knowledgeBase.status === 'processing' && (
          <Card
            title={
              <Space>
                <LoadingOutlined spin />
                <span>处理进度</span>
              </Space>
            }
            size="small"
            className="mb-4"
          >
            <div className="space-y-3">
              <Progress
                percent={65}
                status="active"
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                showInfo={true}
                format={(percent) => `${percent}%`}
              />
              <div className="flex justify-between text-sm text-gray-600">
                <span>正在处理文档并生成向量嵌入...</span>
                <span>预计剩余时间: 5分钟</span>
              </div>
              <div className="text-xs text-gray-500">
                <div>• 文档解析: 已完成</div>
                <div>• 文本分块: 进行中</div>
                <div>• 向量生成: 等待中</div>
                <div>• 索引构建: 等待中</div>
              </div>
            </div>
          </Card>
        )}

        {/* 标签信息 */}
        {knowledgeBase.tags && knowledgeBase.tags.length > 0 && (
          <Card
            title={
              <Space>
                <TagOutlined />
                <span>标签</span>
              </Space>
            }
            size="small"
            className="mb-4"
          >
            <Space wrap>
              {knowledgeBase.tags.map((tag, index) => (
                <Tag
                  key={index}
                  color="blue"
                  className="px-2 py-1 rounded-md"
                >
                  {tag}
                </Tag>
              ))}
            </Space>
          </Card>
        )}

        {/* 配置信息 */}
        <Card
          title={
            <Space>
              <InfoCircleOutlined />
              <span>配置信息</span>
            </Space>
          }
          size="small"
        >
          <Descriptions column={1} size="small">
            <Descriptions.Item label="嵌入模型">
              <Tag color="purple">{getEmbeddingModelLabel(knowledgeBase.embedding_model)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="模型提供商">
              <Text>
                {EMBEDDING_MODELS.find(m => m.key === knowledgeBase.embedding_model)?.provider || '未知'}
              </Text>
            </Descriptions.Item>
            <Descriptions.Item label="知识库类型">
              <Tag color="blue">{getTypeLabel(knowledgeBase.type)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              <Text>{formatDate(knowledgeBase.created_at)}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="最后更新">
              <Text>{formatDate(knowledgeBase.updated_at)}</Text>
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </div>
    </Modal>
  );
};

export default KnowledgeBaseDetailModal;