/**
 * V2 Admin 路由保护组件
 * 用于保护需要认证的路由，未登录用户将被重定向到登录页面
 */
import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import authUtils from '../../utils/auth';
import jwtUtils from '../../utils/jwt';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean; // 是否需要管理员权限
  requireSuperAdmin?: boolean; // 是否需要超级管理员权限
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = true,
  requireSuperAdmin = false,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // 检查基本认证状态
        const authenticated = authUtils.isAuthenticated();
        
        if (!authenticated) {
          setIsAuthenticated(false);
          setHasPermission(false);
          setIsLoading(false);
          return;
        }

        // 检查token是否过期
        const token = jwtUtils.getToken();
        if (token && jwtUtils.isTokenExpired(token)) {
          console.log('Token已过期，清除认证信息');
          authUtils.clearAuthAndRedirect(false, '', 0);
          setIsAuthenticated(false);
          setHasPermission(false);
          setIsLoading(false);
          return;
        }

        setIsAuthenticated(true);

        // 检查权限
        let permission = true;
        
        if (requireSuperAdmin) {
          permission = authUtils.isSuperAdmin();
        } else if (requireAdmin) {
          permission = authUtils.isAdmin();
        }

        setHasPermission(permission);
      } catch (error) {
        console.error('认证检查失败:', error);
        setIsAuthenticated(false);
        setHasPermission(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [requireAdmin, requireSuperAdmin]);

  // 显示加载状态
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        background: '#f5f5f5'
      }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px', color: '#666' }}>验证身份中...</div>
      </div>
    );
  }

  // 未认证，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 权限不足，显示无权限页面或重定向
  if (!hasPermission) {
    if (requireSuperAdmin) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          background: '#f5f5f5'
        }}>
          <h2>权限不足</h2>
          <p>此功能需要超级管理员权限</p>
        </div>
      );
    } else if (requireAdmin) {
      return (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          background: '#f5f5f5'
        }}>
          <h2>权限不足</h2>
          <p>此功能需要管理员权限</p>
        </div>
      );
    }
  }

  // 认证通过且有权限，渲染子组件
  return <>{children}</>;
};

export default ProtectedRoute;
