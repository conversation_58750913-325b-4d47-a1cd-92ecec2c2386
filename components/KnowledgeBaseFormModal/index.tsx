import React, { useEffect } from "react";
import { Modal, Form, Input, Select, message, Space } from "antd";
import { DatabaseOutlined, InfoCircleOutlined } from "@ant-design/icons";
import { knowledgeBaseAPI } from "../../services/api";
import type {
  KnowledgeBase,
  CreateKnowledgeBaseRequest,
  UpdateKnowledgeBaseRequest,
} from "../../types/knowledgeBase";
import { KNOWLEDGE_BASE_TYPES, EMBEDDING_MODELS } from "../../types/knowledgeBase";

const { Option } = Select;
const { TextArea } = Input;

interface KnowledgeBaseFormModalProps {
  visible: boolean;
  knowledgeBase?: KnowledgeBase | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const KnowledgeBaseFormModal: React.FC<KnowledgeBaseFormModalProps> = ({
  visible,
  knowledgeBase,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const isEdit = !!knowledgeBase;

  useEffect(() => {
    if (visible) {
      if (isEdit && knowledgeBase) {
        form.setFieldsValue({
          name: knowledgeBase.name,
          description: knowledgeBase.description,
          type: knowledgeBase.type,
          embedding_model: knowledgeBase.embedding_model,
          tags: knowledgeBase.tags.join(", "),
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          type: "document",
          embedding_model: "text-embedding-ada-002",
        });
      }
    }
  }, [visible, isEdit, knowledgeBase, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const requestData = {
        name: values.name,
        description: values.description,
        type: values.type,
        embedding_model: values.embedding_model,
        tags: values.tags ? values.tags.split(",").map((tag: string) => tag.trim()).filter(Boolean) : [],
      };

      if (isEdit && knowledgeBase) {
        await knowledgeBaseAPI.updateKnowledgeBase(knowledgeBase.id, requestData as UpdateKnowledgeBaseRequest);
        message.success("Knowledge base updated successfully");
      } else {
        await knowledgeBaseAPI.createKnowledgeBase(requestData as CreateKnowledgeBaseRequest);
        message.success("Knowledge base created successfully");
      }

      onSuccess();
    } catch (error: any) {
      console.error("操作失败:", error);
      if (error.response?.data?.message) {
        message.error(error.response.data.message);
      } else {
        message.error(isEdit ? "更新失败，请稍后重试" : "创建失败，请稍后重试");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <Space>
          <DatabaseOutlined />
          {isEdit ? "编辑知识库" : "创建知识库"}
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
      destroyOnHidden
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          type: "document",
          embedding_model: "text-embedding-ada-002",
        }}
      >
        <Form.Item
          name="name"
          label="知识库名称"
          rules={[
            { required: true, message: "请输入知识库名称" },
            { max: 50, message: "知识库名称不能超过50个字符" },
          ]}
        >
          <Input placeholder="请输入知识库名称" />
        </Form.Item>

        <Form.Item
          name="description"
          label="描述"
          rules={[
            { required: true, message: "请输入知识库描述" },
            { max: 200, message: "描述不能超过200个字符" },
          ]}
        >
          <TextArea
            placeholder="请输入知识库描述"
            rows={3}
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          name="type"
          label={
            <Space>
              知识库类型
              <InfoCircleOutlined title="选择适合您数据类型的知识库类型" />
            </Space>
          }
          rules={[{ required: true, message: "请选择知识库类型" }]}
        >
          <Select placeholder="请选择知识库类型">
            {KNOWLEDGE_BASE_TYPES.map((type) => (
              <Option key={type.key} value={type.key}>
                <div>
                  <div style={{ fontWeight: 500 }}>{type.label}</div>
                  <div style={{ fontSize: 12, color: '#666' }}>{type.description}</div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="embedding_model"
          label={
            <Space>
              嵌入模型
              <InfoCircleOutlined title="选择用于向量化文档的嵌入模型" />
            </Space>
          }
          rules={[{ required: true, message: "请选择嵌入模型" }]}
        >
          <Select placeholder="请选择嵌入模型">
            {EMBEDDING_MODELS.map((model) => (
              <Option key={model.key} value={model.key}>
                <div>
                  <div style={{ fontWeight: 500 }}>{model.label}</div>
                  <div style={{ fontSize: 12, color: '#666' }}>提供商: {model.provider}</div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          name="tags"
          label={
            <Space>
              标签
              <InfoCircleOutlined title="用逗号分隔多个标签，便于分类和搜索" />
            </Space>
          }
        >
          <Input placeholder="请输入标签，用逗号分隔" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default KnowledgeBaseFormModal;