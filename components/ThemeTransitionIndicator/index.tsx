import React from 'react';
import { useThemeStore } from '../../stores';
import './style.scss';

/**
 * 主题切换过渡状态指示器
 * 用于调试和监控主题切换过程
 */
const ThemeTransitionIndicator: React.FC = () => {
  const { isTransitioning, transitionPhase, transitionStartTime } = useThemeStore();

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development' || !isTransitioning) {
    return null;
  }

  const duration = Date.now() - transitionStartTime;

  return (
    <div className="theme-transition-indicator">
      <div className="indicator-content">
        <div className="indicator-title">主题切换中</div>
        <div className="indicator-phase">阶段: {transitionPhase}</div>
        <div className="indicator-duration">耗时: {duration}ms</div>
        <div className="indicator-progress">
          <div 
            className="progress-bar" 
            style={{ 
              width: `${Math.min((duration / 800) * 100, 100)}%` 
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default ThemeTransitionIndicator;
