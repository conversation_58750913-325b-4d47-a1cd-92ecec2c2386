import React, { useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Switch,
  Checkbox,
  Space,
  Typography,
  Card,
  Row,
  Col,
  Tooltip,
  Tag,
  Alert,
} from "antd";
import {
  ReloadOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  ApiOutlined,
  BulbOutlined,
  GlobalOutlined,
  CloseOutlined,
} from "@ant-design/icons";
import { MODEL_TYPE_OPTIONS, ModelCreateRequest } from "../../types/model";
import { useModelCreateFormStore } from "../../stores";
import "./style.scss";

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;


interface ModelCreateFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: ModelCreateRequest) => void;
  loading?: boolean;
}

const capabilityOptions = [
  {
    key: "enableVision",
    label: "视觉能力",
    description: "支持图像和视频输入",
    icon: <EyeOutlined />,
  },
  {
    key: "enableFunctionCalling",
    label: "函数调用",
    description: "支持工具和函数调用",
    icon: <ApiOutlined />,
  },
  {
    key: "enableInference",
    label: "推理能力",
    description: "支持复杂推理任务",
    icon: <BulbOutlined />,
  },
  {
    key: "enableOnline",
    label: "在线能力",
    description: "支持实时网络访问",
    icon: <GlobalOutlined />,
  },
];

const ModelCreateForm: React.FC<ModelCreateFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  loading = false,
}) => {
  const [form] = Form.useForm();

  // 使用 Zustand store
  const {
    saving,
    error,
    parameters,
    apiSources,
    loadingApiSources,
    generatedId,
    setParameters,
    addParameter,
    removeParameter,
    updateParameter,
    fetchApiSources,
    generateId,
    createModel,
    resetForm,
    setError,
    clearError,
  } = useModelCreateFormStore();

  // 生成新的ID
  const handleGenerateId = () => {
    const newId = generateId();
    form.setFieldValue("id", newId);
  };

  // 初始化表单
  useEffect(() => {
    if (visible) {
      // 获取API源数据
      fetchApiSources();

      // 检查是否已有ID，避免重复生成
      const currentID = form.getFieldValue('id');
      if (!currentID) {
        const initialID = generateId();
        form.setFieldsValue({
          id: initialID,
          status: true,
          capabilities: [],
        });
      } else {
        // 如果已有ID，只重置其他字段
        form.setFieldsValue({
          status: true,
          capabilities: [],
        });
      }
      clearError();
    }
  }, [visible, form, fetchApiSources, generateId, clearError]);

  // 参数操作方法已从 store 中获取，无需重复定义

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const filteredParams = parameters.filter((param) => param.name && param.value);

      // 处理功能配置，将数组转换为布尔值字段
      const capabilities = values.capabilities || [];

      // 获取选择的API源信息，用于显示供应商名称
      const selectedApiSource = apiSources.find(source => source.id === values.apiSourceId);

      const submitData: ModelCreateRequest = {
        ...values,
        parameters: filteredParams.map(param => ({
          name: param.name,
          value: param.value,
          type: "string", // 默认类型，可以根据需要扩展
          description: "" // 默认描述
        })),
        vendor: selectedApiSource?.name || '', // 保留供应商名称用于显示
        capabilities,
      };

      // 使用 store 的方法创建模型
      await createModel(submitData);

      // 调用父组件的回调
      onSubmit(submitData);
    } catch (error: any) {
      if (error.errorFields) {
        // Ant Design 表单验证错误，不需要额外处理
        return;
      }
      // API 错误已在 store 中处理
    }
  };

  // 取消并重置表单
  const handleCancel = () => {
    form.resetFields();
    resetForm();
    onCancel();
  };

  return (
    <Modal
      title="创建模型"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading || saving}
          onClick={handleSubmit}
        >
          创建
        </Button>,
      ]}
      width={800}
      className="model-create-form-modal"
      destroyOnClose
    >
      {/* 错误提示 */}
      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        className="model-create-form"
        size="middle"
      >
        {/* 基本信息 */}
        <Card title="基本信息" size="small" className="form-section">
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label={
                  <Space>
                    模型ID
                    <Tooltip title="模型的唯一标识符，用于API调用">
                      <InfoCircleOutlined style={{ color: '#1890ff' }} />
                    </Tooltip>
                  </Space>
                }
                name="id"
                rules={[{ required: true, message: "请输入模型ID" }]}
              >
                <Input
                  placeholder="输入模型ID或点击生成"
                  addonAfter={
                    <Button
                      type="link"
                      icon={<ReloadOutlined />}
                      onClick={handleGenerateId}
                      size="small"
                    >
                      生成
                    </Button>
                  }
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="模型名称"
                name="name"
                rules={[{ required: true, message: "请输入模型名称" }]}
              >
                <Input placeholder="输入显示名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="模型类型"
                name="type"
                rules={[{ required: true, message: "请选择模型类型" }]}
              >
                <Select placeholder="选择模型类型">
                  {MODEL_TYPE_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="供应商模型ID"
                name="vendorId"
                rules={[{ required: true, message: "请输入供应商模型ID" }]}
              >
                <Input placeholder="如: gpt-3.5-turbo" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="API数据源"
                name="apiSourceId"
                rules={[{ required: true, message: "请选择API数据源" }]}
              >
                <Select
                  placeholder="选择API数据源"
                  loading={loadingApiSources}
                  showSearch
                  optionFilterProp="children"
                >
                  {apiSources.map((source) => (
                    <Option key={source.id} value={source.id}>
                      <Space>
                        <span>{source.name}</span>
                        <Tag>{source.type}</Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="描述" name="description">
            <TextArea
              placeholder="模型描述信息（可选）"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Card>

        {/* 模型能力 */}
        <Card title="模型能力" size="small" className="form-section">
          <Form.Item name="capabilities">
            <Checkbox.Group className="capability-group">
              <Row gutter={[16, 16]}>
                {capabilityOptions.map((capability) => (
                  <Col span={12} key={capability.key}>
                    <Card
                      className="capability-card"
                      size="small"
                      bodyStyle={{ padding: '12px 16px' }}
                    >
                      <Checkbox value={capability.key}>
                        <Space>
                          {capability.icon}
                          <div>
                            <div className="capability-title">
                              {capability.label}
                            </div>
                            <div className="capability-desc">
                              {capability.description}
                            </div>
                          </div>
                        </Space>
                      </Checkbox>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Card>

        {/* 自定义参数 */}
        <Card 
          title="自定义参数" 
          size="small" 
          className="form-section"
          extra={
            <Button 
              type="dashed" 
              size="small" 
              onClick={addParameter}
              icon={<SettingOutlined />}
            >
              添加参数
            </Button>
          }
        >
          {parameters.map((param, index) => (
            <Row gutter={8} key={index} className="parameter-row">
              <Col span={10}>
                <Input
                  placeholder="参数名"
                  value={param.name}
                  onChange={(e) => updateParameter(index, 'name', e.target.value)}
                />
              </Col>
              <Col span={12}>
                <Input
                  placeholder="参数值"
                  value={param.value}
                  onChange={(e) => updateParameter(index, 'value', e.target.value)}
                />
              </Col>
              <Col span={2}>
                <Button
                  type="text"
                  danger
                  icon={<CloseOutlined />}
                  onClick={() => removeParameter(index)}
                  disabled={parameters.length === 1}
                  size="small"
                />
              </Col>
            </Row>
          ))}
          
          {parameters.length === 0 && (
            <div className="empty-parameters">
              <Text type="secondary">暂无自定义参数</Text>
            </div>
          )}
        </Card>

        {/* 状态设置 */}
        <Card title="状态设置" size="small" className="form-section">
          <Form.Item
            label="启用状态"
            name="status"
            valuePropName="checked"
            extra="模型创建后是否立即启用"
          >
            <Switch />
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};

export default ModelCreateForm;