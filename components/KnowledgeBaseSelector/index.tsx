import React, { useState, useEffect } from "react";
import {
  Switch,
  Card,
  Button,
  Modal,
  Table,
  Space,
  Tag,
  Input,
  Select,
  Slider,
  InputNumber,
  Form,
  Checkbox,
  Empty,
} from "antd";
import {
  DatabaseOutlined,
  // SearchOutlined, // 未使用
  SettingOutlined,
} from "@ant-design/icons";
import type { RAGConfig, KnowledgeBase } from "../../types/assistant";
import type { ColumnsType } from "antd/es/table";
import "./style.scss";

const { Search } = Input;
const { Option } = Select;

interface KnowledgeBaseSelectorProps {
  ragEnabled: boolean;
  onRAGEnabledChange: (enabled: boolean) => void;
  ragConfig?: RAGConfig;
  onRAGConfigChange: (config: RAGConfig) => void;
  selectedKnowledgeBases: string[];
  onKnowledgeBasesChange: (kbIds: string[]) => void;
}

// Mock知识库数据
const mockKnowledgeBases: KnowledgeBase[] = [
  {
    id: "kb-1",
    name: "Vue.js官方文档",
    description: "Vue.js框架的官方文档和API参考",
    document_count: 156,
    vector_count: 2340,
    status: "ready",
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-06-15T14:30:00Z",
  },
  {
    id: "kb-2",
    name: "JavaScript最佳实践",
    description: "JavaScript编程最佳实践和设计模式",
    document_count: 89,
    vector_count: 1567,
    status: "ready",
    created_at: "2024-02-10T09:00:00Z",
    updated_at: "2024-06-10T16:20:00Z",
  },
  {
    id: "kb-3",
    name: "TypeScript手册",
    description: "TypeScript语言特性和类型系统详解",
    document_count: 234,
    vector_count: 3456,
    status: "processing",
    created_at: "2024-03-05T14:00:00Z",
    updated_at: "2024-06-18T11:45:00Z",
  },
];

const KnowledgeBaseSelector: React.FC<KnowledgeBaseSelectorProps> = ({
  ragEnabled,
  onRAGEnabledChange,
  ragConfig,
  onRAGConfigChange,
  selectedKnowledgeBases,
  onKnowledgeBasesChange,
}) => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>([]);
  const [selectorVisible, setSelectorVisible] = useState(false);
  const [configVisible, setConfigVisible] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [loading, setLoading] = useState(false);

  // 默认RAG配置
  const defaultRAGConfig: RAGConfig = {
    retrieval_strategy: "similarity",
    similarity_threshold: 0.7,
    top_k: 5,
    max_context_length: 2000,
    rerank_enabled: false,
    citation_enabled: true,
  };

  useEffect(() => {
    if (ragEnabled) {
      loadKnowledgeBases();
      if (!ragConfig) {
        onRAGConfigChange(defaultRAGConfig);
      }
    }
  }, [ragEnabled]);

  const loadKnowledgeBases = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      setKnowledgeBases(mockKnowledgeBases);
    } catch (error) {
      console.error("加载知识库失败:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleRAGToggle = (enabled: boolean) => {
    onRAGEnabledChange(enabled);
    if (enabled && !ragConfig) {
      onRAGConfigChange(defaultRAGConfig);
    }
  };

  const handleKnowledgeBaseSelect = (selectedRowKeys: React.Key[]) => {
    onKnowledgeBasesChange(selectedRowKeys as string[]);
  };

  const handleConfigSave = (values: any) => {
    onRAGConfigChange(values);
    setConfigVisible(false);
  };

  const filteredKnowledgeBases = knowledgeBases.filter(kb =>
    kb.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    kb.description.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  const selectedKBs = knowledgeBases.filter(kb =>
    selectedKnowledgeBases.includes(kb.id)
  );

  const columns: ColumnsType<KnowledgeBase> = [
    {
      title: "知识库名称",
      dataIndex: "name",
      key: "name",
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: 12, color: "#666" }}>
            {record.description}
          </div>
        </div>
      ),
    },
    {
      title: "文档数量",
      dataIndex: "document_count",
      key: "document_count",
      width: 100,
    },
    {
      title: "向量数量",
      dataIndex: "vector_count",
      key: "vector_count",
      width: 100,
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      render: (status) => (
        <Tag color={status === "ready" ? "green" : "orange"}>
          {status === "ready" ? "就绪" : "处理中"}
        </Tag>
      ),
    },
  ];

  return (
    <div>
      <Card
        title={
          <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
            <DatabaseOutlined />
            <span>知识库集成 (RAG)</span>
          </div>
        }
        extra={
          <Switch
            checked={ragEnabled}
            onChange={handleRAGToggle}
            checkedChildren="启用"
            unCheckedChildren="禁用"
          />
        }
      >
        {!ragEnabled ? (
          <Empty
            description="启用RAG功能以集成知识库"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        ) : (
          <div>
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Button
                  type="primary"
                  icon={<DatabaseOutlined />}
                  onClick={() => setSelectorVisible(true)}
                >
                  选择知识库 ({selectedKnowledgeBases.length})
                </Button>
                <Button
                  icon={<SettingOutlined />}
                  onClick={() => setConfigVisible(true)}
                  style={{ marginLeft: 8 }}
                >
                  RAG配置
                </Button>
              </div>

              {selectedKBs.length > 0 && (
                <div>
                  <div style={{ marginBottom: 8, fontWeight: 500 }}>
                    已选择的知识库:
                  </div>
                  <Space wrap>
                    {selectedKBs.map(kb => (
                      <Tag
                        key={kb.id}
                        color="blue"
                        closable
                        onClose={() => {
                          const newSelected = selectedKnowledgeBases.filter(
                            id => id !== kb.id
                          );
                          onKnowledgeBasesChange(newSelected);
                        }}
                      >
                        {kb.name}
                      </Tag>
                    ))}
                  </Space>
                </div>
              )}

              {ragConfig && (
                <div>
                  <div style={{ marginBottom: 8, fontWeight: 500 }}>
                    当前RAG配置:
                  </div>
                  <Space wrap>
                    <Tag>检索策略: {ragConfig.retrieval_strategy}</Tag>
                    <Tag>相似度阈值: {ragConfig.similarity_threshold}</Tag>
                    <Tag>检索数量: {ragConfig.top_k}</Tag>
                    <Tag>上下文长度: {ragConfig.max_context_length}</Tag>
                    <Tag color={ragConfig.rerank_enabled ? "green" : "red"}>
                      重排序: {ragConfig.rerank_enabled ? "启用" : "禁用"}
                    </Tag>
                    <Tag color={ragConfig.citation_enabled ? "green" : "red"}>
                      引用来源: {ragConfig.citation_enabled ? "显示" : "隐藏"}
                    </Tag>
                  </Space>
                </div>
              )}
            </Space>
          </div>
        )}
      </Card>

      {/* 知识库选择模态框 */}
      <Modal
        title="选择知识库"
        open={selectorVisible}
        onCancel={() => setSelectorVisible(false)}
        onOk={() => setSelectorVisible(false)}
        width={800}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Search
            placeholder="搜索知识库名称或描述"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />
        </div>

        <Table
          columns={columns}
          dataSource={filteredKnowledgeBases}
          rowKey="id"
          loading={loading}
          rowSelection={{
            type: "checkbox",
            selectedRowKeys: selectedKnowledgeBases,
            onChange: handleKnowledgeBaseSelect,
            getCheckboxProps: (record) => ({
              disabled: record.status !== "ready",
            }),
          }}
          pagination={false}
          scroll={{ y: 400 }}
        />
      </Modal>

      {/* RAG配置模态框 */}
      <Modal
        title="RAG配置"
        open={configVisible}
        onCancel={() => setConfigVisible(false)}
        onOk={() => {
          // 这里应该获取表单值并保存
          setConfigVisible(false);
        }}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          layout="vertical"
          initialValues={ragConfig || defaultRAGConfig}
          onFinish={handleConfigSave}
        >
          <Form.Item
            label="检索策略"
            name="retrieval_strategy"
            rules={[{ required: true }]}
          >
            <Select>
              <Option value="similarity">相似度检索</Option>
              <Option value="keyword">关键词检索</Option>
              <Option value="hybrid">混合检索</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="相似度阈值"
            name="similarity_threshold"
            rules={[{ required: true }]}
          >
            <Slider
              min={0}
              max={1}
              step={0.1}
              marks={{
                0: "0",
                0.5: "0.5",
                1: "1",
              }}
            />
          </Form.Item>

          <Form.Item
            label="检索数量 (Top-K)"
            name="top_k"
            rules={[{ required: true }]}
          >
            <InputNumber min={1} max={20} style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item
            label="最大上下文长度"
            name="max_context_length"
            rules={[{ required: true }]}
          >
            <InputNumber min={500} max={8000} style={{ width: "100%" }} />
          </Form.Item>

          <Form.Item name="rerank_enabled" valuePropName="checked">
            <Checkbox>启用重排序</Checkbox>
          </Form.Item>

          <Form.Item name="citation_enabled" valuePropName="checked">
            <Checkbox>显示引用来源</Checkbox>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KnowledgeBaseSelector;
