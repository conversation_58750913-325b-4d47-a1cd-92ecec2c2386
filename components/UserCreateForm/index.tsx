"use client"

import React, { useEffect } from "react"
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Switch,
  Row,
  Col,
  Avatar,
  Typography,
  Upload,
  Alert
} from "antd"
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
  LoadingOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  LockOutlined
} from "@ant-design/icons"
import type { RcFile } from "antd/es/upload/interface"
import { type User, type UserFormData, GENDER_OPTIONS, ROLE_OPTIONS } from "../../types/user"
import { useUserCreateFormStore } from "../../stores"
import authUtils from "../../utils/auth"
import "./style.scss"

const { Option } = Select
const { Text } = Typography

interface UserCreateFormProps {
  visible: boolean
  onCancel: () => void
  onSubmit: (values: UserFormData) => void
  loading?: boolean
  user?: User // 编辑模式时传入的用户数据
}

const UserCreateForm: React.FC<UserCreateFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  loading = false,
  user = null,
}) => {
  const [form] = Form.useForm()

  // 使用 Zustand store
  const {
    saving,
    avatarLoading,
    error,
    editingUser,
    avatarUrl,
    avatarFile,
    setEditingUser,
    setAvatarUrl,
    setAvatarFile,
    uploadAvatar,
    createUser,
    updateUser,
    resetForm,
    setError,
    clearError,
  } = useUserCreateFormStore()

  const isEdit = !!user
  const currentEditingUser = editingUser || user

  // 判断是否在编辑自己的账号
  const isEditingSelf = isEdit && currentEditingUser?.id === authUtils.getCurrentUserId()

  // 初始化表单
  useEffect(() => {
    if (visible && user) {
      // 编辑模式：设置现有用户数据
      setEditingUser(user)
      form.setFieldsValue({
        username: user.username,
        nickname: user.nickname,
        email: user.email,
        phone: user.phone,
        gender: user.gender,
        role: user.role,
        status: user.status,
      })
      setAvatarUrl(user.avatar)
    } else if (visible && !user) {
      // 创建模式：重置表单
      setEditingUser(null)
      form.resetFields()
      form.setFieldsValue({
        role: "user",
        status: true,
      })
      setAvatarUrl(undefined)
      setAvatarFile(null)
    }
    clearError()
  }, [visible, user, form, setEditingUser, setAvatarUrl, setAvatarFile, clearError])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      // 如果有头像文件，先上传头像
      let avatarPath = avatarUrl
      if (avatarFile) {
        try {
          avatarPath = await uploadAvatar(avatarFile)
        } catch (error) {
          // 错误已在 store 中处理
          return
        }
      }

      // 构建提交数据
      const submitData: UserFormData = {
        ...values,
        avatar: avatarPath,
      }

      // 如果是编辑模式且没有输入密码，则不包含密码字段
      if (isEdit && !values.password) {
        delete submitData.password
      }

      // 使用 store 的方法提交数据
      if (isEdit && currentEditingUser) {
        await updateUser(currentEditingUser.id, submitData)
      } else {
        await createUser(submitData)
      }

      // 调用父组件的回调
      onSubmit(submitData)
    } catch (error: any) {
      if (error.errorFields) {
        // Ant Design 表单验证错误，不需要额外处理
        return
      }
      // API 错误已在 store 中处理
    }
  }

  const handleCancel = () => {
    form.resetFields()
    resetForm()
    onCancel()
  }

  // 头像上传前的验证
  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === "image/jpeg" || file.type === "image/png"
    if (!isJpgOrPng) {
      setError("只能上传 JPG/PNG 格式的图片!")
      return false
    }
    const isLt2M = file.size / 1024 / 1024 < 2
    if (!isLt2M) {
      setError("图片大小不能超过 2MB!")
      return false
    }

    // 预览图片
    const reader = new FileReader()
    reader.onload = (e) => {
      setAvatarUrl(e.target?.result as string)
    }
    reader.readAsDataURL(file)
    setAvatarFile(file)

    return false // 阻止自动上传
  }

  return (
    <Modal
      title={isEdit ? "编辑用户" : "新建用户"}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading || saving || avatarLoading}
          onClick={handleSubmit}
        >
          {isEdit ? "保存修改" : "创建用户"}
        </Button>
      ]}
      width={520}
      destroyOnHidden
      className="user-create-modal"
      maskClosable={false}
    >
      <div className="modal-content">
        {/* 错误提示 */}
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            closable
            onClose={clearError}
            style={{ marginBottom: 16 }}
          />
        )}

        <Form form={form} layout="vertical" className="user-form" size="middle" requiredMark="optional">
          {/* 头像上传区域 */}
          <Form.Item label="用户头像" className="avatar-form-item">
            <div className="avatar-upload-wrapper">
              <Upload
                name="avatar"
                className="avatar-uploader"
                showUploadList={false}
                beforeUpload={beforeUpload}
                disabled={avatarLoading}
              >
                <div className="avatar-upload-area">
                  {avatarUrl ? (
                    <Avatar size={64} src={avatarUrl} />
                  ) : (
                    <div className="avatar-placeholder">
                      {avatarLoading ? <LoadingOutlined /> : <PlusOutlined />}
                    </div>
                  )}
                </div>
              </Upload>
              <Text type="secondary" className="upload-hint">
                支持 JPG、PNG 格式，文件大小不超过 2MB
              </Text>
            </div>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="username"
                label="用户名"
                rules={[
                  { required: true, message: "请输入用户名" },
                  { min: 3, max: 20, message: "用户名长度为3-20个字符" },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: "用户名只能包含字母、数字和下划线" },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入用户名"
                  disabled={isEdit}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="nickname"
                label="昵称（可选）"
                rules={[{ max: 50, message: "昵称长度不能超过50个字符" }]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="请输入昵称"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: "请输入邮箱" },
                  { type: "email", message: "请输入有效的邮箱地址" },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱地址"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="手机号码（可选）"
                rules={[{ pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号" }]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="请输入手机号码"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="role"
                label="用户角色"
                rules={[{ required: true, message: "请选择角色" }]}
              >
                <Select
                  placeholder="请选择角色"
                  disabled={isEditingSelf}
                >
                  {ROLE_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="请选择性别" allowClear>
                  {GENDER_OPTIONS.map((option) => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="status" label="账户状态" valuePropName="checked">
                <Switch
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                  disabled={isEditingSelf}
                />
                {isEditingSelf && (
                  <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
                    不能修改自己的状态
                  </div>
                )}
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="password"
            label={isEdit ? "新密码" : "登录密码"}
            rules={
              isEdit
                ? [{ min: 6, message: "密码长度至少6个字符" }]
                : [
                    { required: true, message: "请输入密码" },
                    { min: 6, message: "密码长度至少6个字符" },
                  ]
            }
            extra={isEdit ? "留空则不修改密码，建议使用字母、数字和特殊字符组合" : "建议使用字母、数字和特殊字符组合，提高安全性"}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={isEdit ? "留空则不修改密码" : "请输入登录密码"}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  )
}

export default UserCreateForm
