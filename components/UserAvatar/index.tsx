import React from 'react';
import { Avatar, Dropdown, message } from 'antd';
import { 
  UserOutlined, 
  LogoutOutlined, 
  SettingOutlined,
  ProfileOutlined 
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import type { MenuProps } from 'antd';
import './style.scss';

export interface UserInfo {
  id: string;
  username: string;
  nickname?: string;
  avatar?: string;
  email?: string;
  role?: string;
}

export interface UserAvatarProps {
  user?: UserInfo | null;
  onLogout?: () => Promise<void>;
  onMenuClick?: (key: string) => void;
  className?: string;
}

const UserAvatar: React.FC<UserAvatarProps> = ({
  user,
  onLogout,
  onMenuClick,
  className = ''
}) => {
  // 处理菜单点击事件
  const handleMenuClick: MenuProps['onClick'] = async ({ key }) => {
    if (key === 'logout') {
      const hide = message.loading('退出登录...', 0);
      try {
        if (onLogout) {
          await onLogout();
        }
        message.success('退出登录成功', 3);
      } catch (error) {
        message.error('退出登录失败');
      } finally {
        hide();
      }
    } else {
      onMenuClick?.(key);
    }
  };

  // 菜单项配置 - 完全参考demo实现
  const menuItems: MenuProps['items'] = [
    {
      key: '0',
      icon: <UserOutlined />,
      label: (
        <Link to="/account/center">
          个人中心
        </Link>
      ),
    },
    {
      key: '1',
      icon: <ProfileOutlined />,
      label: (
        <Link to="/account/settings">
          个人设置
        </Link>
      ),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  // 如果没有用户信息，显示未登录状态
  if (!user) {
    return (
      <span className={`user-avatar-wrapper ${className}`}>
        <Avatar size="small" icon={<UserOutlined />} />
        <span className="anticon">未登录</span>
      </span>
    );
  }

  // 显示用户头像和下拉菜单
  return (
    <Dropdown
      menu={{
        items: menuItems,
        onClick: handleMenuClick,
      }}
      placement="bottomRight"
      arrow
    >
      <span className={`user-avatar-wrapper ${className}`}>
        <Avatar 
          src={user.avatar} 
          size="small"
          icon={!user.avatar && <UserOutlined />}
        />
        <span className="anticon">{user.nickname || user.username}</span>
      </span>
    </Dropdown>
  );
};

export default UserAvatar;
