import React, { useState, useEffect } from "react";
import { <PERSON>lt<PERSON>,
  Modal,
  Card,
  Row,
  Col,
  Statistic,
  Input,
  Button,
  Upload,
  Table,
  Tag,
  Space,
  message,
  Progress,
  Tabs,
  Typography,
  Popconfirm,
  Drawer,
  Form,
  Badge,
  Alert,
  Descriptions
} from "antd";
import {
  FilePdfOutlined,
  FileWordOutlined,
  FileMarkdownOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  InboxOutlined,
  BlockOutlined,
  EditOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  FileTextOutlined
} from "@ant-design/icons";
import type {
  KnowledgeBase,
  Document,
  DocumentChunk,
} from "../../types/knowledgeBase";
import { knowledgeBaseAPI } from "../../services/api";
import type { ColumnsType } from "antd/es/table";
import type { UploadProps } from "antd";
import "./style.scss";

const { TabPane } = Tabs;
const { Text, Paragraph } = Typography;
const { Search } = Input;
const { Dragger } = Upload;

interface DocumentManagementModalProps {
  visible: boolean;
  knowledgeBase?: KnowledgeBase | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const DocumentManagementModal: React.FC<DocumentManagementModalProps> = ({
  visible,
  knowledgeBase,
  onCancel,
  onSuccess,
}) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [chunks, setChunks] = useState<DocumentChunk[]>([]);
  const [loading, setLoading] = useState(false);
  const [chunksLoading, setChunksLoading] = useState(false);
  const [searchKeyword, setSearchKeyword] = useState("");
  const [chunkSearchKeyword, setChunkSearchKeyword] = useState("");
  const [uploading, setUploading] = useState(false);
  const [activeTab, setActiveTab] = useState("documents");
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // 新增状态
  const [chunkDetailVisible, setChunkDetailVisible] = useState(false);
  const [selectedChunk, setSelectedChunk] = useState<DocumentChunk | null>(null);
  const [editingDocument, setEditingDocument] = useState<Document | null>(null);
  const [documentEditVisible, setDocumentEditVisible] = useState(false);
  const [editForm] = Form.useForm();

  // 获取文档列表
  const fetchDocuments = async () => {
    if (!knowledgeBase) return;
    try {
      setLoading(true);
      const response = await knowledgeBaseAPI.getDocuments(knowledgeBase.id, {
        search: searchKeyword,
        page: 1,
        page_size: 100,
      });
      setDocuments(response.data.data.items || []);
    } catch (error) {
      console.error("获取文档列表失败:", error);
      message.error("获取文档列表失败");
    } finally {
      setLoading(false);
    }
  };

  // 获取文档分块
  const fetchDocumentChunks = async (documentId: string) => {
    if (!knowledgeBase) return;
    try {
      setChunksLoading(true);
      const response = await knowledgeBaseAPI.getDocumentChunks(
        knowledgeBase.id,
        documentId,
        {
          search: chunkSearchKeyword,
          page: 1,
          page_size: 100,
        }
      );
      setChunks(response.data.data.items || []);
    } catch (error) {
      console.error("获取文档分块失败:", error);
      message.error("获取文档分块失败");
    } finally {
      setChunksLoading(false);
    }
  };

  useEffect(() => {
    if (visible && knowledgeBase) {
      fetchDocuments();
    }
  }, [visible, knowledgeBase, searchKeyword]);

  useEffect(() => {
    if (selectedDocument && knowledgeBase) {
      fetchDocumentChunks(selectedDocument.id);
    }
  }, [selectedDocument, chunkSearchKeyword, knowledgeBase]);

  // 获取文件类型图标
  const getFileIcon = (type: string) => {
    const iconMap = {
      pdf: <FilePdfOutlined style={{ color: '#ff4d4f' }} />,
      docx: <FileWordOutlined style={{ color: '#1890ff' }} />,
      md: <FileMarkdownOutlined style={{ color: '#52c41a' }} />,
      txt: <FileTextOutlined style={{ color: '#666' }} />,
      html: <FileTextOutlined style={{ color: '#fa8c16' }} />,
      csv: <FileTextOutlined style={{ color: '#13c2c2' }} />,
      xlsx: <FileTextOutlined style={{ color: '#52c41a' }} />,
    };
    return iconMap[type as keyof typeof iconMap] || <FileTextOutlined />;
  };

  // 获取状态标签和图标
  const getStatusTag = (status: string) => {
    const statusConfig = {
      processing: {
        color: 'blue',
        text: '处理中',
        icon: <LoadingOutlined spin />,
        description: '正在处理文档'
      },
      completed: {
        color: 'green',
        text: '已完成',
        icon: <CheckCircleOutlined />,
        description: '文档处理完成'
      },
      failed: {
        color: 'red',
        text: '失败',
        icon: <ExclamationCircleOutlined />,
        description: '文档处理失败'
      }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || {
      color: 'default',
      text: status,
      icon: <InfoCircleOutlined />,
      description: '未知状态'
    };
    return (
      <Tooltip title={config.description}>
        <Tag color={config.color} icon={config.icon}>
          {config.text}
        </Tag>
      </Tooltip>
    );
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString(navigator.language);
    } catch (e) {
      console.error('Invalid date format:', dateString);
      return 'Invalid date';
    }
  };

  // 删除文档
  const handleDeleteDocument = async (documentId: string) => {
    if (!knowledgeBase) return;
    try {
      await knowledgeBaseAPI.deleteDocument(knowledgeBase.id, documentId);
      message.success("文档删除成功");
      fetchDocuments();
      onSuccess();
    } catch (error) {
      console.error("删除文档失败:", error);
      message.error("删除文档失败");
    }
  };

  // 查看分块详情
  const handleViewChunkDetail = (chunk: DocumentChunk) => {
    setSelectedChunk(chunk);
    setChunkDetailVisible(true);
  };

  // 编辑文档
  const handleEditDocument = (document: Document) => {
    setEditingDocument(document);
    editForm.setFieldsValue({
      name: document.name,
      // 这里可以添加更多可编辑字段
    });
    setDocumentEditVisible(true);
  };

  // 保存文档编辑
  const handleSaveDocumentEdit = async () => {
    try {
      await editForm.validateFields();
      // 这里调用API保存编辑
      // await knowledgeBaseAPI.updateDocument(knowledgeBase.id, editingDocument.id, values);
      message.success("文档更新成功");
      setDocumentEditVisible(false);
      fetchDocuments();
      onSuccess();
    } catch (error) {
      console.error("更新文档失败:", error);
      message.error("更新文档失败");
    }
  };

  // 重新处理文档
  const handleReprocessDocument = async (_id: string) => {
    try {
      // await knowledgeBaseAPI.reprocessDocument(knowledgeBase.id, documentId);
      message.success("文档重新处理已开始");
      fetchDocuments();
    } catch (error) {
      console.error("重新处理文档失败:", error);
      message.error("重新处理文档失败");
    }
  };

  // 复制分块内容
  const handleCopyChunkContent = (content: string) => {
    navigator.clipboard.writeText(content).then(() => {
      message.success("内容已复制到剪贴板");
    }).catch(() => {
      message.error("复制失败");
    });
  };

  // 文档表格列定义
  const documentColumns: ColumnsType<Document> = [
    {
      title: '文档名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {getFileIcon(record.type)}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      render: (type) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      width: 100,
      render: (size) => formatFileSize(size),
    },
    {
      title: '分块数',
      dataIndex: 'chunk_count',
      key: 'chunk_count',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status, record) => (
        <div>
          {getStatusTag(status)}
          {status === 'processing' && record.processing_progress && (
            <Progress
              percent={record.processing_progress}
              size="small"
              style={{ marginTop: 4 }}
            />
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 160,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑文档">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEditDocument(record)}
            />
          </Tooltip>
          <Tooltip title="查看分块">
            <Button
              type="text"
              size="small"
              icon={<BlockOutlined />}
              onClick={() => {
                setSelectedDocument(record);
                setActiveTab("chunks");
              }}
            />
          </Tooltip>
          <Tooltip title="重新处理">
            <Button
              type="text"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleReprocessDocument(record.id)}
              disabled={record.status === 'processing'}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文档吗？"
            onConfirm={() => handleDeleteDocument(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分块表格列定义
  const chunkColumns: ColumnsType<DocumentChunk> = [
    {
      title: '分块索引',
      dataIndex: 'chunk_index',
      key: 'chunk_index',
      width: 80,
      render: (index) => (
        <Badge count={index} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: '内容预览',
      dataIndex: 'content',
      key: 'content',
      render: (content) => (
        <Paragraph
          ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
          style={{ marginBottom: 0, maxWidth: 300 }}
        >
          {content}
        </Paragraph>
      ),
    },
    {
      title: 'Token数',
      dataIndex: 'token_count',
      key: 'token_count',
      width: 80,
      render: (count) => (
        <Tag color="blue">{count}</Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewChunkDetail(record)}
            />
          </Tooltip>
          <Tooltip title="复制内容">
            <Button
              type="text"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyChunkContent(record.content)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 文件上传配置
  const uploadProps: UploadProps = {
    name: 'files',
    multiple: true,
    customRequest: async ({ file, onSuccess, onError }) => {
      if (!knowledgeBase) return;
      try {
        setUploading(true);
        await knowledgeBaseAPI.uploadDocuments(knowledgeBase.id, [file as File]);
        message.success(`${(file as File).name} 上传成功`);
        onSuccess?.(file);
        fetchDocuments();
      } catch (error) {
        console.error("文件上传失败:", error);
        message.error(`${(file as File).name} 上传失败`);
        onError?.(error as Error);
      } finally {
        setUploading(false);
      }
    },
    showUploadList: false,
    accept: '.pdf,.docx,.txt,.md,.html,.csv,.xlsx',
  };

  if (!knowledgeBase) return null;

  return (
    <Modal
      title={`${knowledgeBase.name} - 数据管理`}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1000}
      style={{ top: 20 }}
    >
      <div className="document-management">
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={8}>
            <Card size="small">
              <Statistic title="文档数量" value={knowledgeBase.document_count} />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic title="向量数量" value={knowledgeBase.vector_count} />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic title="总大小" value={formatFileSize(knowledgeBase.total_size)} />
            </Card>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="文档管理" key="documents">
            <div style={{ marginBottom: 16 }}>
              <Row gutter={16} align="middle">
                <Col flex="auto">
                  <Search
                    placeholder="搜索文档名称"
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    style={{ width: 300 }}
                    allowClear
                  />
                </Col>
                <Col>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={fetchDocuments}
                    loading={loading}
                  >
                    刷新
                  </Button>
                </Col>
              </Row>
            </div>

            {/* 文件上传区域 */}
            <Card size="small" style={{ marginBottom: 16 }}>
              <Dragger {...uploadProps} disabled={uploading}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                <p className="ant-upload-hint">
                  支持 PDF, Word, 文本, Markdown, HTML, CSV, Excel 等格式
                </p>
              </Dragger>
            </Card>

            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </TabPane>

          <TabPane tab="分块管理" key="chunks" disabled={!selectedDocument}>
            {selectedDocument && (
              <>
                <div style={{ marginBottom: 16 }}>
                  <Row gutter={16} align="middle">
                    <Col flex="auto">
                      <Space>
                        <Text strong>当前文档:</Text>
                        <Text>{selectedDocument.name}</Text>
                        <Search
                          placeholder="搜索分块内容"
                          value={chunkSearchKeyword}
                          onChange={(e) => setChunkSearchKeyword(e.target.value)}
                          style={{ width: 300 }}
                          allowClear
                        />
                      </Space>
                    </Col>
                    <Col>
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={() => fetchDocumentChunks(selectedDocument.id)}
                        loading={chunksLoading}
                      >
                        刷新
                      </Button>
                    </Col>
                  </Row>
                </div>

                <Table
                  columns={chunkColumns}
                  dataSource={chunks}
                  rowKey="id"
                  loading={chunksLoading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  }}
                />
              </>
            )}
          </TabPane>
        </Tabs>
      </div>

      {/* 分块详情抽屉 */}
      <Drawer
        title={
          <Space>
            <BlockOutlined />
            <span>分块详情</span>
          </Space>
        }
        placement="right"
        width={600}
        open={chunkDetailVisible}
        onClose={() => setChunkDetailVisible(false)}
      >
        {selectedChunk && (
          <div className="chunk-detail-content">
            {/* 基础信息 */}
            <Card size="small" title="基础信息">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="分块索引">
                  <Badge count={selectedChunk.chunk_index} style={{ backgroundColor: '#52c41a' }} />
                </Descriptions.Item>
                <Descriptions.Item label="Token数量">
                  <Tag color="blue">{selectedChunk.token_count}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {getStatusTag(selectedChunk.status)}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  <Space>
                    <ClockCircleOutlined />
                    <Text>{formatDate(selectedChunk.created_at)}</Text>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  <Space>
                    <ClockCircleOutlined />
                    <Text>{formatDate(selectedChunk.updated_at)}</Text>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="分块ID">
                  <Text code copyable>{selectedChunk.id}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 内容 */}
            <Card
              size="small"
              title="分块内容"
              extra={
                <Button
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={() => handleCopyChunkContent(selectedChunk.content)}
                >
                  复制
                </Button>
              }
            >
              <div className="chunk-content-container">
                <Text style={{ whiteSpace: 'pre-wrap', fontSize: '14px', lineHeight: '1.6' }}>
                  {selectedChunk.content}
                </Text>
              </div>
            </Card>

            {/* 元数据 */}
            {selectedChunk.metadata && Object.keys(selectedChunk.metadata).length > 0 && (
              <Card size="small" title="元数据">
                <div className="metadata-container">
                  {Object.entries(selectedChunk.metadata).map(([key, value]) => (
                    <div key={key} className="metadata-item">
                      <Text strong className="metadata-key">{key}:</Text>
                      <Text className="metadata-value">{String(value)}</Text>
                    </div>
                  ))}
                </div>
              </Card>
            )}

            {/* 错误信息 */}
            {selectedChunk.error_message && (
              <Alert
                message="处理错误"
                description={selectedChunk.error_message}
                type="error"
                showIcon
              />
            )}
          </div>
        )}
      </Drawer>

      {/* 文档编辑抽屉 */}
      <Drawer
        title={
          <Space>
            <EditOutlined />
            <span>编辑文档</span>
          </Space>
        }
        placement="right"
        width={500}
        open={documentEditVisible}
        onClose={() => setDocumentEditVisible(false)}
        extra={
          <Space>
            <Button onClick={() => setDocumentEditVisible(false)}>
              取消
            </Button>
            <Button type="primary" onClick={handleSaveDocumentEdit}>
              保存
            </Button>
          </Space>
        }
      >
        {editingDocument && (
          <Form
            form={editForm}
            layout="vertical"
            className="document-edit-form"
          >
            <Form.Item
              name="name"
              label="文档名称"
              rules={[
                { required: true, message: "请输入文档名称" },
                { max: 100, message: "文档名称不能超过100个字符" },
              ]}
            >
              <Input placeholder="请输入文档名称" />
            </Form.Item>

            {/* 文档信息展示 */}
            <Card size="small" title="文档信息">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="文档类型">
                  <Tag>{editingDocument.type.toUpperCase()}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="文件大小">
                  {formatFileSize(editingDocument.size)}
                </Descriptions.Item>
                <Descriptions.Item label="分块数量">
                  {editingDocument.chunk_count}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  {getStatusTag(editingDocument.status)}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {formatDate(editingDocument.created_at)}
                </Descriptions.Item>
                <Descriptions.Item label="文档ID">
                  <Text code copyable>{editingDocument.id}</Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {/* 错误信息 */}
            {editingDocument.error_message && (
              <Alert
                message="处理错误"
                description={editingDocument.error_message}
                type="error"
                showIcon
              />
            )}
          </Form>
        )}
      </Drawer>
    </Modal>
  );
};

export default DocumentManagementModal;