/**
 * Store 初始化组件
 * 负责初始化 Zustand store 的各种状态
 */

import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useTabsStore, useRouteHistoryStore, useRecentVisitsStore } from '../../stores';
import { initializeTheme } from '../../stores/settings/themeSlice';
import { useResponsiveListener } from '../../hooks/useResponsiveListener';
import { shouldRecordRoute } from '../../stores/global/routeHistorySlice';
import { shouldRecordVisit } from '../../stores/global/recentVisitsSlice';
import { getPageInfo } from '../../utils/pageInfo';

interface StoreInitializerProps {
  children: React.ReactNode;
}

/**
 * Store 初始化组件
 * 在应用启动时初始化各种状态，并监听路由变化
 */
const StoreInitializer: React.FC<StoreInitializerProps> = ({ children }) => {
  const location = useLocation();
  
  // 获取 store 方法
  const { loadTabs, addTab } = useTabsStore();
  const { addRoute, setCurrentPath } = useRouteHistoryStore();
  const { addVisit } = useRecentVisitsStore();
  
  // 启用响应式监听
  useResponsiveListener();

  // 初始化 store
  useEffect(() => {
    console.log('🚀 Initializing stores...');
    
    // 初始化主题系统
    const themeCleanup = initializeTheme();
    
    // 加载标签页数据
    loadTabs();
    
    console.log('✅ Stores initialized');
    
    // 返回清理函数
    return () => {
      if (themeCleanup) {
        themeCleanup();
      }
    };
  }, [loadTabs]);

  // 监听路由变化，更新相关状态
  useEffect(() => {
    const currentPath = location.pathname;
    const pageInfo = getPageInfo(currentPath);
    
    console.log('🛣️ Route changed:', currentPath, pageInfo.title);
    
    // 更新当前路径
    setCurrentPath(currentPath);
    
    // 添加标签页（如果需要）
    addTab(currentPath);
    
    // 记录路由历史（如果需要）
    if (shouldRecordRoute(currentPath)) {
      addRoute({
        path: currentPath,
        title: pageInfo.title,
        params: {}, // TODO: 从路由中解析参数
        query: {}, // TODO: 从 location.search 中解析查询参数
      });
    }
    
    // 记录访问记录（如果需要）
    if (shouldRecordVisit(currentPath)) {
      addVisit(currentPath, pageInfo.title, pageInfo.icon);
    }
  }, [location.pathname, addTab, addRoute, addVisit, setCurrentPath]);

  return <>{children}</>;
};

export default StoreInitializer;
