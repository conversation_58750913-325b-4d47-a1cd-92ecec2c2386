# TanStack Table 主题支持

## 概述

TanStack Table 组件现在完全支持亮色和暗色主题切换，通过CSS变量系统实现动态主题响应。

## 支持的主题元素

### 1. 表格主体
- **背景色**: `var(--theme-bg-primary)` - 表格主背景
- **边框色**: `var(--theme-border-color-split)` - 表格外边框
- **过渡效果**: 0.2s ease 平滑过渡

### 2. 表头 (thead th)
- **背景色**: `var(--theme-table-header-bg)` - 表头背景，回退到 `var(--theme-bg-primary)`
- **文字颜色**: `var(--theme-text-primary)` - 表头文字
- **边框色**: `var(--theme-border-color-split)` - 表头底部边框
- **悬停效果**: `var(--theme-table-row-hover)` - 可排序列的悬停背景

### 3. 表格行 (tbody tr)
- **偶数行**: 透明背景
- **奇数行**: `var(--theme-table-row-stripe)` - 斑马纹背景，回退到 `var(--theme-bg-tertiary)`
- **悬停效果**: `var(--theme-table-row-hover)` - 行悬停背景，回退到 `var(--theme-bg-secondary)`

### 4. 表格单元格 (td)
- **文字颜色**: `var(--theme-text-primary)` - 单元格文字
- **边框色**: `var(--theme-border-color-split)` - 单元格底部边框

### 5. 排序指示器
- **默认颜色**: `var(--theme-text-secondary)` - 非激活状态
- **激活颜色**: `$primary-color` - 排序激活状态

### 6. 分页器
- **背景色**: `var(--theme-bg-primary)` - 分页器背景
- **边框色**: `var(--theme-border-color-split)` - 分页器顶部边框
- **文字颜色**: 
  - 主要信息: `var(--theme-text-primary)`
  - 次要信息: `var(--theme-text-secondary)`
  - 筛选信息: `$primary-color`

### 7. 滚动条
- **轨道背景**: `var(--theme-bg-secondary)` - 滚动条轨道
- **滑块背景**: `var(--theme-text-tertiary)` - 滚动条滑块
- **滑块悬停**: `var(--theme-text-secondary)` - 滑块悬停状态

## CSS变量定义

### 亮色主题 (light)
```css
[data-theme="light"] {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-tertiary: #fafafa;
  --theme-text-primary: #000000d9;
  --theme-text-secondary: #00000073;
  --theme-text-tertiary: #00000040;
  --theme-border-color-split: #f0f0f0;
  
  /* 表格特定 */
  --theme-table-header-bg: #ffffff;
  --theme-table-row-hover: #f5f5f5;
  --theme-table-row-stripe: #fafafa;
}
```

### 暗色主题 (dark)
```css
[data-theme="dark"] {
  --theme-bg-primary: #1f1f1f;
  --theme-bg-secondary: #141414;
  --theme-bg-tertiary: #262626;
  --theme-text-primary: rgba(255, 255, 255, 0.85);
  --theme-text-secondary: rgba(255, 255, 255, 0.65);
  --theme-text-tertiary: rgba(255, 255, 255, 0.45);
  --theme-border-color-split: #303030;
  
  /* 表格特定 */
  --theme-table-header-bg: #1f1f1f;
  --theme-table-row-hover: #262626;
  --theme-table-row-stripe: #252525;
}
```

## 使用方法

### 1. 自动主题响应
表格会自动响应全局主题变化，无需额外配置：

```tsx
<TanStackTable
  data={data}
  columns={columns}
  // 其他配置...
/>
```

### 2. 主题切换
通过更改DOM根元素的 `data-theme` 属性来切换主题：

```javascript
// 切换到暗色主题
document.documentElement.setAttribute('data-theme', 'dark');

// 切换到亮色主题
document.documentElement.setAttribute('data-theme', 'light');
```

### 3. 与Zustand主题系统集成
表格自动与项目的Zustand主题管理系统集成，通过ThemeToggle组件切换主题时会自动更新。

## 测试页面

以下页面包含TanStack Table，可用于测试主题切换：

1. **用户管理** - `/user-management`
2. **登录日志** - `/login-logs` 
3. **操作日志** - `/operation-logs`
4. **系统日志** - `/system-logs`
5. **模型管理** - `/model-management`

## 注意事项

1. **过渡效果**: 所有颜色变化都包含0.2s的平滑过渡动画
2. **回退机制**: 表格特定变量提供回退到基础变量的机制
3. **兼容性**: 与现有的Ant Design主题系统完全兼容
4. **性能**: 使用CSS变量避免了重新渲染，提供最佳性能

## 自定义主题

如需自定义表格主题，可以覆盖相应的CSS变量：

```css
[data-theme="custom"] {
  --theme-table-header-bg: #your-color;
  --theme-table-row-hover: #your-hover-color;
  --theme-table-row-stripe: #your-stripe-color;
}
```
