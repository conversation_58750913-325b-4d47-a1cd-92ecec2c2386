import { rankItem, compareItems } from '@tanstack/match-sorter-utils';
import type { FilterFn, SortingFn } from '@tanstack/react-table';
import { sortingFns } from '@tanstack/react-table';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import type { TableSize, SizePresetConfig } from './types';

// 初始化 dayjs 插件
dayjs.extend(relativeTime);

// 表格尺寸预设配置
export const SIZE_PRESETS: Record<TableSize, SizePresetConfig> = {
  compact: {
    headerHeight: 39,
    rowHeight: 40,
    paginationHeight: 48,
    description: '紧凑型 - 适用于数据密集型界面',
  },
  standard: {
    headerHeight: 48,
    rowHeight: 53,
    paginationHeight: 56,
    description: '标准型 - 平衡的视觉效果和空间利用',
  },
  comfortable: {
    headerHeight: 55,
    rowHeight: 65,
    paginationHeight: 62,
    description: '舒适型 - 适用于大屏幕或触控设备',
  },
};

// 获取尺寸预设配置
export const getSizePreset = (size: TableSize = 'standard'): SizePresetConfig => {
  return SIZE_PRESETS[size];
};

// 获取所有可用的尺寸选项
export const getAvailableSizes = (): Array<{ value: TableSize; label: string; description: string }> => {
  return Object.entries(SIZE_PRESETS).map(([key, config]) => ({
    value: key as TableSize,
    label: key === 'compact' ? '紧凑' : key === 'standard' ? '标准' : '舒适',
    description: config.description,
  }));
};

// 模糊筛选函数
export const fuzzyFilter: FilterFn<any> = (row, _columnId, value, addMeta) => {
  if (!value || typeof value !== 'string' || value.trim() === '') {
    return true;
  }

  try {
    // 获取行数据中的搜索字段值
    const searchValue = getSearchableValue(row.original);
    
    // 使用 rankItem 进行模糊匹配
    const itemRank = rankItem(searchValue, value);

    // 添加排名信息到元数据中，用于排序
    if (addMeta) {
      addMeta({
        itemRank: itemRank as any,
      });
    }

    return itemRank.passed;
  } catch (error) {
    console.error('模糊搜索错误:', error);
    return true;
  }
};

// 模糊排序函数
export const fuzzySort: SortingFn<any> = (rowA, rowB, columnId) => {
  try {
    let dir = 0;

    // 检查是否有排名元数据
    const metaA = (rowA as any).columnFiltersMeta?.[columnId];
    const metaB = (rowB as any).columnFiltersMeta?.[columnId];

    if (metaA?.itemRank && metaB?.itemRank) {
      dir = compareItems(metaA.itemRank, metaB.itemRank);
    }

    return dir === 0 ? sortingFns.alphanumeric(rowA, rowB, columnId) : dir;
  } catch (error) {
    console.error('模糊排序错误:', error);
    return 0;
  }
};

// 获取数据的可搜索字段值（需要根据具体数据结构自定义）
export const getSearchableValue = (data: any): string => {
  if (!data || typeof data !== 'object') {
    return String(data || '');
  }

  // 默认搜索所有字符串字段
  const searchableFields = Object.values(data)
    .filter(value => typeof value === 'string' || typeof value === 'number')
    .map(value => String(value));

  return searchableFields.join(' ');
};

// 日期范围筛选函数
export const dateRangeFilter: FilterFn<any> = (row, columnId, value) => {
  if (!value || !Array.isArray(value) || value.length !== 2) {
    return true;
  }

  const [startDate, endDate] = value;
  if (!startDate || !endDate) {
    return true;
  }

  const cellValue = row.getValue(columnId);
  if (!cellValue) {
    return false;
  }

  const cellDate = dayjs(cellValue);
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  return cellDate.isAfter(start.subtract(1, 'day')) && cellDate.isBefore(end.add(1, 'day'));
};

// 简单的全局搜索筛选函数
export const simpleGlobalFilter: FilterFn<any> = (row, _columnId, value) => {
  if (!value || typeof value !== 'string' || value.trim() === '') {
    return true;
  }

  try {
    const searchValue = getSearchableValue(row.original);
    const searchTerm = value.toLowerCase().trim();
    return searchValue.toLowerCase().includes(searchTerm);
  } catch (error) {
    console.error('全局搜索错误:', error);
    return true;
  }
};

// 计算表格高度
export const calculateTableHeight = (
  currentPageRowCount: number,
  pageSize: number,
  options: {
    rowHeight?: number;
    headerHeight?: number;
    includeHeader?: boolean;
    emptyStateHeight?: number;
  } = {}
): string => {
  const {
    rowHeight = 49,
    headerHeight = 41,
    includeHeader = false,
    emptyStateHeight = 280,
  } = options;

  // 空状态
  if (currentPageRowCount === 0) {
    return `${emptyStateHeight}px`;
  }

  // 10条/页：固定高度
  if (pageSize === 10) {
    const height = rowHeight * pageSize;
    return `${includeHeader ? height + headerHeight : height}px`;
  }

  // 20条/页 和 50条/页：动态高度
  const minHeight = rowHeight * 10;
  const actualHeight = rowHeight * currentPageRowCount;
  const finalHeight = Math.max(actualHeight, minHeight);

  return `${includeHeader ? finalHeight + headerHeight : finalHeight}px`;
};

// 计算包含分页组件的总高度
export const calculateTotalHeight = (
  currentPageRowCount: number,
  pageSize: number,
  options: {
    rowHeight?: number;
    headerHeight?: number;
    paginationHeight?: number;
    includeHeader?: boolean;
    includePagination?: boolean;
    emptyStateHeight?: number;
  } = {}
): string => {
  const {
    rowHeight = 49,
    headerHeight = 41,
    paginationHeight = 56,
    includeHeader = false,
    includePagination = false,
    emptyStateHeight = 280,
  } = options;

  // 空状态
  if (currentPageRowCount === 0) {
    const totalHeight = emptyStateHeight + (includePagination ? paginationHeight : 0);
    return `${totalHeight}px`;
  }

  // 10条/页：固定高度
  if (pageSize === 10) {
    let height = rowHeight * pageSize;
    if (includeHeader) height += headerHeight;
    if (includePagination) height += paginationHeight;
    return `${height}px`;
  }

  // 20条/页 和 50条/页：动态高度
  const minHeight = rowHeight * 10;
  const actualHeight = rowHeight * currentPageRowCount;
  let finalHeight = Math.max(actualHeight, minHeight);

  if (includeHeader) finalHeight += headerHeight;
  if (includePagination) finalHeight += paginationHeight;

  return `${finalHeight}px`;
};

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

// 节流函数
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

// 格式化日期时间
export const formatDateTime = (dateTime: string | null): string => {
  if (!dateTime) return '';
  return dayjs(dateTime).format('YYYY-MM-DD HH:mm');
};

// 格式化相对时间
export const formatRelativeTime = (dateTime: string | null): string => {
  if (!dateTime) return '';
  return (dayjs(dateTime) as any).fromNow();
};