import type { ColumnDef, SortingState, PaginationState, ColumnFiltersState } from '@tanstack/react-table';

// 表格尺寸预设类型
export type TableSize = 'compact' | 'standard' | 'comfortable';

// 尺寸预设配置接口
export interface SizePresetConfig {
  headerHeight: number;
  rowHeight: number;
  paginationHeight: number;
  description: string;
}

// 表格配置接口
export interface TableConfig {
  size?: TableSize;
  rowHeight?: number;
  headerHeight?: number;
  paginationHeight?: number;
  emptyStateHeight?: number;
  minRowsForDynamic?: number;
  pageSizeOptions?: number[];
  defaultPageSize?: number;
  minTableWidth?: number;
}

// 分页事件接口
export interface PaginationEvents {
  onPageChange?: (pageIndex: number, pageSize: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
}

// 排序事件接口
export interface SortingEvents {
  onSortingChange?: (sorting: SortingState) => void;
}

// 筛选事件接口
export interface FilterEvents {
  onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
  onGlobalFilterChange?: (globalFilter: string) => void;
}

// 表格事件接口
export interface TableEvents extends PaginationEvents, SortingEvents, FilterEvents {
  onRowClick?: (row: any) => void;
  onRowDoubleClick?: (row: any) => void;
  getRowClassName?: (row: any, index: number) => string;
}

// 主要 Props 接口
export interface TanStackTableProps<TData = any> {
  // 数据相关
  data: TData[];
  columns: ColumnDef<TData, any>[];
  loading?: boolean;
  
  // 筛选相关
  globalFilter?: string;
  columnFilters?: ColumnFiltersState;
  filterFns?: Record<string, any>;
  
  // 排序相关
  sorting?: SortingState;
  
  // 分页相关
  pagination?: PaginationState;
  manualPagination?: boolean;
  
  // 配置相关
  config?: TableConfig;
  
  // 事件处理
  events?: TableEvents;
  
  // 样式相关
  className?: string;
  style?: React.CSSProperties;
  
  // 空状态自定义
  emptyContent?: React.ReactNode;
  
  // 是否显示分页
  showPagination?: boolean;
}

// 表格状态接口
export interface TableState {
  sorting: SortingState;
  pagination: PaginationState;
  columnFilters: ColumnFiltersState;
  globalFilter: string;
}

// 表格维度测量接口
export interface TableDimensions {
  containerHeight: number;
  headerHeight: number;
  rowHeight: number;
  availableHeight: number;
}

// 高度计算策略接口
export interface HeightStrategy {
  type: 'empty' | 'fixed' | 'dynamic';
  height: number;
  description: string;
  useDomMeasurement?: boolean;
}