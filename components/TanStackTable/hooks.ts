import { useState, useCallback, useMemo } from 'react';
import { type SortingState, type PaginationState, type ColumnFiltersState } from '@tanstack/react-table';
import { SIZE_PRESETS } from './utils';
import type { TableSize } from './types';

// 表格状态管理 Hook
export const useTableState = (initialState?: {
  sorting?: SortingState;
  pagination?: PaginationState;
  columnFilters?: ColumnFiltersState;
  globalFilter?: string;
}) => {
  const [sorting, setSorting] = useState<SortingState>(initialState?.sorting || []);
  const [pagination, setPagination] = useState<PaginationState>(
    initialState?.pagination || { pageIndex: 0, pageSize: 10 }
  );
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(
    initialState?.columnFilters || []
  );
  const [globalFilter, setGlobalFilter] = useState<string>(initialState?.globalFilter || '');

  const resetFilters = useCallback(() => {
    setSorting([]);
    setColumnFilters([]);
    setGlobalFilter('');
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, []);

  const resetPagination = useCallback(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, []);

  return {
    sorting,
    setSorting,
    pagination,
    setPagination,
    columnFilters,
    setColumnFilters,
    globalFilter,
    setGlobalFilter,
    resetFilters,
    resetPagination,
  };
};

// 表格配置 Hook
export const useTableConfig = (config?: {
  size?: TableSize;
  rowHeight?: number;
  headerHeight?: number;
  paginationHeight?: number;
  emptyStateHeight?: number;
  minRowsForDynamic?: number;
  pageSizeOptions?: number[];
  defaultPageSize?: number;
  minTableWidth?: number;
}) => {
  return useMemo(() => {
    // 获取尺寸预设配置，默认使用 standard
    const sizePreset = SIZE_PRESETS[config?.size || 'standard'];

    // 基础配置
    const baseConfig = {
      emptyStateHeight: 280,
      minRowsForDynamic: 10,
      pageSizeOptions: [10, 20, 50],
      defaultPageSize: 10,
      minTableWidth: 1000,
    };

    // 合并配置：预设尺寸 -> 自定义配置
    return {
      ...baseConfig,
      headerHeight: sizePreset.headerHeight,
      rowHeight: sizePreset.rowHeight,
      paginationHeight: sizePreset.paginationHeight,
      ...config, // 自定义配置可以覆盖预设值
    };
  }, [config]);
};

// 表格事件处理 Hook
export const useTableEvents = () => {
  const [events, setEvents] = useState<{
    onPageChange?: (pageIndex: number, pageSize: number) => void;
    onPageSizeChange?: (pageSize: number) => void;
    onSortingChange?: (sorting: SortingState) => void;
    onColumnFiltersChange?: (filters: ColumnFiltersState) => void;
    onGlobalFilterChange?: (globalFilter: string) => void;
    onRowClick?: (row: any) => void;
    onRowDoubleClick?: (row: any) => void;
  }>({});

  const updateEvents = useCallback((newEvents: typeof events) => {
    setEvents(prev => ({ ...prev, ...newEvents }));
  }, []);

  return {
    events,
    updateEvents,
  };
};