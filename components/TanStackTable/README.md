# TanStackTable 组件

基于 TanStack Table v8 的可复用表格组件，支持自适应高度、DOM 测量、同步滚动等高级功能。

## 功能特性

### 🎯 核心功能
- ✅ **自适应分页显示**：根据容器高度和行高自动计算每页显示条数
- ✅ **DOM 尺寸计算**：通过实际 DOM 测量获取行高和容器尺寸，而非硬编码值
- ✅ **水平滚动同步**：表头和内容同步滚动
- ✅ **表头粘性定位**：垂直滚动时表头保持可见
- ✅ **分页控件独立**：放置在表格容器外部下方

### 🔧 技术特性
- ✅ **筛选和排序**：支持全局搜索、列筛选、模糊搜索、日期范围筛选
- ✅ **事件处理**：支持行点击、双击、分页变化等事件
- ✅ **响应式设计**：适配移动端和桌面端
- ✅ **TypeScript 支持**：完整的类型定义
- ✅ **可定制配置**：支持自定义行高、分页选项等

## 安装依赖

```bash
npm install @tanstack/react-table @tanstack/match-sorter-utils
npm install antd dayjs
```

## 基本使用

```tsx
import React, { useState, useMemo } from 'react';
import { createColumnHelper, type ColumnDef } from '@tanstack/react-table';
import TanStackTable from './components/TanStackTable';

interface UserData {
  id: number;
  name: string;
  email: string;
  role: string;
}

const columnHelper = createColumnHelper<UserData>();

const MyComponent: React.FC = () => {
  const [data, setData] = useState<UserData[]>([
    { id: 1, name: 'John', email: '<EMAIL>', role: 'admin' },
    { id: 2, name: 'Jane', email: '<EMAIL>', role: 'user' },
  ]);

  const columns = useMemo<ColumnDef<UserData, any>[]>(() => [
    columnHelper.accessor('name', {
      header: '姓名',
      size: 150,
    }),
    columnHelper.accessor('email', {
      header: '邮箱',
      size: 200,
    }),
    columnHelper.accessor('role', {
      header: '角色',
      size: 100,
    }),
  ], []);

  return (
    <TanStackTable
      data={data}
      columns={columns}
      showPagination={true}
    />
  );
};
```

## 高级使用

### 1. 完整配置示例

```tsx
import React, { useState, useCallback } from 'react';
import TanStackTable from './components/TanStackTable';
import { fuzzyFilter, dateRangeFilter } from './components/TanStackTable/utils';

const AdvancedExample: React.FC = () => {
  const [sorting, setSorting] = useState([]);
  const [pagination, setPagination] = useState({ pageIndex: 0, pageSize: 10 });
  const [columnFilters, setColumnFilters] = useState([]);
  const [globalFilter, setGlobalFilter] = useState('');

  const handleRowClick = useCallback((row) => {
    console.log('点击行:', row);
  }, []);

  return (
    <TanStackTable
      data={data}
      columns={columns}
      loading={false}
      
      // 表格状态
      sorting={sorting}
      pagination={pagination}
      columnFilters={columnFilters}
      globalFilter={globalFilter}
      
      // 筛选函数
      filterFns={{
        fuzzy: fuzzyFilter,
        dateRange: dateRangeFilter,
      }}
      
      // 配置选项
      config={{
        size: 'standard', // 使用预设尺寸：compact | standard | comfortable
        // 或者自定义尺寸（会覆盖预设值）
        // rowHeight: 49,
        // headerHeight: 41,
        emptyStateHeight: 280,
        minRowsForDynamic: 10,
        pageSizeOptions: [10, 20, 50],
        defaultPageSize: 10,
        minTableWidth: 1000,
      }}
      
      // 事件处理
      events={{
        onSortingChange: setSorting,
        onPageChange: (pageIndex, pageSize) => {
          setPagination({ pageIndex, pageSize });
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        onRowClick: handleRowClick,
      }}
      
      showPagination={true}
      className="my-table"
    />
  );
};
```

### 2. 自定义列定义

```tsx
const columns = useMemo<ColumnDef<UserData, any>[]>(() => [
  // 基础列
  columnHelper.accessor('username', {
    id: 'username',
    header: '用户名',
    size: 200,
    enableSorting: true,
    cell: ({ row }) => (
      <Space>
        <Avatar src={row.original.avatar} />
        <span>{row.original.username}</span>
      </Space>
    ),
  }),
  
  // 带筛选的列
  columnHelper.accessor('role', {
    id: 'role',
    header: '角色',
    size: 100,
    filterFn: 'equalsString', // 精确匹配
    cell: ({ getValue }) => (
      <Tag color="blue">{getValue()}</Tag>
    ),
  }),
  
  // 日期列（支持日期范围筛选）
  columnHelper.accessor('createdAt', {
    id: 'createdAt',
    header: '创建时间',
    size: 160,
    enableSorting: true,
    filterFn: dateRangeFilter,
    cell: ({ getValue }) => formatDateTime(getValue()),
  }),
  
  // 操作列
  columnHelper.display({
    id: 'actions',
    header: '操作',
    size: 120,
    cell: ({ row }) => (
      <Space>
        <Button 
          size="small" 
          onClick={(e) => {
            e.stopPropagation(); // 阻止行点击事件
            handleEdit(row.original);
          }}
        >
          编辑
        </Button>
        <Button 
          size="small" 
          danger
          onClick={(e) => {
            e.stopPropagation();
            handleDelete(row.original);
          }}
        >
          删除
        </Button>
      </Space>
    ),
  }),
], []);
```

### 3. 筛选功能

```tsx
// 全局搜索
<Input.Search
  placeholder="搜索..."
  value={globalFilter}
  onChange={(e) => setGlobalFilter(e.target.value)}
/>

// 列筛选
const handleFiltersChange = useCallback(() => {
  const newColumnFilters = [];
  
  if (roleFilter !== 'all') {
    newColumnFilters.push({ id: 'role', value: roleFilter });
  }
  
  if (dateRange) {
    newColumnFilters.push({ id: 'createdAt', value: dateRange });
  }
  
  setColumnFilters(newColumnFilters);
}, [roleFilter, dateRange]);
```

## API 参考

### TanStackTableProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | `T[]` | - | 表格数据 |
| columns | `ColumnDef<T, any>[]` | - | 列定义 |
| loading | `boolean` | `false` | 加载状态 |
| sorting | `SortingState` | - | 排序状态 |
| pagination | `PaginationState` | - | 分页状态 |
| columnFilters | `ColumnFiltersState` | - | 列筛选状态 |
| globalFilter | `string` | - | 全局筛选 |
| filterFns | `Record<string, FilterFn>` | - | 自定义筛选函数 |
| config | `TableConfig` | - | 表格配置 |
| events | `TableEvents` | - | 事件处理器 |
| showPagination | `boolean` | `true` | 是否显示分页 |
| className | `string` | - | 自定义样式类 |
| emptyContent | `ReactNode` | - | 空状态内容 |

### TableConfig

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| size | `'compact' \| 'standard' \| 'comfortable'` | `'standard'` | 预设尺寸 |
| rowHeight | `number` | 根据 size 确定 | 行高度（覆盖预设值） |
| headerHeight | `number` | 根据 size 确定 | 表头高度（覆盖预设值） |
| paginationHeight | `number` | 根据 size 确定 | 分页组件高度（覆盖预设值） |
| emptyStateHeight | `number` | `280` | 空状态高度 |
| minRowsForDynamic | `number` | `10` | 动态高度最小行数 |
| pageSizeOptions | `number[]` | `[10, 20, 50]` | 分页选项 |
| defaultPageSize | `number` | `10` | 默认分页大小 |
| minTableWidth | `number` | `1000` | 最小表格宽度 |

### TableEvents

| 事件 | 类型 | 说明 |
|------|------|------|
| onSortingChange | `(sorting: SortingState) => void` | 排序变化 |
| onPageChange | `(pageIndex: number, pageSize: number) => void` | 分页变化 |
| onColumnFiltersChange | `(filters: ColumnFiltersState) => void` | 列筛选变化 |
| onGlobalFilterChange | `(filter: string) => void` | 全局筛选变化 |
| onRowClick | `(row: T) => void` | 行点击 |
| onRowDoubleClick | `(row: T) => void` | 行双击 |

## 尺寸预设

TanStackTable 提供三种预设尺寸配置，以适应不同的使用场景和用户偏好：

### 预设尺寸类型

| 尺寸 | 表头高度 | 行高度 | 分页高度 | 适用场景 |
|------|----------|--------|----------|----------|
| `compact` | 39px | 40px | 48px | 数据密集型界面，需要在有限空间内显示更多数据 |
| `standard` | 48px | 49px | 56px | 标准界面，平衡的视觉效果和空间利用（默认） |
| `comfortable` | 55px | 56px | 64px | 大屏幕或触控设备，提供更舒适的交互体验 |

### 使用方式

#### 1. 使用预设尺寸

```tsx
// 紧凑型 - 适用于数据密集型界面
<TanStackTable
  data={data}
  columns={columns}
  config={{ size: 'compact' }}
/>

// 标准型 - 默认配置
<TanStackTable
  data={data}
  columns={columns}
  config={{ size: 'standard' }}
/>

// 舒适型 - 适用于大屏幕或触控设备
<TanStackTable
  data={data}
  columns={columns}
  config={{ size: 'comfortable' }}
/>
```

#### 2. 自定义覆盖预设值

```tsx
// 使用 comfortable 预设，但自定义行高
<TanStackTable
  data={data}
  columns={columns}
  config={{
    size: 'comfortable',
    rowHeight: 60, // 覆盖预设的 56px
  }}
/>

// 完全自定义（不使用预设）
<TanStackTable
  data={data}
  columns={columns}
  config={{
    headerHeight: 45,
    rowHeight: 52,
  }}
/>
```

#### 3. 获取预设信息

```tsx
import { getSizePreset, getAvailableSizes } from './components/TanStackTable/utils';

// 获取特定尺寸的配置
const standardConfig = getSizePreset('standard');
console.log(standardConfig); // { headerHeight: 41, rowHeight: 49, description: '...' }

// 获取所有可用尺寸选项（用于构建选择器）
const sizeOptions = getAvailableSizes();
console.log(sizeOptions);
// [
//   { value: 'compact', label: '紧凑', description: '紧凑型 - 适用于数据密集型界面' },
//   { value: 'standard', label: '标准', description: '标准型 - 平衡的视觉效果和空间利用' },
//   { value: 'comfortable', label: '舒适', description: '舒适型 - 适用于大屏幕或触控设备' }
// ]
```

### 向后兼容性

- 如果不指定 `size` 属性，默认使用 `standard` 尺寸
- 直接指定 `rowHeight` 和 `headerHeight` 仍然有效，会覆盖预设值
- 现有代码无需修改即可继续工作

## 内置筛选函数

### fuzzyFilter
模糊搜索筛选，支持拼音、部分匹配等。

```tsx
import { fuzzyFilter } from './components/TanStackTable/utils';

// 在 filterFns 中使用
filterFns={{
  fuzzy: fuzzyFilter,
}}

// 在列定义中使用
columnHelper.accessor('name', {
  filterFn: 'fuzzy',
})
```

### dateRangeFilter
日期范围筛选，支持 dayjs 日期对象。

```tsx
import { dateRangeFilter } from './components/TanStackTable/utils';

// 在 filterFns 中使用
filterFns={{
  dateRange: dateRangeFilter,
}}

// 在列定义中使用
columnHelper.accessor('createdAt', {
  filterFn: dateRangeFilter,
})

// 设置筛选值
setColumnFilters([
  { id: 'createdAt', value: [startDate, endDate] }
]);
```

## 工具函数

### calculateTotalHeight
计算包含分页组件的总高度，用于统一计算表格和分页区域的总高度。

```tsx
import { calculateTotalHeight } from './components/TanStackTable';

// 计算包含分页的总高度
const totalHeight = calculateTotalHeight(
  currentPageRowCount, // 当前页行数
  pageSize,            // 每页大小
  {
    rowHeight: 49,           // 行高度
    headerHeight: 48,        // 表头高度
    paginationHeight: 56,    // 分页组件高度
    includeHeader: true,     // 是否包含表头
    includePagination: true, // 是否包含分页
    emptyStateHeight: 280,   // 空状态高度
  }
);

// 使用预设配置计算
const config = getSizePreset('standard');
const totalHeightWithPreset = calculateTotalHeight(10, 20, {
  rowHeight: config.rowHeight,
  headerHeight: config.headerHeight,
  paginationHeight: config.paginationHeight,
  includeHeader: true,
  includePagination: true,
});
```

## 高度计算策略

组件支持三种高度计算策略：

### 1. 空状态（Empty）
当没有数据时，使用固定的空状态高度。

### 2. 固定高度（Fixed）
当 `pageSize = 10` 时，使用固定高度：`10 × rowHeight`

### 3. 动态高度（Dynamic）
当 `pageSize > 10` 时，使用动态高度：
```
height = max(currentPageRowCount × rowHeight, minRowsForDynamic × rowHeight)
```

## DOM 测量

组件会自动测量以下尺寸：
- 容器高度
- 表头高度  
- 行高度（实际测量第一行）
- 可用高度

测量结果会用于精确的高度计算，替代硬编码值。

## 样式定制

### CSS 变量
```scss
.tanstack-table {
  --row-height: 49px;
  --header-height: 41px;
  --border-color: #f0f0f0;
  --hover-color: #f5f5f5;
}
```

### 自定义样式类
```tsx
<TanStackTable
  className="my-custom-table"
  // ...
/>
```

```scss
.my-custom-table {
  .data-table {
    tbody tr:hover {
      background: #e6f7ff;
    }
  }
}
```

## 最佳实践

### 1. 性能优化
- 使用 `useMemo` 缓存列定义
- 使用 `useCallback` 缓存事件处理函数
- 避免在渲染过程中创建新对象

### 2. 状态管理
- 将表格状态提升到父组件
- 使用自定义 Hook 管理复杂状态
- 考虑使用状态管理库（如 Zustand）

### 3. 数据处理
- 在服务端进行分页和筛选（大数据量）
- 使用防抖处理搜索输入
- 缓存筛选结果

### 4. 用户体验
- 提供加载状态
- 显示筛选条件数量
- 支持键盘导航
- 提供清除筛选功能

## 示例页面

项目中包含以下示例页面：

1. **TanStackTableTest** (`/pages/TanStackTableTest`) - 基础功能测试
2. **TanStackTableDemo** (`/pages/TanStackTableDemo`) - 完整功能演示  
3. **UserManagementRefactored** (`/pages/UserManagementRefactored`) - 实际业务场景

## 故障排除

### 常见问题

1. **表格高度不正确**
   - 检查容器是否有固定高度
   - 确认 DOM 测量是否完成
   - 查看控制台的测量日志

2. **筛选不生效**
   - 检查 `filterFns` 是否正确注册
   - 确认列定义中的 `filterFn` 属性
   - 查看 `columnFilters` 状态

3. **分页异常**
   - 检查 `pagination` 状态
   - 确认 `onPageChange` 事件处理
   - 查看数据总数计算

4. **样式问题**
   - 检查 CSS 导入
   - 确认样式优先级
   - 查看响应式断点

### 调试技巧

1. 开启控制台日志查看组件状态
2. 使用 React DevTools 检查 props 和 state
3. 检查 DOM 结构和样式计算
4. 使用浏览器开发工具测试响应式

## 更新日志

### v1.2.0
- ✨ 新增分页高度配置功能
  - 在 SIZE_PRESETS 中为每个预设尺寸添加 `paginationHeight` 字段
  - 新增 `calculateTotalHeight` 函数，支持计算包含分页组件的总高度
  - 在 TableConfig 接口中添加 `paginationHeight` 配置选项
  - 更新示例和文档，展示分页高度配置的使用方法
- 🔧 改进类型定义
  - 扩展 SizePresetConfig 接口，包含分页高度信息
  - 更新 useTableConfig hook，支持分页高度配置

### v1.1.0
- ✨ 新增尺寸预设功能
  - 添加 compact、standard、comfortable 三种预设尺寸
  - 支持通过 `size` 属性快速配置表格尺寸
  - 提供 `getSizePreset` 和 `getAvailableSizes` 工具函数
  - 保持向后兼容性，现有代码无需修改
- 🐛 修复高度计算逻辑
  - 所有分页大小现在都正确包含表头高度
  - 改进 DOM 测量的准确性
- 📚 完善文档和示例

### v1.0.0
- 初始版本发布
- 支持基础表格功能
- 实现自适应高度
- 添加 DOM 测量功能
- 支持筛选和排序
- 完善 TypeScript 类型定义

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个组件。

## 许可证

MIT License