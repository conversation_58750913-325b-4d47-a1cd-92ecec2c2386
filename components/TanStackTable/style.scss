// TanStackTable 组件样式
@import '../../styles/variables.scss';

.tanstack-table {
  background: var(--theme-bg-primary);
  border: 1px solid var(--theme-border-color-split);
  border-radius: $border-radius-base;
  overflow: hidden;
  transition: background-color 0.2s ease, border-color 0.2s ease;

  // 表格容器
  .table-container {
    position: relative;
    overflow: hidden;

    .table-wrapper {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      -webkit-overflow-scrolling: touch;
    }

    // 表格滚动容器 - 表头和表体同步滚动
    .table-scroll-container {
      flex: 1;
      overflow-x: auto;
      overflow-y: auto;
      position: relative;
      -webkit-overflow-scrolling: touch;

      // 优化滚动性能，提高垂直滚动响应性
      scroll-behavior: auto; // 使用浏览器默认滚动行为，提高响应性
      will-change: scroll-position; // 提示浏览器优化滚动性能

      // 移除可能影响滚动性能的属性
      // overscroll-behavior: contain; // 移除过度滚动控制
      // scroll-snap-type: none; // 移除滚动捕捉

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-track {
        background: var(--theme-bg-secondary);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: var(--theme-text-tertiary);
        border-radius: 3px;
        transition: background-color 0.2s ease;

        &:hover {
          background: var(--theme-text-secondary);
        }
      }

      &::-webkit-scrollbar-corner {
        background: var(--theme-bg-secondary);
      }

      // 动态垂直滚动条控制
      &.no-vertical-scroll {
        overflow-y: hidden;
      }

      &.has-vertical-scroll {
        overflow-y: auto;
      }

      .data-table {
        margin-bottom: 0;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        min-width: 100%; // 确保表格至少占满容器
        font-size: 14px;
        table-layout: auto; // 使用自动布局，允许表格超出容器宽度

        // 表头样式 - sticky 定位
        thead th {
          height: 41px;
          background: var(--theme-table-header-bg, var(--theme-bg-primary));
          border-bottom: 1px solid var(--theme-border-color-split);
          padding: 0 16px;
          text-align: left;
          font-weight: 600;
          color: var(--theme-text-primary);
          position: sticky;
          top: 0;
          z-index: 1;
          user-select: none;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;

          &.sortable {
            cursor: pointer;

            &:hover {
              background: var(--theme-table-row-hover, var(--theme-bg-secondary));
            }
          }

          .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 100%;
          }

          .sort-indicator {
            margin-left: 4px;
            color: var(--theme-text-secondary);
            transition: color 0.2s ease;
            font-size: 12px;

            &.active {
              color: $primary-color;
            }
          }
        }

        // 表体样式
        tbody {
          tr {
            height: 49px;
            transition: background-color 0.2s ease;
            cursor: pointer;

            &:hover {
              background: var(--theme-table-row-hover, var(--theme-bg-secondary)) !important;
            }

            &.even {
              background: transparent;
            }

            &.odd {
              background: var(--theme-table-row-stripe, var(--theme-bg-tertiary));
            }
          }

          td {
            padding: 0 16px;
            vertical-align: middle;
            color: var(--theme-text-primary);
            border-bottom: 1px solid var(--theme-border-color-split);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: color 0.2s ease, border-color 0.2s ease;

              &.empty-row {
                text-align: center;
                height: 280px;
                border-bottom: none;
                cursor: default;

                .empty-content {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  height: 100%;
                  width: 100%;

                  p {
                    margin: 0;
                    font-size: 14px;
                  }
                }
              }
            }
          }

          // 确保表格内容不会被滚动条遮挡
          tr:last-child td {
            border-bottom: none;
          }
        }
      }
    }
  }

  // 分页容器
  .pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    border-top: 1px solid var(--theme-border-color-split);
    background: var(--theme-bg-primary);
    box-sizing: border-box;
    transition: background-color 0.2s ease, border-color 0.2s ease;

    // 当设置了高度时，确保样式生效
    &[style*="height"] {
      // 使用 flex 布局但固定高度
      flex-shrink: 0;

      // 确保内容垂直居中
      .pagination-info,
      .pagination-controls {
        display: flex;
        align-items: center;
      }
    }

    .pagination-info {
      color: var(--theme-text-secondary);
      font-size: 14px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: color 0.2s ease;

      .record-range {
        color: var(--theme-text-primary);
        font-weight: normal;
        transition: color 0.2s ease;
      }

      .filter-info {
        color: $primary-color;
        font-size: 13px;
      }
    }

    .pagination-controls {
      display: flex;
      align-items: center;
      gap: 8px;

      .page-info {
        color: var(--theme-text-secondary);
        font-size: 14px;
        margin: 0 8px;
        white-space: nowrap;
        transition: color 0.2s ease;
      }

      .ant-btn {
        min-width: 64px;
      }

      .ant-select {
        .ant-select-selector {
          border-radius: 4px;
        }
      }
    }
  }


// 响应式设计
@media (max-width: 768px) {
  .tanstack-table {
    .table-container {
      .table-scroll-container {
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none;
        }

        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;
      }

      .data-table {
        // min-width 由组件动态设置，不在 CSS 中硬编码

        thead th {
          padding: 0 12px;
          font-size: 13px;
        }

        tbody td {
          padding: 0 12px;
          font-size: 13px;
        }
      }
    }

    .pagination-container {
      flex-direction: column;
      gap: 12px;
      text-align: center;

      .pagination-info {
        order: 2;
        font-size: 12px;
      }

      .pagination-controls {
        order: 1;
        justify-content: center;

        .page-info {
          font-size: 12px;
        }
      }
    }
  }
}