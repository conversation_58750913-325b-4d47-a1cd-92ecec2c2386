import React from 'react';
import { motion } from 'framer-motion';

interface FramerPageAnimationProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
}

/**
 * 统一的页面动画组件 - 基于 Framer Motion
 * 
 * 特性：
 * - 简单稳定的页面进入动画
 * - 优秀的浏览器兼容性（包括 Safari）
 * - 硬件加速优化
 * - 无路由监听，避免动画冲突
 * - 性能优化，避免不必要的重渲染
 */
const FramerPageAnimation: React.FC<FramerPageAnimationProps> = ({
  children,
  className = '',
  delay = 0
}) => {
  // 页面进入动画配置
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 30,
    },
    animate: {
      opacity: 1,
      y: 0,
    },
    exit: {
      opacity: 0,
      y: -30,
    }
  };

  // 动画过渡配置
  const pageTransition = {
    type: "tween" as const,
    ease: "easeOut" as const,
    duration: 0.6,
    delay: delay,
  };

  return (
    <motion.div
      className={`framer-page-animation ${className}`}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
      transition={pageTransition}
      style={{
        // 硬件加速优化
        willChange: 'transform, opacity',
        // 确保在动画期间保持层叠上下文
        isolation: 'isolate',
      }}
    >
      {children}
    </motion.div>
  );
};

export default FramerPageAnimation;
