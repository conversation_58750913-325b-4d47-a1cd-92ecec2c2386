// Framer Motion 页面动画样式
.framer-page-animation {
  // 基础样式
  width: 100%;
  height: 100%;
  
  // 硬件加速优化
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform, opacity;
  
  // 确保动画流畅性
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
  
  // 防止动画期间的布局抖动
  contain: layout style paint;
  
  // Safari 特定优化
  @supports (-webkit-appearance: none) {
    // Safari 特定的渲染优化
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    
    // 强制硬件加速
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
}

// 表格固定高度样式（从原 PageLoadingAnimation 迁移）
.table-fixed-height {
  // 确保表格容器有稳定的布局
  position: relative;
  overflow: hidden;

  .ant-table-wrapper {
    // 防止表格内容溢出
    overflow: hidden;
  }

  .ant-table-body {
    min-height: var(--scroll-height);
    // 确保滚动条行为一致
    overflow-y: auto;
    overflow-x: hidden;
  }

  // 带边框表格的边框修复
  .ant-table-bordered {
    .ant-table-body {
      border-inline-end: 1px solid #d9d9d9;
      border-block-end: 1px solid #d9d9d9;
    }
  }

  // 确保分页器位置稳定
  .ant-table-pagination {
    // 分页器固定在底部
    position: relative;
    z-index: 1;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    margin: 0;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .framer-page-animation {
    // 移动端减少动画复杂度
    will-change: opacity;
  }
}

// 减少动画偏好的用户
@media (prefers-reduced-motion: reduce) {
  .framer-page-animation {
    // 禁用动画，直接显示内容
    transform: none !important;
    transition: none !important;
    animation: none !important;
  }
}
