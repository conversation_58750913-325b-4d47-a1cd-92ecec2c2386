import React from 'react';
import { useLocation } from 'react-router-dom';
import { Breadcrumb } from 'antd';
import { BreadcrumbConfig, RouteConfig } from '../../types';
import { generateBreadcrumbs } from '../../utils/routeUtils';
import routeConfig from '../../config/routes';
import './style.scss';

interface DynamicBreadcrumbProps {
  routes?: RouteConfig[];
  config?: BreadcrumbConfig;
  className?: string;
  style?: React.CSSProperties;
}

/**
 * 动态面包屑导航组件
 * 根据当前路由自动生成面包屑路径
 */
const DynamicBreadcrumb: React.FC<DynamicBreadcrumbProps> = ({
  routes = routeConfig,
  config = {},
  className = '',
  style = {},
}) => {
  const location = useLocation();
  
  const {
    showHome = true,
    homeTitle = '首页',
    homePath = '/dashboard',
    showIcon = true,
    maxItems = 5,
  } = config;

  // 生成面包屑数据
  const breadcrumbItems = React.useMemo(() => {
    try {
      return generateBreadcrumbs(routes, location.pathname, {
        showHome,
        homeTitle,
        homePath,
      });
    } catch (error) {
      console.warn('Failed to generate breadcrumbs:', error);
      return [];
    }
  }, [routes, location.pathname, showHome, homeTitle, homePath]);

  // 如果没有面包屑项，不渲染
  if (breadcrumbItems.length === 0) {
    return null;
  }

  // 限制面包屑项数量
  const limitedItems = maxItems && breadcrumbItems.length > maxItems
    ? [
        breadcrumbItems[0], // 保留首页
        { title: '...', key: 'ellipsis' }, // 省略号
        ...breadcrumbItems.slice(-2) // 保留最后两项
      ]
    : breadcrumbItems;

  // 转换为Ant Design Breadcrumb所需的格式
  const antdBreadcrumbItems = limitedItems.map((item, index) => {
    const isLast = index === limitedItems.length - 1;
    const isEllipsis = item.key === 'ellipsis';
    
    if (isEllipsis) {
      return {
        key: item.key,
        title: <span className="breadcrumb-ellipsis">...</span>,
      };
    }

    return {
      key: item.key || index,
      title: (
        <span className="breadcrumb-item">
          {/* 标题 - 移除图标和跳转功能 */}
          <span className="breadcrumb-text">{item.title}</span>
        </span>
      ),
    };
  });

  return (
    <div className={`dynamic-breadcrumb ${className}`} style={style}>
      <Breadcrumb
        items={antdBreadcrumbItems}
        className="breadcrumb-nav"
      />
    </div>
  );
};

export default DynamicBreadcrumb;
