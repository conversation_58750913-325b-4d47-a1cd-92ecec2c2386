import React, { useState, useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Switch,
  message,
  Typography,
  Card,
  Row,
  Col,
} from "antd";
import {
  InfoCircleOutlined,
} from "@ant-design/icons";
import { apiSourceAPI } from "../../services/api";
import { handleApiResponse } from "../../utils/apiResponseHandler";
// 临时 API 源类型定义
enum ApiSourceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ERROR = 'error'
}

// enum ApiSourceType {
//   OPENAI = 'openai',
//   ANTHROPIC = 'anthropic',
//   GOOGLE = 'google',
//   AZURE = 'azure',
//   CUSTOM = 'custom'
// }

interface ApiSource {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: ApiSourceStatus;
  isActive?: boolean;
}
import "./style.scss";

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface ApiSourceFormProps {
  visible: boolean;
  onCancel: () => void;
  onSubmit: (values: any) => void;
  loading?: boolean;
  editingRecord?: ApiSource;
}

interface ApiTemplate {
  id: string;
  name: string;
  type: string;
  description: string;
  defaultConfig: any;
}

const ApiSourceForm: React.FC<ApiSourceFormProps> = ({
  visible,
  onCancel,
  onSubmit,
  loading = false,
  editingRecord,
}) => {
  const [form] = Form.useForm();
  const [templates, setTemplates] = useState<ApiTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<ApiTemplate | null>(null);

  // 获取API模板
  const fetchTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const response = await apiSourceAPI.getApiSourceTemplates();
      const result = handleApiResponse(response);
      
      if (result.success && result.data) {
        setTemplates(Array.isArray(result.data) ? result.data : []);
      } else {
        console.error("获取模板失败:", result.error);
        message.error("获取API模板失败");
      }
    } catch (error) {
      console.error("获取模板失败:", error);
      message.error("获取API模板失败");
    } finally {
      setLoadingTemplates(false);
    }
  };

  // 初始化表单
  useEffect(() => {
    if (visible) {
      fetchTemplates();
      
      if (editingRecord) {
        // 编辑模式
        form.setFieldsValue({
          ...editingRecord,
          status: editingRecord.status === 'active',
        });
      } else {
        // 创建模式
        form.setFieldsValue({
          status: true,
        });
      }
    }
  }, [visible, editingRecord, form]);

  // 模板选择处理
  const handleTemplateChange = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      form.setFieldsValue({
        type: template.type,
        name: template.name,
        description: template.description,
      });
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      const submitData = {
        ...values,
        status: values.status ? 'active' : 'inactive',
        config: selectedTemplate?.defaultConfig || {},
      };

      await onSubmit(submitData);
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  // 取消操作
  const handleCancel = () => {
    form.resetFields();
    setSelectedTemplate(null);
    onCancel();
  };

  return (
    <Modal
      title={editingRecord ? "编辑API源" : "创建API源"}
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading}
          onClick={handleSubmit}
        >
          {editingRecord ? "更新" : "创建"}
        </Button>,
      ]}
      width={600}
      className="api-source-form"
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          status: true,
        }}
      >
        {/* 基本信息 */}
        <Card title="基本信息" size="small" className="form-section">
          {!editingRecord && (
            <Form.Item label="选择模板">
              <Select
                placeholder="选择API源模板（可选）"
                allowClear
                loading={loadingTemplates}
                onChange={handleTemplateChange}
              >
                {templates.map(template => (
                  <Option key={template.id} value={template.id}>
                    {template.name} - {template.description}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          )}

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="名称"
                name="name"
                rules={[{ required: true, message: "请输入API源名称" }]}
              >
                <Input placeholder="API源的显示名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="类型"
                name="type"
                rules={[{ required: true, message: "请选择API源类型" }]}
              >
                <Select placeholder="选择API源类型">
                  <Option value="openai">OpenAI</Option>
                  <Option value="claude">Claude</Option>
                  <Option value="qianwen">千问</Option>
                  <Option value="gemini">Gemini</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="API地址"
            name="apiUrl"
            rules={[
              { required: true, message: "请输入API地址" },
              { type: 'url', message: "请输入有效的URL地址" }
            ]}
          >
            <Input placeholder="https://api.example.com" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="状态"
                name="status"
                valuePropName="checked"
              >
                <Switch
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item label="描述" name="description">
            <TextArea
              placeholder="API源的详细描述"
              rows={3}
              maxLength={500}
              showCount
            />
          </Form.Item>
        </Card>

        {/* 模板信息显示 */}
        {selectedTemplate && (
          <Card title="模板信息" size="small" className="form-section template-info">
            <div className="template-detail">
              <Text type="secondary">
                <InfoCircleOutlined /> 当前选择的模板：{selectedTemplate.name}
              </Text>
              <p>{selectedTemplate.description}</p>
            </div>
          </Card>
        )}
      </Form>
    </Modal>
  );
};

export default ApiSourceForm; 