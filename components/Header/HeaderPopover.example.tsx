import React, { useState, useRef } from 'react';
import { Button, Space } from 'antd';
import { BellOutlined, QuestionCircleOutlined, UserOutlined, SettingOutlined } from '@ant-design/icons';
import HeaderPopover from './HeaderPopover';

/**
 * HeaderPopover使用示例
 * 展示如何在Header组件中使用HeaderPopover组件
 */
const HeaderPopoverExample: React.FC = () => {
  // 状态管理
  const [clickPopoverVisible, setClickPopoverVisible] = useState(false);
  const [hoverPopoverVisible, setHoverPopoverVisible] = useState(false);
  const [customPopoverVisible, setCustomPopoverVisible] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState({ top: 0, left: 0 });

  // 按钮引用
  const clickButtonRef = useRef<HTMLButtonElement>(null);
  const hoverButtonRef = useRef<HTMLButtonElement>(null);
  const customButtonRef = useRef<HTMLButtonElement>(null);

  // 处理点击触发的弹出框
  const handleClickTrigger = () => {
    if (clickButtonRef.current) {
      const rect = clickButtonRef.current.getBoundingClientRect();
      setPopoverPosition({
        top: rect.bottom + 8,
        left: rect.left - 100,
      });
      setClickPopoverVisible(!clickPopoverVisible);
    }
  };

  // 处理悬浮触发的弹出框
  const handleHoverEnter = () => {
    if (hoverButtonRef.current) {
      const rect = hoverButtonRef.current.getBoundingClientRect();
      setPopoverPosition({
        top: rect.bottom + 8,
        left: rect.left - 100,
      });
      setHoverPopoverVisible(true);
    }
  };

  const handleHoverLeave = () => {
    // 延迟关闭，给用户时间移动到弹出框
    setTimeout(() => {
      setHoverPopoverVisible(false);
    }, 100);
  };

  // 处理自定义内容弹出框
  const handleCustomTrigger = () => {
    if (customButtonRef.current) {
      const rect = customButtonRef.current.getBoundingClientRect();
      setPopoverPosition({
        top: rect.bottom + 8,
        left: rect.left - 150,
      });
      setCustomPopoverVisible(!customPopoverVisible);
    }
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>HeaderPopover 使用示例</h2>
      
      <Space size="large" style={{ marginBottom: '20px' }}>
        {/* 点击触发示例 */}
        <Button
          ref={clickButtonRef}
          type="primary"
          icon={<BellOutlined />}
          onClick={handleClickTrigger}
        >
          点击触发
        </Button>

        {/* 悬浮触发示例 */}
        <Button
          ref={hoverButtonRef}
          icon={<QuestionCircleOutlined />}
          onMouseEnter={handleHoverEnter}
          onMouseLeave={handleHoverLeave}
        >
          悬浮触发
        </Button>

        {/* 自定义内容示例 */}
        <Button
          ref={customButtonRef}
          icon={<UserOutlined />}
          onClick={handleCustomTrigger}
        >
          自定义内容
        </Button>
      </Space>

      {/* 点击触发的弹出框 */}
      <HeaderPopover
        visible={clickPopoverVisible}
        onClose={() => setClickPopoverVisible(false)}
        position={popoverPosition}
        theme="light"
        trigger="click"
        width={200}
        placement="bottom"
      >
        <div className="header-popover-custom">
          <div className="custom-content">
            <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>通知消息</h4>
            <div className="header-popover-menu">
              <div className="header-menu-item">
                <BellOutlined />
                <span className="header-item-label">系统通知</span>
                <span className="header-item-badge">3</span>
              </div>
              <div className="header-menu-item">
                <UserOutlined />
                <span className="header-item-label">用户消息</span>
                <span className="header-item-badge">新</span>
              </div>
              <div className="header-popover-divider"></div>
              <div className="header-menu-item">
                <span className="header-item-label">查看全部</span>
              </div>
            </div>
          </div>
        </div>
      </HeaderPopover>

      {/* 悬浮触发的弹出框 */}
      <HeaderPopover
        visible={hoverPopoverVisible}
        onClose={() => setHoverPopoverVisible(false)}
        position={popoverPosition}
        theme="light"
        trigger="hover"
        width={180}
        placement="bottom"
      >
        <div className="header-popover-custom">
          <div className="custom-content">
            <h4 style={{ margin: '0 0 8px 0', fontSize: '14px' }}>帮助中心</h4>
            <div className="header-popover-menu">
              <div className="header-menu-item">
                <QuestionCircleOutlined />
                <span className="header-item-label">使用指南</span>
              </div>
              <div className="header-menu-item">
                <SettingOutlined />
                <span className="header-item-label">快捷键</span>
              </div>
              <div className="header-menu-item">
                <UserOutlined />
                <span className="header-item-label">联系客服</span>
              </div>
            </div>
          </div>
        </div>
      </HeaderPopover>

      {/* 自定义内容的弹出框 */}
      <HeaderPopover
        visible={customPopoverVisible}
        onClose={() => setCustomPopoverVisible(false)}
        position={popoverPosition}
        theme="light"
        trigger="click"
        width={250}
        placement="bottom"
      >
        <div style={{ padding: '12px' }}>
          <h4 style={{ margin: '0 0 12px 0', fontSize: '14px' }}>用户信息</h4>
          <div style={{ marginBottom: '8px' }}>
            <strong>用户名：</strong> admin
          </div>
          <div style={{ marginBottom: '8px' }}>
            <strong>角色：</strong> 管理员
          </div>
          <div style={{ marginBottom: '12px' }}>
            <strong>最后登录：</strong> 2024-01-01 10:00:00
          </div>
          <Button type="primary" size="small" block>
            查看详情
          </Button>
        </div>
      </HeaderPopover>

      <div style={{ marginTop: '40px' }}>
        <h3>使用说明</h3>
        <ul>
          <li><strong>点击触发：</strong> 点击按钮显示/隐藏弹出框，点击外部区域或按ESC键关闭</li>
          <li><strong>悬浮触发：</strong> 鼠标悬浮显示弹出框，鼠标离开后延迟关闭</li>
          <li><strong>自定义内容：</strong> 可以在弹出框中放置任意React组件</li>
          <li><strong>样式隔离：</strong> 使用独立的CSS类名，不会影响原侧边栏的SubMenuPopover</li>
          <li><strong>多方向支持：</strong> 支持right、left、bottom、top四个方向的弹出</li>
        </ul>
      </div>
    </div>
  );
};

export default HeaderPopoverExample;
