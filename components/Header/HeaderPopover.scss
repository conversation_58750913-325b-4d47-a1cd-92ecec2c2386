@use '../../styles/variables' as *;

// Header弹出组件样式 - 基于SubMenuPopover但使用独立的类名前缀
.header-popover {
  position: fixed !important;
  z-index: 9999 !important;
  background: $background-color-white;
  border-radius: $border-radius-base;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12), 0 3px 6px rgba(0, 0, 0, 0.08);
  border: 1px solid $border-color-split;
  animation: headerPopoverFadeIn 0.2s ease-out;
  display: block !important;
  overflow: visible; // 确保箭头可见

  // 暗色主题
  &.dark {
    background: $dark-component-background;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.2);

    .header-popover-arrow {
      &::before {
        background: $dark-component-background;
        border-color: rgba(255, 255, 255, 0.1);
        box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.2);
      }

      &::after {
        background: $dark-component-background;
      }
    }
  }

  // 箭头指示器 - 支持多个方向
  .header-popover-arrow {
    position: absolute;
    width: 12px;
    height: 12px;
    z-index: 1;

    // 使用伪元素创建箭头
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 12px;
      height: 12px;
      background: $sidebar-bg-light;
      border: 1px solid $border-color-split;
      border-right: none;
      border-bottom: none;
      box-shadow: -2px -2px 4px rgba(0, 0, 0, 0.05);
    }

    // 遮盖多余的边框
    &::after {
      content: '';
      position: absolute;
      left: 2px;
      top: 0;
      width: 10px;
      height: 12px;
      background: $sidebar-bg-light;
      z-index: 2;
    }
  }

  .header-popover-content {
    padding: 8px;
    overflow: hidden;
    position: relative;
    z-index: 2;
  }

  // 通用的菜单样式（如果内容是菜单）
  .header-popover-menu {
    border: none !important;
    background: transparent !important;
    padding: 4px 0;

    .header-menu-item {
      height: 36px;
      line-height: 36px;
      margin: 0;
      padding: 0 12px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;

      // 图标样式
      .anticon {
        font-size: 14px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .header-item-label {
        flex: 1;
        font-size: $font-size-sm;
      }

      .header-item-badge {
        background: $error-color;
        color: white;
        font-size: $font-size-xs;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
      }

      // 悬停状态
      &:hover {
        background: $background-color-light;
        color: $primary-color;
      }

      // 选中状态
      &.selected {
        background: $primary-color-light;
        color: $primary-color;
      }

      // 禁用状态
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        
        &:hover {
          background: transparent;
          color: inherit;
        }
      }
    }
  }

  // 暗色主题下的菜单项
  &.dark .header-popover-menu {
    .header-menu-item {
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        background: rgba(255, 255, 255, 0.08);
        color: $primary-color;
      }

      &.selected {
        background: rgba(24, 144, 255, 0.2);
        color: $primary-color;
      }
    }
  }

  // 分割线样式
  .header-popover-divider {
    height: 1px;
    background: $border-color-split;
    margin: 4px 0;
  }

  &.dark .header-popover-divider {
    background: rgba(255, 255, 255, 0.1);
  }

  // 自定义内容区域
  .header-popover-custom {
    padding: 0;
    
    .custom-content {
      padding: 12px;
      font-size: $font-size-sm;
      line-height: 1.5;
    }
  }
}

// 动画效果 - 独立的动画名称避免冲突
@keyframes headerPopoverFadeIn {
  from {
    opacity: 0;
    transform: translateX(-8px) scale(0.95);
    filter: blur(1px);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
    filter: blur(0);
  }
}

// 响应式适配
@media (max-width: $breakpoint-sm) {
  .header-popover {
    max-width: 280px;
    
    .header-popover-content {
      padding: 6px;
    }
    
    .header-popover-menu .header-menu-item {
      height: 32px;
      line-height: 32px;
      padding: 0 8px;
      
      .anticon {
        font-size: 12px;
        margin-right: 6px;
      }
      
      .header-item-label {
        font-size: $font-size-xs;
      }
    }
  }
}

// 特殊的弹出位置样式调整
.header-popover {
  // 右侧弹出（默认）
  &.placement-right .header-popover-arrow {
    left: -6px;
    top: 16px;
  }
  
  // 左侧弹出
  &.placement-left .header-popover-arrow {
    right: -6px;
    top: 16px;
  }
  
  // 底部弹出
  &.placement-bottom .header-popover-arrow {
    top: -6px;
    left: 16px;
  }
  
  // 顶部弹出
  &.placement-top .header-popover-arrow {
    bottom: -6px;
    left: 16px;
  }
}
