import React, { useEffect, useRef, ReactNode } from 'react';
import './HeaderPopover.scss';

interface HeaderPopoverProps {
  visible: boolean;
  onClose: () => void;
  position: { top: number; left: number };
  theme?: 'light' | 'dark';
  trigger?: 'click' | 'hover';
  children: ReactNode;
  className?: string;
  width?: number | string;
  placement?: 'right' | 'left' | 'bottom' | 'top';
}

/**
 * Header通用弹出组件 - 基于侧边栏SubMenuPopover复用
 * 支持点击或悬浮触发，可自定义内容
 */
const HeaderPopover: React.FC<HeaderPopoverProps> = ({
  visible,
  onClose,
  position,
  theme = 'light',
  trigger = 'click',
  children,
  className = '',
  width = 200,
  placement = 'right',
}) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (visible && trigger === 'click') {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscKey);
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
      }
    };
  }, [visible, onClose, trigger]);

  // 处理鼠标进入弹出菜单（仅在hover模式下）
  const handleMouseEnter = () => {
    if (trigger === 'hover' && closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = null;
    }
  };

  // 处理鼠标离开弹出菜单（仅在hover模式下）
  const handleMouseLeave = () => {
    if (trigger === 'hover') {
      closeTimeoutRef.current = setTimeout(() => {
        onClose();
      }, 200);
    }
  };

  // 计算箭头位置和弹出框位置
  const getArrowPosition = () => {
    switch (placement) {
      case 'right':
        return { left: -6, top: 16 };
      case 'left':
        return { right: -6, top: 16 };
      case 'bottom':
        return { top: -6, left: 16 };
      case 'top':
        return { bottom: -6, left: 16 };
      default:
        return { left: -6, top: 16 };
    }
  };

  // 计算箭头旋转角度
  const getArrowRotation = () => {
    switch (placement) {
      case 'right':
        return 'rotate(-45deg)';
      case 'left':
        return 'rotate(135deg)';
      case 'bottom':
        return 'rotate(45deg)';
      case 'top':
        return 'rotate(135deg)'; // 修复top placement的箭头方向，指向下方
      default:
        return 'rotate(-45deg)';
    }
  };

  if (!visible) {
    return null;
  }

  const arrowPosition = getArrowPosition();
  const arrowRotation = getArrowRotation();

  return (
    <div
      ref={popoverRef}
      className={`header-popover ${theme} ${className}`}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 9999,
        backgroundColor: theme === 'dark' ? '#1f1f1f' : '#ffffff',
        border: '1px solid #d9d9d9',
        borderRadius: '6px',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
        width: typeof width === 'number' ? `${width}px` : width,
        minWidth: '120px',
        maxWidth: '320px',
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* 箭头指示器 */}
      <div 
        className="header-popover-arrow"
        style={{
          ...arrowPosition,
          transform: arrowRotation,
        }}
      ></div>

      <div className="header-popover-content">
        {children}
      </div>
    </div>
  );
};

export default HeaderPopover;
