@use '../../styles/variables' as *;

/**
 * 助手表单模态框样式
 */
.assistant-form-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .form-section {
    margin-bottom: 16px;

    .section-title {
      font-size: $font-size-lg;
      font-weight: $font-weight-medium;
      color: var(--theme-text-primary);
      margin-bottom: $spacing-md;
      padding-bottom: $spacing-md;
      border-bottom: 1px solid var(--theme-border-color-split);
      transition: color 0.2s ease, border-color 0.2s ease;
    }
  }

  .ant-form-item {
    margin-bottom: 16px;

    .ant-form-item-label {
      font-weight: $font-weight-medium;
    }
  }

  .tag-input-container {
    .ant-tag {
      margin-bottom: $spacing-md;
    }
  }

  .knowledge-base-selector {
    .ant-select {
      width: 100%;
    }
  }

  .prompt-textarea {
    .ant-input {
      min-height: 120px;
      resize: vertical;
    }
  }
}
