import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>u, ConfigProvider, Popover, Drawer, Toolt<PERSON>, But<PERSON> } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { useResponsive } from '../../hooks/useResponsiveListener';
import { SidebarProps, MenuItem } from '../../types';
import { getMenuStateByPath } from '../../utils/menuData';
import './style_new.scss';
import { GithubOutlined, ReloadOutlined } from '@ant-design/icons';
// import SubMenuPopover from "./SubMenuPopover"; // 未使用

// const { Sider } = Layout; // 未使用

/**
 * 侧边栏组件 - 基于demo项目重构
 * 简化设计，专注核心功能：展开/折叠、子菜单、响应式
 */
const Sidebar: React.FC<SidebarProps> = ({
  collapsed = false,
  onCollapse,
  menuItems = [],
  selectedKey,
  onMenuSelect,
  theme = 'light',
  width = 240,
  collapsedWidth = 64,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isMobile } = useResponsive();

  // 菜单状态
  const [openKeys, setOpenKeys] = useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  // 根据当前路径更新菜单状态
  useEffect(() => {
    const menuState = getMenuStateByPath(location.pathname);
    setSelectedKeys(selectedKey ? [selectedKey] : menuState.selectedKeys);
    
    // 只在非折叠状态下设置展开的keys
    if (!collapsed) {
      setOpenKeys(menuState.openKeys);
    } else {
      setOpenKeys([]);
    }
  }, [location.pathname, selectedKey, collapsed]);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = findMenuItem(menuItems, key);
    
    if (menuItem?.path) {
      navigate(menuItem.path);
      onMenuSelect?.(key, menuItem.path);
    }
  };

  // 处理子菜单展开/收起
  const handleOpenChange = (keys: string[]) => {
    // 在折叠状态下不允许展开子菜单
    if (collapsed && !isMobile) {
      setOpenKeys([]);
      return;
    }

    // 展开状态下允许正常的子菜单展开/收缩
    setOpenKeys(keys);
  };



  // 查找菜单项的辅助函数
  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) {
        return item;
      }
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  // 渲染子菜单内容
  const renderSubMenuContent = (children: MenuItem[]) => (
    <Menu
      mode="vertical"
      theme={theme}
      className="submenu-popover-menu"
      onClick={({ key }) => {
        const clickedItem = findMenuItem(children, key);
        if (clickedItem?.path) {
          navigate(clickedItem.path);
          onMenuSelect?.(key, clickedItem.path);
        }
      }}
      style={{
        border: 'none',
        background: 'transparent',
        padding: '4px 0',
        minWidth: '160px',
      }}
    >
      {children.map(child => (
        <Menu.Item key={child.key} icon={child.icon}>
          {child.label}
        </Menu.Item>
      ))}
    </Menu>
  );

  // 转换菜单项为Ant Design Menu格式 - 使用Popover方案
  const convertMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => {
      // 如果有子菜单且在折叠状态下，使用Popover包装
      if (item.children && collapsed && !isMobile) {
        return {
          key: item.key,
          icon: item.icon,
          label: (
            <Popover
              content={renderSubMenuContent(item.children)}
              trigger="hover"
              placement="rightTop"
              overlayClassName={`custom-sidebar-popover ${theme}`}
              destroyTooltipOnHide
              mouseEnterDelay={0.1}
              mouseLeaveDelay={0.2}
            >
              <span style={{ display: 'block', width: '100%' }}>
                {item.label}
              </span>
            </Popover>
          ),
        };
      }

      // 普通菜单项或展开状态下的子菜单
      return {
        key: item.key,
        icon: item.icon,
        label: item.label,
        children: item.children && !collapsed ? convertMenuItems(item.children) : undefined,
      };
    });
  };

  // 转换后的菜单项
  const antdMenuItems = useMemo(() => convertMenuItems(menuItems), [menuItems]);

  // 侧边栏内容
  const sidebarContent = (
    <div className={`v2-sidebar ${theme} ${collapsed ? 'collapsed hide-text' : ''}`}>
      {/* Logo区域 */}
      <div className="sidebar-logo">
        <div className="logo-content">
          {collapsed ? (
            <span className="logo-icon">A</span>
          ) : (
            <>
              <span className="logo-icon">A</span>
              <span className="logo-text">Admin V2</span>
            </>
          )}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="sidebar-menu">
        <ConfigProvider
          theme={{
            components: {
              Menu: {
                iconSize: 18,
                itemBorderRadius: 6,
                itemMarginBlock: 2,
                itemMarginInline: 8,
                collapsedIconSize: 18,
                itemHeight: 40,
                iconMarginInlineEnd: 12,
                groupTitleFontSize: 12,
                subMenuItemBorderRadius: 6,
              },
            },
          }}
        >
          <Menu
            mode="inline"
            theme={theme}
            selectedKeys={selectedKeys}
            openKeys={collapsed ? [] : openKeys}
            onOpenChange={handleOpenChange}
            onClick={handleMenuClick}
            inlineCollapsed={collapsed}
            className="sidebar-menu-component"
            style={{
              width: collapsed ? collapsedWidth : width,
              borderRight: 'none',
            }}
            items={antdMenuItems}
          />
        </ConfigProvider>
      </div>

      {/* 底部区域 */}
      <div className="sidebar-footer">
        {collapsed ? (
          <Tooltip title="GitHub">
            <a
              href="#" // TODO: 后续替换为真实GitHub地址
              target="_blank"
              rel="noopener noreferrer"
              style={{ fontSize: 20 }}
            >
              <GithubOutlined />
            </a>
          </Tooltip>
        ) : (
          <div className="footer-content" style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
            <Tooltip title="GitHub">
              <a
                href="#" // TODO: 后续替换为真实GitHub地址
                target="_blank"
                rel="noopener noreferrer"
                style={{ fontSize: 20, marginRight: 16 }}
              >
                <GithubOutlined />
              </a>
            </Tooltip>
            <Button
              icon={<ReloadOutlined />}
              size="small"
              type="link"
              style={{ padding: 0, marginRight: 16 }}
              onClick={() => window.location.reload()}
            >
              更新
            </Button>
            <span className="version-info" style={{ marginLeft: 'auto' }}>V2.0.0</span>
          </div>
        )}
      </div>
    </div>
  );

  // 移动端使用Drawer
  if (isMobile) {
    return (
      <Drawer
        placement="left"
        closable={false}
        open={!collapsed}
        onClose={() => onCollapse?.(true)}
        bodyStyle={{ padding: 0 }}
        width={width}
        className="v2-sidebar-drawer"
      >
        {sidebarContent}
      </Drawer>
    );
  }

  // 桌面端固定侧边栏
  return (
    <div
      className={`v2-sidebar-wrapper ${collapsed ? 'collapsed' : 'expanded'}`}
      style={{
        width: `${collapsed ? collapsedWidth : width}px`,
        minWidth: `${collapsed ? collapsedWidth : width}px`,
        maxWidth: `${collapsed ? collapsedWidth : width}px`,
      }}
    >
      {sidebarContent}
    </div>
  );
};

export default Sidebar;
