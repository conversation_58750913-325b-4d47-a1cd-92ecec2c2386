import React from 'react';
import { Card, Button, Input, Select, Table, Tag, Space, Divider } from 'antd';
import { 
  UserOutlined, 
  SettingOutlined, 
  BellOutlined,
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined 
} from '@ant-design/icons';
import ThemeToggle from '../ThemeToggle';
import { useThemeStore } from '../../stores';

const { Option } = Select;

// 示例数据
const demoData = [
  {
    key: '1',
    name: '张三',
    age: 32,
    address: '北京市朝阳区',
    status: 'active',
    tags: ['开发者', 'React'],
  },
  {
    key: '2',
    name: '李四',
    age: 28,
    address: '上海市浦东新区',
    status: 'inactive',
    tags: ['设计师', 'UI/UX'],
  },
  {
    key: '3',
    name: '王五',
    age: 35,
    address: '深圳市南山区',
    status: 'active',
    tags: ['产品经理', '敏捷'],
  },
];

const columns = [
  {
    title: '姓名',
    dataIndex: 'name',
    key: 'name',
    render: (text: string) => <a>{text}</a>,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    key: 'age',
  },
  {
    title: '地址',
    dataIndex: 'address',
    key: 'address',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    render: (status: string) => (
      <Tag color={status === 'active' ? 'green' : 'red'}>
        {status === 'active' ? '活跃' : '非活跃'}
      </Tag>
    ),
  },
  {
    title: '标签',
    key: 'tags',
    dataIndex: 'tags',
    render: (tags: string[]) => (
      <>
        {tags.map((tag) => (
          <Tag color="blue" key={tag}>
            {tag}
          </Tag>
        ))}
      </>
    ),
  },
  {
    title: '操作',
    key: 'action',
    render: () => (
      <Space size="middle">
        <Button type="link" icon={<EditOutlined />}>
          编辑
        </Button>
        <Button type="link" danger icon={<DeleteOutlined />}>
          删除
        </Button>
      </Space>
    ),
  },
];

const ThemeDemo: React.FC = () => {
  const { mode, actualTheme } = useThemeStore();

  return (
    <div style={{ padding: '24px', background: 'var(--bg-secondary)', minHeight: '100vh' }}>
      <Card title="主题切换演示" style={{ marginBottom: '24px' }}>
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          {/* 主题信息显示 */}
          <div>
            <h3>当前主题信息</h3>
            <p>主题模式: <Tag color="blue">{mode}</Tag></p>
            <p>实际主题: <Tag color={actualTheme === 'dark' ? 'purple' : 'orange'}>{actualTheme}</Tag></p>
          </div>

          <Divider />

          {/* 主题切换控件 */}
          <div>
            <h3>主题切换控件</h3>
            <Space>
              <span>下拉菜单模式:</span>
              <ThemeToggle mode="dropdown" />
              <span>按钮模式:</span>
              <ThemeToggle mode="button" />
              <span>带文本:</span>
              <ThemeToggle mode="dropdown" showText />
            </Space>
          </div>

          <Divider />

          {/* 表单组件演示 */}
          <div>
            <h3>表单组件</h3>
            <Space wrap>
              <Input 
                placeholder="请输入内容" 
                prefix={<UserOutlined />}
                style={{ width: 200 }}
              />
              <Select defaultValue="option1" style={{ width: 120 }}>
                <Option value="option1">选项1</Option>
                <Option value="option2">选项2</Option>
                <Option value="option3">选项3</Option>
              </Select>
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button icon={<PlusOutlined />}>
                添加
              </Button>
              <Button type="dashed" icon={<SettingOutlined />}>
                设置
              </Button>
            </Space>
          </div>

          <Divider />

          {/* 按钮组件演示 */}
          <div>
            <h3>按钮组件</h3>
            <Space wrap>
              <Button type="primary">主要按钮</Button>
              <Button>默认按钮</Button>
              <Button type="dashed">虚线按钮</Button>
              <Button type="text">文本按钮</Button>
              <Button type="link">链接按钮</Button>
              <Button danger>危险按钮</Button>
              <Button type="primary" danger>
                主要危险按钮
              </Button>
            </Space>
          </div>
        </Space>
      </Card>

      {/* 表格组件演示 */}
      <Card title="表格组件演示">
        <Table 
          columns={columns} 
          dataSource={demoData} 
          pagination={{ pageSize: 5 }}
        />
      </Card>
    </div>
  );
};

export default ThemeDemo;
