import { PlusOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Button, Input } from 'antd';
import { useRef } from 'react';
import '../PageLoadingAnimation/style.scss';
// import './style.scss'; // 已清除自定义样式，使用默认样式

export interface LogsProTableProps<T = any> {
  /** 表格标题 */
  headerTitle: string;
  /** 表格列配置 */
  columns: ProColumns<T>[];
  /** 数据请求函数 */
  request: (params: any, sort: any, filter: any) => Promise<{ data: T[]; success?: boolean; total?: number }>;
  /** 搜索配置 */
  search?: {
    labelWidth?: number | 'auto';
    placeholder?: string;
  };
  /** 分页配置 */
  pagination?: {
    pageSize?: number;
    showQuickJumper?: boolean;
    showSizeChanger?: boolean;
  };
  /** 工具栏按钮配置 */
  toolBarConfig?: {
    showRefresh?: boolean;
    showExport?: boolean;
    showAdd?: boolean;
    addButtonText?: string;
    extraButtons?: React.ReactNode[];
  };
  /** 表格配置 */
  tableConfig?: {
    size?: 'small' | 'middle' | 'large';
    bordered?: boolean;
    scroll?: { x?: number; y?: number | string };
    autoHeight?: boolean; // 是否自动计算高度
    maxHeight?: number; // 最大高度限制
  };
  /** 自定义操作 */
  onRefresh?: () => void;
  onExport?: () => void;
  onAdd?: () => void;
  /** 行选择配置 */
  rowSelection?: any;
  /** 可编辑配置 */
  editable?: any;
}

const LogsProTable = <T extends Record<string, any>>({
  headerTitle,
  columns,
  request,
  search = {
    labelWidth: 'auto',
  },
  pagination = {
    pageSize: 10,
    showQuickJumper: true,
    showSizeChanger: true,
  },
  toolBarConfig = {
    showRefresh: true,
    showExport: true,
    showAdd: false,
  },
  tableConfig = {
    size: 'small',
    bordered: false,
    scroll: { x: 1000 },
    autoHeight: true,
    maxHeight: 500,
  },
  onRefresh,
  onExport,
  onAdd,
  rowSelection,
  editable,
}: LogsProTableProps<T>) => {
  const actionRef = useRef<ActionType>();

  // 默认刷新操作
  const handleRefresh = () => {
    actionRef.current?.reload();
    onRefresh?.();
  };

  // 默认导出操作
  const handleExport = () => {
    console.log('导出数据');
    onExport?.();
  };

  // 默认新增操作
  const handleAdd = () => {
    console.log('新增数据');
    onAdd?.();
  };

  // 构建工具栏按钮
  const buildToolBarRender = () => {
    const buttons: React.ReactNode[] = [];

    // 刷新按钮
    if (toolBarConfig.showRefresh) {
      buttons.push(
        <Button
          key="refresh"
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
        >
          刷新
        </Button>
      );
    }

    // 导出按钮
    if (toolBarConfig.showExport) {
      buttons.push(
        <Button
          key="export"
          icon={<ExportOutlined />}
          onClick={handleExport}
        >
          导出
        </Button>
      );
    }

    // 新增按钮
    if (toolBarConfig.showAdd) {
      buttons.push(
        <Button
          key="add"
          icon={<PlusOutlined />}
          onClick={handleAdd}
          type="primary"
        >
          {toolBarConfig.addButtonText || '新建'}
        </Button>
      );
    }

    // 移除更多操作下拉菜单

    // 添加额外按钮
    if (toolBarConfig.extraButtons) {
      buttons.push(...toolBarConfig.extraButtons);
    }

    return buttons;
  };

  return (
    <div style={{ padding: 0, background: 'transparent' }} className="table-row-animation">
      <ProTable<T>
        columns={columns}
        actionRef={actionRef}
        cardBordered
        request={request}
        editable={editable}
        columnsState={{
          persistenceKey: `pro-table-${headerTitle}`,
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="key"
        search={search}
        options={{
          reload: false,    // 隐藏刷新按钮
          density: false,   // 隐藏密度调整按钮
          setting: true,    // 显示列设置按钮
        }}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return values;
            }
            return values;
          },
        }}
        pagination={pagination}
        dateFormatter="string"
        headerTitle={
          <Input.Search
            placeholder="搜索日志..."
            allowClear
            style={{ width: 300 }}
            onSearch={() => {
              // 触发搜索
              actionRef.current?.reload();
            }}
          />
        }
        toolBarRender={() => buildToolBarRender()}
        rowSelection={rowSelection}
        size={tableConfig.size}
        bordered={tableConfig.bordered}
        scroll={tableConfig.scroll}
      />
    </div>
  );
};

export default LogsProTable;
