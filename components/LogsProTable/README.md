# LogsProTable 组件

基于 Ant Design Pro Table 封装的统一日志表格组件，用于登录日志、操作日志、系统日志等页面。

## 特性

- 🚀 基于 ProTable，功能强大
- 🎨 统一的视觉风格和交互体验
- 🔍 内置搜索、筛选、排序功能
- 📊 支持多种数据类型和渲染方式
- 📱 响应式设计，支持移动端
- 🌙 支持暗色主题
- ⚡ TypeScript 支持，类型安全

## 基本用法

```tsx
import LogsProTable from '@components/LogsProTable';
import type { ProColumns } from '@ant-design/pro-components';

interface LogItem {
  key: number;
  username: string;
  ip: string;
  time: string;
  status: string;
}

const columns: ProColumns<LogItem>[] = [
  {
    title: '用户名',
    dataIndex: 'username',
    copyable: true,
  },
  {
    title: '登录IP',
    dataIndex: 'ip',
    copyable: true,
  },
  // ... 更多列配置
];

const MyLogsPage = () => {
  const handleRequest = async (params: any) => {
    // 处理数据请求
    const response = await fetchLogs(params);
    return {
      data: response.data,
      success: true,
      total: response.total,
    };
  };

  return (
    <LogsProTable<LogItem>
      headerTitle="登录日志"
      columns={columns}
      request={handleRequest}
      toolBarConfig={{
        showRefresh: true,
        showExport: true,
        showAdd: false,
      }}
    />
  );
};
```

## API

### LogsProTableProps

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| headerTitle | 表格标题 | `string` | - |
| columns | 表格列配置 | `ProColumns<T>[]` | - |
| request | 数据请求函数 | `(params, sort, filter) => Promise<{data: T[], success?: boolean, total?: number}>` | - |
| search | 搜索配置 | `{labelWidth?: number \| 'auto', placeholder?: string}` | `{labelWidth: 'auto'}` |
| pagination | 分页配置 | `{pageSize?: number, showQuickJumper?: boolean, showSizeChanger?: boolean}` | `{pageSize: 10, showQuickJumper: true, showSizeChanger: true}` |
| toolBarConfig | 工具栏配置 | `ToolBarConfig` | `{showRefresh: true, showExport: true, showAdd: false}` |
| tableConfig | 表格配置 | `TableConfig` | `{size: 'small', bordered: true, scroll: {x: 1000, y: 500}}` |
| onRefresh | 刷新回调 | `() => void` | - |
| onExport | 导出回调 | `() => void` | - |
| onAdd | 新增回调 | `() => void` | - |

## 注意事项

1. 确保已安装 `@ant-design/pro-components` 依赖
2. 数据请求函数必须返回指定格式的数据
3. 列配置遵循 ProTable 的 ProColumns 规范
