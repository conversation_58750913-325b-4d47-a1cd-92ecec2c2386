import React, { Suspense } from 'react';
import { Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { RouteConfig } from '../../types';

interface RouteRendererProps {
  routes: RouteConfig[];
  fallback?: React.ReactNode;
  notFoundElement?: React.ReactNode;
}

/**
 * 路由渲染组件
 * 递归渲染嵌套路由结构
 */
const RouteRenderer: React.FC<RouteRendererProps> = ({
  routes,
  fallback = <Spin size="large" style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }} />,
  notFoundElement = (
    <div style={{
      padding: 48,
      textAlign: 'center',
      background: '#fff',
      borderRadius: 8,
      boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
    }}>
      <h1 style={{ fontSize: 48, color: '#1890ff', margin: '0 0 16px 0' }}>404</h1>
      <h2 style={{ fontSize: 24, margin: '0 0 16px 0' }}>页面未找到</h2>
      <p style={{ color: '#8c8c8c' }}>抱歉，您访问的页面不存在</p>
    </div>
  )
}) => {
  const location = useLocation();
  /**
   * 渲染单个路由
   */
  const renderRoute = (route: RouteConfig) => {
    // 处理重定向
    if (route.redirect) {
      return (
        <Route
          key={route.path}
          path={route.path}
          element={<Navigate to={route.redirect} replace />}
        />
      );
    }

    // 处理叶子路由（有element的路由）
    if (route.element) {
      // 生成组件key，包含路径和刷新状态
      const componentKey = `${route.path}-${location.state?.refresh ? location.state.timestamp : 'default'}`;

      return (
        <Route
          key={route.path}
          path={route.path}
          element={
            <Suspense fallback={fallback}>
              <route.element key={componentKey} />
            </Suspense>
          }
        />
      );
    }

    return null;
  };

  return (
    <Routes>
      {routes.map(route => renderRoute(route))}
      
      {/* 404 路由 */}
      <Route path="*" element={notFoundElement} />
    </Routes>
  );
};

export default RouteRenderer;
