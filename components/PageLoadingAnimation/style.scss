// 页面载入动画样式 - Safari 浏览器兼容性优化
.page-loading-animation {
  // 标准动画属性
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;

  // Safari 兼容性前缀
  -webkit-animation: fadeInUp 0.6s ease-out forwards;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;

  // 强制硬件加速，提升 Safari 性能
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  will-change: transform, opacity;

  // Safari 动画准备状态
  &.animation-preparing {
    animation: none !important;
    -webkit-animation: none !important;
    opacity: 0 !important;
    transform: translateY(30px) !important;
    -webkit-transform: translateY(30px) translateZ(0) !important;
  }

  // 防止动画重复执行
  &.animation-disabled {
    animation: none !important;
    -webkit-animation: none !important;
    opacity: 0 !important;
  }
}

// fadeInUp 动画关键帧 - Safari 浏览器兼容性优化
@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
    -webkit-transform: translateY(30px) translateZ(0);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
    -webkit-transform: translateY(0) translateZ(0);
  }
}

// Safari WebKit 前缀版本
@-webkit-keyframes fadeInUp {
  0% {
    opacity: 0;
    -webkit-transform: translateY(30px) translateZ(0);
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    -webkit-transform: translateY(0) translateZ(0);
    transform: translateY(0);
  }
}

// 分阶段动画延迟类 - Safari 兼容性优化
.page-loading-animation {
  &.delay-0 {
    animation-delay: 0s;
    -webkit-animation-delay: 0s;
  }
  &.delay-1 {
    animation-delay: 0.1s;
    -webkit-animation-delay: 0.1s;
  }
  &.delay-2 {
    animation-delay: 0.2s;
    -webkit-animation-delay: 0.2s;
  }
  &.delay-3 {
    animation-delay: 0.3s;
    -webkit-animation-delay: 0.3s;
  }
  &.delay-4 {
    animation-delay: 0.4s;
    -webkit-animation-delay: 0.4s;
  }
  &.delay-5 {
    animation-delay: 0.5s;
    -webkit-animation-delay: 0.5s;
  }
  &.delay-6 {
    animation-delay: 0.6s;
    -webkit-animation-delay: 0.6s;
  }
  &.delay-7 {
    animation-delay: 0.7s;
    -webkit-animation-delay: 0.7s;
  }
}

// 容器动画 - 用于整个页面（仅在有多个子元素时使用）
.page-container-animation {
  // 只有当容器内有多个 PageLoadingAnimation 组件时才应用延迟
  &.multi-children {
    .page-loading-animation {
      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.1s; }
      &:nth-child(3) { animation-delay: 0.2s; }
      &:nth-child(4) { animation-delay: 0.3s; }
      &:nth-child(5) { animation-delay: 0.4s; }
      &:nth-child(6) { animation-delay: 0.5s; }
      &:nth-child(7) { animation-delay: 0.6s; }
      &:nth-child(8) { animation-delay: 0.7s; }
    }
  }
}

// 表格固定高度样式（保留）
.table-fixed-height {
  // 固定高度表格样式
  .ant-table-body {
    min-height: var(--scroll-height);
  }

  // 带边框表格的边框修复
  .ant-table-bordered {
    .ant-table-body {
      border-inline-end: 1px solid #d9d9d9;
      border-block-end: 1px solid #d9d9d9;
    }
  }
}

// 卡片网格动画 - Safari 兼容性优化
.card-grid-animation {
  .ant-col {
    .ant-card {
      animation: fadeInUp 0.6s ease-out forwards;
      -webkit-animation: fadeInUp 0.6s ease-out forwards;
      opacity: 0;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }

    @for $i from 1 through 12 {
      &:nth-child(#{$i}) .ant-card {
        animation-delay: #{($i - 1) * 0.1}s;
        -webkit-animation-delay: #{($i - 1) * 0.1}s;
      }
    }
  }
}

// Safari 浏览器特定优化
@supports (-webkit-appearance: none) {
  .page-loading-animation {
    // Safari 特定的动画优化
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    perspective: 1000px;

    // 强制重绘，解决 Safari 动画问题
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 1px;
      height: 1px;
      background: transparent;
      z-index: -1;
    }
  }
}
