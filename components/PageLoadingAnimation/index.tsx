import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import './style.scss';

interface PageLoadingAnimationProps {
  children: React.ReactNode;
  className?: string;
  delay?: number; // 动画延迟时间（秒）
}

/**
 * 页面载入动画组件
 * 基于 Dashboard 页面的 fadeInUp 动画效果
 * 简化版本，确保稳定性
 */
const PageLoadingAnimation: React.FC<PageLoadingAnimationProps> = ({
  children,
  className = '',
  delay = 0
}) => {
  const location = useLocation();
  const [animationKey, setAnimationKey] = useState(0);

  // 监听路由变化，重置动画
  useEffect(() => {
    setAnimationKey(prev => prev + 1);
  }, [location.pathname]);

  // 检测浏览器类型（简化版本）
  const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

  // 动画样式
  const animationStyle: React.CSSProperties = {
    animationDelay: `${delay}s`,
    ...(isSafari && {
      WebkitAnimationDelay: `${delay}s`,
      WebkitAnimationFillMode: 'forwards',
      WebkitTransform: 'translateZ(0)',
      willChange: 'transform, opacity',
    })
  };

  return (
    <div
      key={`page-animation-${animationKey}`}
      className={`page-loading-animation ${className}`}
      style={animationStyle}
    >
      {children}
    </div>
  );
};

export default PageLoadingAnimation;
