@use '../../styles/variables' as *;

/**
 * 分类管理模态框样式
 */
.category-management-modal {
  .ant-modal-content {
    border-radius: $border-radius-lg;
  }

  .ant-modal-header {
    border-bottom: 1px solid var(--theme-border-color-split);
    padding: 16px;
    transition: border-color 0.2s ease;
  }

  .ant-modal-body {
    padding: 16px;
  }

  .category-form {
    margin-bottom: 16px;

    .ant-form-item {
      margin-bottom: 16px;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: $spacing-md;
    }
  }

  .category-list {
    .ant-list-item {
      padding: $spacing-md;
      border: 1px solid var(--theme-border-color-split);
      border-radius: $border-radius-base;
      margin-bottom: $spacing-md;
      transition: background-color 0.2s ease, border-color 0.2s ease;

      &:hover {
        background-color: var(--theme-bg-tertiary);
      }

      .category-info {
        flex: 1;

        .category-name {
          font-weight: $font-weight-medium;
          color: var(--theme-text-primary);
          margin-bottom: $spacing-md;
          transition: color 0.2s ease;
        }

        .category-description {
          color: var(--theme-text-secondary);
          font-size: $font-size-sm;
          transition: color 0.2s ease;
        }
      }

      .category-actions {
        display: flex;
        gap: $spacing-md;
      }
    }
  }

  .empty-state {
    text-align: center;
    padding: 16px;
    color: var(--theme-text-tertiary);
    transition: color 0.2s ease;
  }
}
