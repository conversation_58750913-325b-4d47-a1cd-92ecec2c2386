import React, { useState, useEffect } from "react";
import {
  Modal,
  Table,
  Button,
  Form,
  Input,
  Space,
  message,
  Popconfirm,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
} from "@ant-design/icons";
import type { AssistantCategory } from "../../types/assistant";
import type { ColumnsType } from "antd/es/table";
import "./style.scss";

const { TextArea } = Input;

interface CategoryManagementModalProps {
  visible: boolean;
  categories: AssistantCategory[];
  onCancel: () => void;
  onSuccess: (categories: AssistantCategory[]) => void;
}

// interface CategoryFormData {
//   key: string;
//   label: string;
//   description: string;
// }

const CategoryManagementModal: React.FC<CategoryManagementModalProps> = ({
  visible,
  categories,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [localCategories, setLocalCategories] = useState<AssistantCategory[]>([]);
  const [editingCategory, setEditingCategory] = useState<AssistantCategory | null>(null);
  const [formVisible, setFormVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      setLocalCategories([...categories]);
    }
  }, [visible, categories]);

  const handleAdd = () => {
    setEditingCategory(null);
    form.resetFields();
    setFormVisible(true);
  };

  const handleEdit = (category: AssistantCategory) => {
    setEditingCategory(category);
    form.setFieldsValue(category);
    setFormVisible(true);
  };

  const handleDelete = (categoryKey: string) => {
    const newCategories = localCategories.filter(c => c.key !== categoryKey);
    setLocalCategories(newCategories);
    message.success("删除成功");
  };

  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingCategory) {
        // 编辑模式
        const newCategories = localCategories.map(c => 
          c.key === editingCategory.key ? { ...values } : c
        );
        setLocalCategories(newCategories);
        message.success("更新成功");
      } else {
        // 新增模式
        // 检查key是否已存在
        if (localCategories.some(c => c.key === values.key)) {
          message.error("分类标识已存在");
          return;
        }
        
        const newCategories = [...localCategories, values];
        setLocalCategories(newCategories);
        message.success("添加成功");
      }
      
      setFormVisible(false);
      form.resetFields();
      setEditingCategory(null);
    } catch (error) {
      console.error("表单验证失败:", error);
    }
  };

  const handleSave = () => {
    onSuccess(localCategories);
  };

  const columns: ColumnsType<AssistantCategory> = [
    {
      title: "分类标识",
      dataIndex: "key",
      key: "key",
      width: 120,
    },
    {
      title: "分类名称",
      dataIndex: "label",
      key: "label",
      width: 150,
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      ellipsis: true,
    },
    {
      title: "操作",
      key: "action",
      width: 120,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个分类吗？"
            onConfirm={() => handleDelete(record.key)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Modal
        title="分类管理"
        open={visible}
        onCancel={onCancel}
        onOk={handleSave}
        width={800}
        okText="保存"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加分类
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={localCategories}
          rowKey="key"
          pagination={false}
          size="small"
          scroll={{ y: 400 }}
        />
      </Modal>

      {/* 分类表单模态框 */}
      <Modal
        title={editingCategory ? "编辑分类" : "添加分类"}
        open={formVisible}
        onCancel={() => {
          setFormVisible(false);
          form.resetFields();
          setEditingCategory(null);
        }}
        onOk={handleFormSubmit}
        okText="确定"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            key: "",
            label: "",
            description: "",
          }}
        >
          <Form.Item
            label="分类标识"
            name="key"
            rules={[
              { required: true, message: "请输入分类标识" },
              { pattern: /^[a-z_]+$/, message: "只能包含小写字母和下划线" },
            ]}
          >
            <Input
              placeholder="请输入分类标识，如：general, writing"
              disabled={!!editingCategory}
            />
          </Form.Item>

          <Form.Item
            label="分类名称"
            name="label"
            rules={[{ required: true, message: "请输入分类名称" }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            label="描述"
            name="description"
            rules={[{ required: true, message: "请输入描述" }]}
          >
            <TextArea
              rows={3}
              placeholder="请输入分类描述"
              showCount
              maxLength={100}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default CategoryManagementModal;
