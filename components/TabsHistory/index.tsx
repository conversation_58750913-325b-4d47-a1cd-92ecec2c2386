import React, { useState, useRef, useEffect, useCallback } from 'react';
import { 
  Dropdown, 
  Button
} from 'antd';
import {
  CloseOutlined,
  ReloadOutlined,
  PushpinOutlined,
  LeftOutlined,
  RightOutlined,
  MoreOutlined,
  LoadingOutlined,
  DeleteOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTabsStore } from '../../stores';
import { renderIcon } from '../../utils/pageInfo';
import type { TabRecord } from '../../stores/types';
import './style.scss';

/**
 * 标签页历史记录组件属性
 */
interface TabsHistoryProps {
  className?: string;
  // onTabChange?: (key: string) => void; // 未使用
}

/**
 * 标签页历史记录组件
 */
const TabsHistory: React.FC<TabsHistoryProps> = React.memo(({ className = '' /*, onTabChange*/ }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    tabs,
    isLoading,
    removeTab,
    clearOtherTabs,
    clearAllTabs,
    pinTab,
    refreshTab,
    getActiveTab
  } = useTabsStore();

  // 获取当前活动标签
  const activeTab = getActiveTab(location.pathname);

  // 调试信息
  console.log('📊 TabsHistory render - tabs count:', tabs.length, 'activeTab:', activeTab?.title);
  
  const [scrollLeft, setScrollLeft] = useState(0);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  // 检查滚动状态
  const checkScrollStatus = () => {
    if (tabsContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = tabsContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
      setScrollLeft(scrollLeft);
    }
  };

  // 滚动到指定位置
  const scrollTo = (direction: 'left' | 'right') => {
    if (tabsContainerRef.current) {
      const scrollAmount = 200;
      const newScrollLeft = direction === 'left' 
        ? Math.max(0, scrollLeft - scrollAmount)
        : scrollLeft + scrollAmount;
      
      tabsContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      });
    }
  };

  // 处理标签点击
  const handleTabClick = useCallback((tab: TabRecord) => {
    if (tab.path !== location.pathname) {
      navigate(tab.path);
    }
  }, [location.pathname, navigate]);

  // 处理标签关闭
  const handleTabClose = useCallback((e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    e.preventDefault();

    const tab = tabs.find(t => t.id === tabId);
    if (!tab || tab.pinned) {
      return;
    }

    // 如果关闭的是当前标签，需要先跳转再关闭
    if (tab.path === location.pathname) {
      const remainingTabs = tabs.filter(t => t.id !== tabId);
      if (remainingTabs.length > 0) {
        // 跳转到最后一个标签（最近访问的）
        const lastTab = remainingTabs[remainingTabs.length - 1];
        navigate(lastTab.path);
      } else {
        // 如果没有其他标签，跳转到主页
        navigate('/dashboard');
      }
    }

    // 延迟移除标签，确保导航完成
    setTimeout(() => {
      removeTab(tabId);
    }, 10);
  }, [tabs, location.pathname, removeTab, navigate]);

  // 右键菜单
  const getContextMenuItems = (tab: TabRecord) => [
    {
      key: 'refresh',
      icon: <ReloadOutlined />,
      label: '刷新页面',
      onClick: () => {
        console.log('🔄 Context menu refresh clicked for tab:', tab.id);
        refreshTab(tab.id, navigate, location.pathname);
      },
    },
    {
      key: 'pin',
      icon: <PushpinOutlined />,
      label: tab.pinned ? '取消固定' : '固定标签',
      onClick: () => pinTab(tab.id, !tab.pinned),
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'close-others',
      icon: <CloseCircleOutlined />,
      label: '关闭其他',
      onClick: () => {
        console.log('🗑️ Context menu close others clicked for tab:', tab.id);
        clearOtherTabs(tab.id, navigate);
      },
    },
    {
      key: 'close-all',
      icon: <DeleteOutlined />,
      label: '关闭所有',
      onClick: () => clearAllTabs(navigate),
    },
  ];

  // 监听滚动事件
  useEffect(() => {
    const container = tabsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollStatus);
      // 初始检查
      const timer = setTimeout(checkScrollStatus, 100);

      return () => {
        container.removeEventListener('scroll', checkScrollStatus);
        clearTimeout(timer);
      };
    }
  }, [tabs.length]); // 只在标签数量变化时重新绑定

  // 当活动标签变化时，滚动到活动标签
  useEffect(() => {
    if (activeTab && tabsContainerRef.current) {
      const timer = setTimeout(() => {
        const activeTabElement = tabsContainerRef.current?.querySelector(`[data-tab-id="${activeTab.id}"]`);
        if (activeTabElement) {
          activeTabElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'center'
          });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [activeTab?.id]); // 只在活动标签ID变化时执行

  if (tabs.length === 0) {
    return null;
  }

  return (
    <div className={`tabs-history ${className}`}>
      {/* 左滚动按钮 */}
      {canScrollLeft && (
        <Button
          type="text"
          size="small"
          icon={<LeftOutlined />}
          className="scroll-button scroll-left"
          onClick={() => scrollTo('left')}
        />
      )}

      {/* 标签页容器 */}
      <div 
        ref={tabsContainerRef}
        className="tabs-container"
        onScroll={checkScrollStatus}
      >
        <div className="tabs-wrapper">
          {tabs.map((tab) => (
            <Dropdown
              key={tab.id}
              menu={{ items: getContextMenuItems(tab) }}
              trigger={['contextMenu']}
            >
              <div
                data-tab-id={tab.id}
                className={`tab-item ${tab.path === location.pathname ? 'active' : ''} ${tab.pinned ? 'pinned' : ''}`}
                onClick={() => handleTabClick(tab)}
              >
                <div className="tab-content">
                  {tab.loading ? (
                    <LoadingOutlined className="tab-icon" spin />
                  ) : (
                    tab.icon && <span className="tab-icon">{renderIcon(tab.icon)}</span>
                  )}
                  <span className="tab-title">{tab.title}</span>
                  {tab.pinned && <PushpinOutlined className="pin-icon" />}
                </div>
                
                {!tab.pinned && tabs.length > 1 && (
                  <CloseOutlined 
                    className="close-icon"
                    onClick={(e) => handleTabClose(e, tab.id)}
                  />
                )}
              </div>
            </Dropdown>
          ))}
        </div>
      </div>

      {/* 右滚动按钮 */}
      {canScrollRight && (
        <Button
          type="text"
          size="small"
          icon={<RightOutlined />}
          className="scroll-button scroll-right"
          onClick={() => scrollTo('right')}
        />
      )}

      {/* 更多操作按钮 */}
      <Dropdown
        menu={{
          items: [
            {
              key: 'refresh-current',
              icon: <ReloadOutlined />,
              label: '刷新当前',
              onClick: () => {
                console.log('🔄 More menu refresh current clicked, activeTab:', activeTab);
                if (activeTab) {
                  refreshTab(activeTab.id, navigate, location.pathname);
                } else {
                  console.warn('No active tab found for refresh');
                }
              },
            },
            {
              type: 'divider' as const,
            },
            {
              key: 'close-others',
              icon: <CloseCircleOutlined />,
              label: '关闭其他',
              onClick: () => {
                console.log('🗑️ More menu close others clicked, activeTab:', activeTab);
                if (activeTab) {
                  clearOtherTabs(activeTab.id, navigate);
                } else {
                  console.warn('No active tab found for close others');
                }
              },
            },
            {
              key: 'close-all',
              icon: <DeleteOutlined />,
              label: '关闭所有',
              onClick: () => clearAllTabs(navigate),
            },
          ]
        }}
        trigger={['click']}
      >
        <Button
          type="text"
          size="small"
          icon={<MoreOutlined />}
          className="more-button"
        />
      </Dropdown>
    </div>
  );
});

TabsHistory.displayName = 'TabsHistory';

export default TabsHistory;
