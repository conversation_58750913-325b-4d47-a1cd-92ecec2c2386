import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { App as AntApp, ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { StagewiseToolbar } from '@stagewise/toolbar-react'
import { ReactPlugin } from '@stagewise-plugins/react'
import Login from '../pages/Login'
import ProtectedRoute from '../components/ProtectedRoute'
import V2Routes from '../routes'
import RouteStateManager from '../components/RouteStateManager'
import StoreInitializer from '../components/StoreInitializer'
import ThemeProvider from '../components/ThemeProvider'

import { useMessage } from '../utils/messageUtils'
import '../styles/theme.scss'


/**
 * App内容组件 - 处理消息实例初始化
 */
const AppContent: React.FC = () => {
  // 初始化全局消息实例
  useMessage();

  return (
    <div className="v2-admin-app">
      <Routes>
        {/* 登录路由 - 不需要认证 */}
        <Route path="/login" element={<Login />} />

        {/* 受保护的管理路由 - 使用独立路由配置 */}
        <Route path="/*" element={
          <ProtectedRoute requireAdmin={true}>
            <StoreInitializer>
              <RouteStateManager>
                <V2Routes />
              </RouteStateManager>
            </StoreInitializer>
          </ProtectedRoute>
        } />

        {/* 未匹配路由重定向到登录页 */}
        <Route path="*" element={<Navigate to="/login" replace />} />
      </Routes>
      
      {/* Stagewise 工具栏 - 仅在开发环境中显示 */}
      <StagewiseToolbar
        config={{
          plugins: [ReactPlugin],
        }}
      />
    </div>
  );
};

/**
 * V2 Admin 主应用组件
 * 独立运行的管理界面应用，集成登录认证功能
 */
const App: React.FC = () => {
  return (
    <ThemeProvider>
      <ConfigProvider locale={zhCN}>
        <AntApp>
          <AppContent />
        </AntApp>
      </ConfigProvider>
    </ThemeProvider>
  )
}

export default App
